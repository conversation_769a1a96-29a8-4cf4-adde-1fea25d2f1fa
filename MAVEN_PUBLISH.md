# 发布到 Maven Central 指南

本文档说明如何将 agent-base 和 agent-sdk 库发布到 Maven Central 仓库。

## 前提条件

1. 在 [Sonatype OSSRH](https://s01.oss.sonatype.org/) 注册账号
2. 配置 GPG 密钥以签名你的构件
3. 为你的 GitHub 用户名/组织名申请 Maven Central 的命名空间
4. 确保你的 Android 项目能正常构建

## 配置步骤

### 1. 设置 GPG 密钥

如果你还没有 GPG 密钥，需要先创建：

```bash
# 生成密钥
gpg --gen-key

# 查看生成的密钥 ID
gpg --list-keys --keyid-format SHORT

# 导出私钥文件用于签名
gpg --export-secret-keys -o ~/.gnupg/secring.gpg

# 发布公钥
gpg --keyserver keyserver.ubuntu.com --send-keys 40108739F913E5B23D4A9E1469577CE7XXXXXXXX
```

### 2. 配置发布凭据

复制 `local.properties.template` 到 `local.properties`（该文件已在 .gitignore 中），并填写你的实际信息：

```properties
# Sonatype 账号信息
ossrhUsername=your_sonatype_username
ossrhPassword=your_sonatype_password

# GPG 签名信息
signing.keyId=GPGKEYID_LAST_8_CHARS
signing.password=your_gpg_passphrase
signing.secretKeyRingFile=/path/to/your/secring.gpg
```

### 3. 调整 Maven 组 ID

在 `agent-base/build.gradle.kts` 和 `agent-sdk/build.gradle.kts` 文件中，确保 `groupId` 符合你的实际组织：

```kotlin
configureMavenPublish(
    artifactId = "agent-base", // 或 agent-sdk
    groupId = "io.github.yourusername", // 替换为你的组 ID
    version = baseVersion,
    // ...
)
```

### 4. 修改 POM 信息

同样在这些文件中，确保项目元数据正确：

- 项目名称和描述
- 开发者信息
- SCM 信息（版本控制系统链接）
- 许可证信息

## 发布流程

### 1. 发布 agent-base 库

```bash
# 清理并构建 base 库
./gradlew :agent-base:clean :agent-base:build

# 发布到 Maven Central
./gradlew :agent-base:publishReleasePublicationToSonatypeRepository
```

### 2. 发布 agent-sdk 库

```bash
# 清理并构建 SDK 库
./gradlew :agent-sdk:clean :agent-sdk:build

# 发布到 Maven Central
./gradlew :agent-sdk:publishReleasePublicationToSonatypeRepository
```

### 3. 关闭和发布 Maven Central 上的构件

完成以上步骤后，登录 [Sonatype OSSRH](https://s01.oss.sonatype.org/) 并执行以下操作：

1. 在 "Staging Repositories" 中找到你的构件
2. 选择你的仓库并点击 "Close"
3. 验证通过后，点击 "Release"
4. 构件将在几个小时内同步到 Maven Central（通常 1-2 小时）

## 在项目中使用发布的库

一旦你的库在 Maven Central 上发布成功，其他开发者可以这样使用它们：

```kotlin
// 在 build.gradle.kts
dependencies {
    implementation("io.github.orionagent:agent-base:0.0.1")
    implementation("io.github.orionagent:agent-sdk:0.0.1")
}
```

## 常见问题

### 签名失败

确保 GPG 密钥路径正确，并且你有权限访问该文件。

### 发布被拒绝

检查 Sonatype 网站上的仓库，查看具体的拒绝原因，通常是因为元数据不完整或格式不正确。

### 版本号冲突

确保每次发布都使用新的版本号，一旦发布到 Maven Central，该版本就不能再次发布或修改。 