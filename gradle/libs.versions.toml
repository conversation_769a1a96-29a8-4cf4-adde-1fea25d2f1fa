[versions]
agentBase = "0.2.8-SNAPSHOT"
agp = "8.5.1"
awebp = "3.0.1"
coil = "2.7.0"
eventbus = "3.3.1"
gson = "2.11.0"
kotlin = "1.9.0"
coreKtx = "1.13.1"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.0"
ktor = "3.1.3"
livekitAndroid = "2.7.2"
material = "1.12.0"
activity = "1.9.1"
constraintlayout = "2.1.4"
mmkv = "1.3.9"
xlog = "1.11.1"
easyfloat = "2.0.4"
fastjson = "1.2.62"
supportAnnotations = "28.0.0"

[libraries]
agent-base = { module = "com.orionstar.agent:base", version.ref = "agentBase" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
awebp = { module = "com.github.penfeizhou.android.animation:awebp", version.ref = "awebp" }
coil = { module = "io.coil-kt:coil", version.ref = "coil" }
eventbus = { module = "org.greenrobot:eventbus", version.ref = "eventbus" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
ktor-client-logging = { module = "io.ktor:ktor-client-logging", version.ref = "ktor" }
ktor-client-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "ktor" }
ktor-client-serialization = { module = "io.ktor:ktor-client-serialization", version.ref = "ktor" }
ktor-serialization-gson = { module = "io.ktor:ktor-serialization-gson", version.ref = "ktor" }
livekit-android = { module = "io.livekit:livekit-android", version.ref = "livekitAndroid" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
mmkv = { module = "com.tencent:mmkv", version.ref = "mmkv" }
xlog = { module = "com.elvishew:xlog", version.ref = "xlog" }
easyfloat = { group = "com.github.princekin-f", name = "EasyFloat", version.ref = "easyfloat" }
fastjson = { module = "com.alibaba:fastjson", version.ref = "fastjson" }
support-annotations = { group = "com.android.support", name = "support-annotations", version.ref = "supportAnnotations" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
android-library = { id = "com.android.library", version.ref = "agp" }


