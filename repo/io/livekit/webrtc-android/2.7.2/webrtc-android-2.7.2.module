{"formatVersion": "1.1", "component": {"group": "io.livekit", "module": "webrtc-android", "version": "2.7.2", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.7"}}, "variants": [{"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8", "version": {"requires": "1.9.0"}}], "files": [{"name": "webrtc-android-2.7.2.aar", "url": "webrtc-android-2.7.2.aar", "size": 20259788, "sha512": "87daa0194294da661de17f6c0fe5c15a2ed588d4045bf14ab110aeec60bb44f61263272e6400f250562aac71a4719b9a60eb4d2fd212b8478cbbec1899c83fa4", "sha256": "f4d8332ac2d9cba07389f65c27840aad54f3d830b5f0206f5157807982c2cd59", "sha1": "e0ff50fa6094307010f733188ed46f826bd15276", "md5": "09f995eadb95c7d95f3ebf3e2e232989"}]}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.core", "module": "core-ktx", "version": {"requires": "1.13.1"}}, {"group": "androidx.appcompat", "module": "appcompat", "version": {"requires": "1.7.0"}}, {"group": "com.google.android.material", "module": "material", "version": {"requires": "1.12.0"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8", "version": {"requires": "1.9.0"}}], "files": [{"name": "webrtc-android-2.7.2.aar", "url": "webrtc-android-2.7.2.aar", "size": 20259788, "sha512": "87daa0194294da661de17f6c0fe5c15a2ed588d4045bf14ab110aeec60bb44f61263272e6400f250562aac71a4719b9a60eb4d2fd212b8478cbbec1899c83fa4", "sha256": "f4d8332ac2d9cba07389f65c27840aad54f3d830b5f0206f5157807982c2cd59", "sha1": "e0ff50fa6094307010f733188ed46f826bd15276", "md5": "09f995eadb95c7d95f3ebf3e2e232989"}]}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "files": [{"name": "webrtc-android-2.7.2-sources.jar", "url": "webrtc-android-2.7.2-sources.jar", "size": 395321, "sha512": "ab272cc24d737ef0b4fa349a5c7f40ac8101796b123de6d09fab34b79841a20319b9f2a89ed4ad6732424f962c176eb8d5122aa26d79b28640b88af5b90a2091", "sha256": "81e6fada35fcb98aabf8034e495cf5c8a1f10c38eda0d5e62055170252996f0f", "sha1": "6a23a2512de1236a6808791f9a1d9daeada70fa1", "md5": "04a6fa3bc23501172ef627f4d17cf060"}]}]}