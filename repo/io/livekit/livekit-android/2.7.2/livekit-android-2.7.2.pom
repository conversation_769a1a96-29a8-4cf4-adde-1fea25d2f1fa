<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>radle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.livekit</groupId>
  <artifactId>livekit-android</artifactId>
  <version>2.7.2</version>
  <packaging>aar</packaging>
  <name>LiveKit Client Android SDK</name>
  <description>LiveKit Android SDK, WebRTC Rooms</description>
  <url>https://github.com/livekit/client-sdk-android</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>livekit</id>
      <name>LiveKit</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/livekit/client-sdk-android.git</connection>
    <developerConnection>scm:git:ssh://**************/livekit/client-sdk-android.git</developerConnection>
    <url>https://github.com/livekit/client-sdk-android</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>4.12.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.livekit</groupId>
      <artifactId>webrtc-android</artifactId>
      <version>2.7.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.github.davidliu</groupId>
      <artifactId>audioswitch</artifactId>
      <version>89582c47c9a04c62f90aa5e57251af4800a62c9a</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib-jdk7</artifactId>
      <version>1.8.20</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlinx</groupId>
      <artifactId>kotlinx-coroutines-android</artifactId>
      <version>1.6.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlinx</groupId>
      <artifactId>kotlinx-serialization-json</artifactId>
      <version>1.5.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.annotation</groupId>
      <artifactId>annotation</artifactId>
      <version>1.7.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.core</groupId>
      <artifactId>core</artifactId>
      <version>1.13.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-javalite</artifactId>
      <version>3.22.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>javax.sip</groupId>
      <artifactId>android-jain-sip-ri</artifactId>
      <version>1.3.0-91</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.dagger</groupId>
      <artifactId>dagger</artifactId>
      <version>2.46</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.github.ajalt</groupId>
      <artifactId>timberkt</artifactId>
      <version>1.5.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.vdurmont</groupId>
      <artifactId>semver4j</artifactId>
      <version>3.1.0</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>
