{"formatVersion": "1.1", "component": {"group": "io.livekit", "module": "livekit-android", "version": "2.7.2", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.2"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.squareup.okhttp3", "module": "okhttp", "version": {"requires": "4.12.0"}}, {"group": "io.livekit", "module": "webrtc-android", "version": {"requires": "2.7.2"}}, {"group": "com.github.davidliu", "module": "audioswitch", "version": {"requires": "89582c47c9a04c62f90aa5e57251af4800a62c9a"}}], "files": [{"name": "livekit-android-2.7.2.aar", "url": "livekit-android-2.7.2.aar", "size": 2406659, "sha512": "3c5cc6ac32f52aac8d433d54f1023e660db68ce06edb79bd97d56725c0b72bf1c37dd7e34da1bbe1cd3ceaf67f5a2eb77290f45b4ceb1b089fdc26a0e602b9e9", "sha256": "fdec06d006ccebfce36b84424187877e696bd74fed936dd0f918186aa1e3c298", "sha1": "a71d5f0225b824dc3a65b19045424c538d7590a6", "md5": "5a07e5bd894143d9c714f168446905ec"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "1.8.20"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-android", "version": {"requires": "1.6.0"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-serialization-json", "version": {"requires": "1.5.0"}}, {"group": "com.squareup.okhttp3", "module": "okhttp", "version": {"requires": "4.12.0"}}, {"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.7.1"}}, {"group": "androidx.core", "module": "core", "version": {"requires": "1.13.1"}}, {"group": "com.google.protobuf", "module": "protobuf-javalite", "version": {"requires": "3.22.0"}}, {"group": "javax.sip", "module": "android-jain-sip-ri", "version": {"requires": "1.3.0-91"}}, {"group": "com.google.dagger", "module": "dagger", "version": {"requires": "2.46"}}, {"group": "com.github.ajalt", "module": "timberkt", "version": {"requires": "1.5.1"}}, {"group": "com.vdurmont", "module": "semver4j", "version": {"requires": "3.1.0"}}, {"group": "io.livekit", "module": "webrtc-android", "version": {"requires": "2.7.2"}}, {"group": "com.github.davidliu", "module": "audioswitch", "version": {"requires": "89582c47c9a04c62f90aa5e57251af4800a62c9a"}}], "files": [{"name": "livekit-android-2.7.2.aar", "url": "livekit-android-2.7.2.aar", "size": 2406659, "sha512": "3c5cc6ac32f52aac8d433d54f1023e660db68ce06edb79bd97d56725c0b72bf1c37dd7e34da1bbe1cd3ceaf67f5a2eb77290f45b4ceb1b089fdc26a0e602b9e9", "sha256": "fdec06d006ccebfce36b84424187877e696bd74fed936dd0f918186aa1e3c298", "sha1": "a71d5f0225b824dc3a65b19045424c538d7590a6", "md5": "5a07e5bd894143d9c714f168446905ec"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "livekit-android-2.7.2-sources.jar", "url": "livekit-android-2.7.2-sources.jar", "size": 624611, "sha512": "5a69442e16e94200b6e19271f821382182a18b25e427b72175b9929d7d79a206bb0bc03559cb358702c089e6ae968c23afca2789503a8375873087e272cc54c5", "sha256": "e7798414183aeaa69ca0d1036ae016214d601cda8f59a52b9d34de47ed025f02", "sha1": "52419175306ea48526be3a0a6404333a754c8ccd", "md5": "973a693e75dc22c4fbe18f870cd9d253"}]}, {"name": "releaseVariantReleaseJavaDocPublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "livekit-android-2.7.2-javadoc.jar", "url": "livekit-android-2.7.2-javadoc.jar", "size": 4753720, "sha512": "c1421ddce5c72dfe6441c70009e32a88aec083577e0c4585bf87a47370eb0695519b4968b06f83e0e416cf0664da4b19569a1eb91a8193d014833c7fb02da48f", "sha256": "ab67ffe8b5e3bbe2640ee1af02420c76040eb9264a55c91a084e20ca6349d55c", "sha1": "917227ce75bdcebc619676a7ca026eef42af44e3", "md5": "527d9fde5f1f827c2ab4770168279aab"}]}]}