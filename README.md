# 构建版本管理

## 版本号文件

文件路径：`app/version.properties`

### 文件内容示例：
```
productVersionName=0.1.241128.1
versionName=10.0.4
versionCode=100000004
```

### 内容说明：
- **productVersionName**：产品版本号，每次发版时手动更新。
- **versionName**：APK版本号，用`.`分隔为三位：
    1. **首位**：大版本号，重大升级时增加。
    2. **第二位**：feature版本号，每次小版本发布时增加，当首位大版本号增加时，此位清0。
    3. **第三位**：构建版本号，方便测试出问题时从代码提交记录中找到对应的版本，类似tag的作用，每次构建都会自增，当发布大版本或小版本时，此位都清0。
- **versionCode**：APK版本号，与`versionName`同步更新，共9位：
    1. **首三位**：大版本号，作用同上。
    2. **次三位**：feature版本号，作用同上，且当首位大版本号增加时，此位清0。
    3. **最后三位**：构建版本号，自增，作用同上，当发布大版本或小版本时，此位都清0。
  
---

## 代码模块

### 1. 代码提交规范