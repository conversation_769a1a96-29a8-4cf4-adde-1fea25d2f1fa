import java.util.Properties

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
    kotlin("plugin.serialization") version "2.0.0"
}

val regionVersion: String? by project
val buildType: String? by project

val versionProperties = file("version.properties")
val versionProps = Properties().apply {
    load(versionProperties.inputStream())
}

val versionCode2 = versionProps["versionCode"].toString().toInt() + 1
var versionName2 = versionProps["versionName"].toString()
val productVersionName = if (regionVersion == "domestic") {
    versionProps["productVersionNameDomestic"].toString()
} else {
    versionProps["productVersionNameOversea"].toString()
}

val actionVersionDomestic = versionProps["actionVersionDomestic"].toString()
val actionVersionOversea = versionProps["actionVersionOversea"].toString()

val versionNameParts = versionName2.split(".").toMutableList()
versionNameParts[versionNameParts.size - 1] =
    (versionNameParts.last().toInt() + 1).toString()
versionName2 = versionNameParts.joinToString(".")

android {
    namespace = "com.ainirobot.agent"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.ainirobot.agentservice"
        minSdk = 25
        targetSdk = 34
        versionCode = versionCode2
        versionName = versionName2

        buildConfigField("String", "REGION_VERSION", "\"${regionVersion}\"")
        buildConfigField("boolean", "REGION_DOMESTIC", "${regionVersion == "domestic"}")
        buildConfigField("boolean", "REGION_OVERSEA", "${regionVersion == "oversea"}")
        buildConfigField("String", "AGENT_ACTION_VERSION_DOMESTIC", "\"${actionVersionDomestic}\"")
        buildConfigField("String", "AGENT_ACTION_VERSION_OVERSEA", "\"${actionVersionOversea}\"")
        buildConfigField("String", "PRODUCT_VERSION", "\"${productVersionName}\"")
        buildConfigField("boolean", "DEBUG_MODE_ENABLE", "${buildType == "debug"}")

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            // 指定支持的 ABI 架构
            abiFilters.addAll(listOf("armeabi-v7a"))
        }

        manifestPlaceholders["PRODUCT_VERSION"] = productVersionName
    }

    signingConfigs {
        create("robot_debug") {
            keyAlias = "platform"
            keyPassword = "android"
            storeFile = file("debug.keystore")
            storePassword = "android"
        }

        create("robot_release") {
            keyAlias = "AiniBox"
            keyPassword = "AiniRobot@9102"
            storeFile = file("platform.keystore")
            storePassword = "AiniRobot@9102"
        }
    }

    buildFeatures {
        buildConfig = true
        aidl = true
    }

//    flavorDimensions += listOf("version")
//    productFlavors {
//        create("robotDev") {
//            buildConfigField("String", "LIVE_KIT_URL", "\"${agentDevUrl}\"")
//            buildConfigField("String", "AGENT_ENV", "\"DEV\"")
//            buildConfigField("int", "AGENT_ENV_CODE", "1")
//        }
//        create("robotTest") {
//            buildConfigField("String", "LIVE_KIT_URL", "\"${agentTestUrl}\"")
//            buildConfigField("String", "AGENT_ENV", "\"TEST\"")
//            buildConfigField("int", "AGENT_ENV_CODE", "2")
//        }
//        create("robotStable") {
//            buildConfigField("String", "LIVE_KIT_URL", "\"${agentStableUrl}\"")
//            buildConfigField("String", "AGENT_ENV", "\"STABLE\"")
//            buildConfigField("int", "AGENT_ENV_CODE", "0")
//        }
//    }

    buildTypes {
        getByName("release") {
            isMinifyEnabled = false
            signingConfig = signingConfigs.getByName("robot_release")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
        getByName("debug") {
            signingConfig = signingConfigs.getByName("robot_release")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }

    sourceSets {
        getByName("main") {
            // 设置 AIDL 文件路径，假设你的 AIDL 文件存放在 src/main/aidlCustom 目录下 SpeechService-orion/app/src/main/aidl/com/ainirobot/speech/IVoiceCallback.aidl
            aidl.srcDirs("src/main/aidl")
        }
    }
}

dependencies {
    implementation(fileTree(mapOf("include" to listOf("*.jar", "*.aar"), "dir" to "libs")))
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.activity)
    implementation(libs.androidx.constraintlayout)

    implementation(libs.livekit.android)
    implementation(libs.xlog)
    implementation(libs.gson)

    implementation(libs.ktor.client.core)
    implementation(libs.ktor.client.okhttp)
    implementation(libs.ktor.client.serialization)
    implementation(libs.ktor.serialization.gson)
    implementation(libs.ktor.client.content.negotiation)
    implementation(libs.mmkv)
    implementation(libs.ktor.client.logging)
    implementation(libs.awebp)
    implementation(libs.coil)
    implementation(libs.eventbus)
    implementation("com.squareup.retrofit2:retrofit:2.6.0")
    implementation("com.squareup.retrofit2:converter-gson:2.0.2")
    implementation(libs.agent.base)


    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    implementation(libs.easyfloat)
    implementation(libs.fastjson)
    implementation(libs.support.annotations)
}

// 创建版本号自增任务
tasks.register("incrementVersion") {
    doFirst {
        versionProps["versionCode"] = versionCode2.toString()
        versionProps["versionName"] = versionName2
        versionProperties.outputStream().use {
            versionProps.store(it, null)
        }
    }
}

android.applicationVariants.all {
    assembleProvider.configure {
        dependsOn("incrementVersion")
    }
}
