# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-dontwarn org.slf4j.impl.StaticLoggerBinder
-dontwarn com.ainirobot.coreservice.ApplicationWrapper
-dontwarn javax.lang.model.element.Modifier

-keep class com.ainirobot.coreservice.** { *; }

-keep class com.ainirobot.daemon.AgentDaemonService { *; }

# 保留所有 runtime 注解的类
-keep @interface * {
    *;
}

# 保留 runtime 注解的元数据，防止注解属性被移除
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations
-keep,allowobfuscation @com.ainirobot.bridge.executor.Action class *
