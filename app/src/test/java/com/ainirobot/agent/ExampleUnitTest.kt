import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonParseException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.lang.reflect.Type


fun main() {
    runBlocking {
        val scope = CoroutineScope(SupervisorJob())
        scope.launch {	//兄弟协程
            delay(2000)
            println("第一个协程执行完毕")
        }
        scope.launch {	//协程二
            scope.launch {    //第二个协程 的子协程 抛出异常;
                throw AssertionError("The second child is cancelled")
            }
            delay(1000)
            println("第二个协程执行完毕?")	//未打印
        }
        delay(5000)
        scope.launch {
            println("第三个协程执行完毕")
        }
        delay(1000)

        println("全部执行完毕")
    }

}
