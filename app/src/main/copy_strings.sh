#!/bin/bash

# 用法说明
if [ $# -lt 2 ]; then
    echo "用法: $0 <source_project_path> <key1> [key2] [key3] ..."
    echo "示例: $0 /Users/<USER>/AndroidProject cancel ok confirm"
    exit 1
fi

SOURCE_PROJECT="$1"
shift
KEY_NAMES=("$@")

TARGET_PROJECT="$(pwd)/res"

if ! command -v python3 &>/dev/null; then
    echo "❌ Python3未安装，请安装Python3后再运行。"
    exit 1
fi

KEY_LIST=$(printf "'%s'," "${KEY_NAMES[@]}")
KEY_LIST="[${KEY_LIST%,}]"

LANG_DIRS=$(find "$SOURCE_PROJECT/res" -type d -name "values*")

for LANG_DIR in $LANG_DIRS; do
    LANG_DIR_NAME=$(basename "$LANG_DIR")
    SRC_STRINGS="$LANG_DIR/strings.xml"
    DEST_DIR="$TARGET_PROJECT/$LANG_DIR_NAME"
    DEST_STRINGS="$DEST_DIR/strings.xml"

    if [ ! -f "$SRC_STRINGS" ]; then
        continue
    fi

    echo "🔍 处理 $SRC_STRINGS -> $DEST_STRINGS"

    mkdir -p "$DEST_DIR"

    python3 - <<EOF
import xml.etree.ElementTree as ET
import os
import re

src_file = "$SRC_STRINGS"
dst_file = "$DEST_STRINGS"
keys = $KEY_LIST

def read_xml(file):
    try:
        tree = ET.parse(file)
        return tree.getroot()
    except Exception as e:
        print(f"❌ 读取XML失败: {e}")
        return None

src_root = read_xml(src_file)
if src_root is None: exit(1)

key_map = {}
for elem in src_root.findall('string'):
    name = elem.attrib.get('name')
    if name in keys:
        key_map[name] = elem.text

if not key_map:
    print(f"⚠️ 未找到指定key: {keys} in {src_file}")
    exit(0)

if not os.path.exists(dst_file):
    with open(dst_file, 'w', encoding='utf-8') as f:
        f.write('<resources>\n</resources>\n')

with open(dst_file, 'r', encoding='utf-8') as f:
    lines = f.readlines()

existing_keys = set()
pattern = re.compile(r'<string name="(.*?)"')
for line in lines:
    match = pattern.search(line)
    if match:
        existing_keys.add(match.group(1))

insert_lines = []
for key, text in key_map.items():
    if key not in existing_keys:
        insert_lines.append(f'    <string name="{key}">{text}</string>\n')

if insert_lines:
    for i in range(len(lines)):
        if lines[i].strip() == '</resources>':
            lines = lines[:i] + insert_lines + [lines[i]]
            break
    with open(dst_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    print(f"✅ 已更新: {dst_file}")
else:
    print(f"✅ 无需更新: {dst_file}（所有key已存在）")
EOF

done

echo "🎉 所有处理完成。"

