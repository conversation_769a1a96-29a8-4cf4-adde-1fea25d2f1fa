<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.REBOOT"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!-- 这个权限用于进行网络定位-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- 这个权限用于访问系统接口提供的卫星定位信息-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- 用于访问wifi网络信息，wifi信息会用于进行网络定位-->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 获取运营商信息，用于支持提供运营商信息相关的接口-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- 这个权限用于获取wifi的获取权限，wifi信息会用来进行网络定位-->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <!-- 写入扩展存储，向扩展卡写入数据，用于写入离线定位数据-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- 访问网络，网络定位需要上网-->
    <uses-permission android:name="android.permission.INTERNET" />
    <!--接收wakeupId广播权限-->
    <uses-permission android:name="com.ainirobot.permission.WAKEUP_ID_RECEIVER" />

    <permission android:name="com.ainirobot.agentservice.SpokesmanProvider"/>
    <permission android:name="com.ainirobot.agent.permission.AGENT_SERVICE"/>

    <application
        android:name="com.ainirobot.app.MainApp"
        android:allowBackup="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/Theme.AgentService"
        android:largeHeap="true"
        android:hardwareAccelerated="true"
        tools:targetApi="31">

        <!-- 百度定位相关 start -->
        <meta-data
            android:name="com.baidu.lbsapi.API_KEY"
            android:value="xlYVLl1odsDdS80VZ50QcPGkbGLSTENf" />
        
        <!-- 添加产品版本号 -->
        <meta-data
            android:name="product_version"
            android:value="${PRODUCT_VERSION}" />

        <service android:name="com.baidu.location.f" android:enabled="true" android:process=":remote"> </service>

        <activity
            android:name="com.ainirobot.app.MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <service android:name=".service.AgentService"/>
        <service
            android:name="com.ainirobot.agentservice.SpeechService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.ainirobot.robotos.SERVICE" />
            </intent-filter>
        </service>

        <provider
            android:name="com.ainirobot.speechasrservice.data.spokesman.SpokesmanProvider"
            android:authorities="com.ainirobot.agentservice.SpokesmanProvider"
            android:exported="true"
            android:permission="com.ainirobot.agentservice.SpokesmanProvider"/>

        <provider
            android:name="com.ainirobot.speechasrservice.data.DBProvider"
            android:authorities="com.ainirobot.agentservice"
            android:exported="true" />

        <service android:name="com.ainirobot.features.web.WebRemoteService" android:process=":agent_web"/>
        <service android:name=".service.OpenAgentService"
            android:exported="true"
            android:foregroundServiceType="microphone"
            android:permission="com.ainirobot.agent.permission.AGENT_SERVICE"
            tools:ignore="ForegroundServicePermission" />
        <activity android:name="com.ainirobot.app.ConnectivityTestActivity"
            android:exported="false"
            android:launchMode="singleTop"/>
    </application>

</manifest>