package com.cm.speech.asr;

public class AwakeAngleJni {
    public static final int SOCKET_ASR = 0;
    public static final int SOCKET_PCM_DUMP = 1;
    public static final int SOCKET_WAKEUP_DUMP = 2;

    static {
        System.loadLibrary("awakeAngle");
    }

    public static native int init(int type);

    public static native int send(int fd, byte[] data);

    public static native int receive(int fd, byte[] data);

    public static native int release(int fd);

}

