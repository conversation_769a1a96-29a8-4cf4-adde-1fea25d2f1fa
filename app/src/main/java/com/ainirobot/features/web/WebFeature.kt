package com.ainirobot.features.web

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import com.ainirobot.agent.BuildConfig
import com.ainirobot.agent.core.CommonMessage
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.agent.web.IRemoteWebView
import com.ainirobot.agent.web.IWebViewCallback
import com.ainirobot.common.Config
import com.ainirobot.common.DataStore
import com.ainirobot.common.runOnMain
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.KLog.e
import com.ainirobot.common.utils.MessageSenderUtil
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull

object WebFeature : IWebViewCallback.Stub() {

    private val APP_ID: String get() {
        return if (BuildConfig.REGION_OVERSEA) {
            "system_95b3de1d8f68b33892e5ac2b42662517"
        } else {
            "system_f1383df09054ea2ae5ce28a977b54a5d"
        }
    }

    override fun onMessage(tag: String?, message: String?) {
        if (tag == null || message == null) {
            return
        }
        MessageSenderUtil.sendAgentMessage(tag, message)
    }

    override fun onPageFinished() {
        KLog.d("onPageFinished", "WebFeature")
        <EMAIL>?.invoke()
        <EMAIL> = null
    }

    override fun onClose(isUpdatePage: Boolean) {
        KLog.d("onClose isUpdatePage: $isUpdatePage", "WebFeature")
        isShowing = false
        if (isUpdatePage) {
            DataStore.currentAppId = lastAppId
            DataStore.currentPageId = lastPageId
            MessageSender.sendMessage(
                CommonMessage(
                    name = "state.robot_state",
                    content = mapOf(
                        "interface_state" to mapOf(
                            "app_id" to lastAppId,
                            "page_id" to lastPageId
                        )
                    )
                )
            )
        }

        MessageSenderUtil.sendAgentMessage("anchor", "")
        MessageSenderUtil.sendAgentMessage("webview_dom", "")

        <EMAIL>?.invoke()
        <EMAIL> = null
        <EMAIL> = null

        Config.context.unbindService(connection)
    }

    private val connection = object : ServiceConnection {

        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            KLog.d("onServiceConnected", "WebFeature")
            webView = IRemoteWebView.Stub.asInterface(service).apply {
                registerCallback(this@WebFeature)
                targetUrl?.let {
                    loadUrl(it)
                }
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            KLog.d("onServiceDisconnected", "WebFeature")
            webView = null
        }
    }

    private var webView: IRemoteWebView? = null
    @Volatile
    private var isShowing = false
    private var targetUrl: String? = null

    private var lastAppId = ""
    private var lastPageId = ""

    @Volatile
    private var onPageFinish: (() -> Unit)? = null

    private fun bindService() {
        Config.context.bindService(
            Intent(Config.context, WebRemoteService::class.java),
            connection,
            Context.BIND_AUTO_CREATE
        )
    }

    private fun showWeb() {
        if (isShowing) {
            return
        }
        isShowing = true
        lastAppId = DataStore.currentAppId
        lastPageId = DataStore.currentPageId

        DataStore.currentAppId = APP_ID
        DataStore.currentPageId = ""
        MessageSender.sendMessage(
            CommonMessage(
                name = "state.robot_state",
                content = mapOf(
                    "interface_state" to mapOf(
                        "app_id" to APP_ID,
                        "page_id" to ""
                    )
                )
            )
        )
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun openWeb(url: String, timeoutMillis: Long = 30000): Boolean {
        showWeb()
        val result: Boolean? = withTimeoutOrNull(timeoutMillis) {
            suspendCancellableCoroutine { continuation ->
                runOnMain {
                    <EMAIL>(url) {
                        continuation.resume(true) { e ->
                            e.e("FloatWebView")
                        }
                    }
                }
            }
        }
        return result ?: false
    }

    private fun openWebAsync(url: String, callback: (() -> Unit)? = null) {
        this.onPageFinish?.invoke()
        this.onPageFinish = callback
        if (webView == null) {
            targetUrl = url
            bindService()
            return
        }
        webView?.loadUrl(url)
    }

    fun closeWeb(updatePage: Boolean = true): Boolean {
        if (!isShowing) return false
        KLog.d("closeWeb updatePage: $updatePage", "WebFeature")
        webView?.closeWebView(updatePage)
        return true
    }

    fun simulateClick(index: Int) {
        KLog.d("simulateClick index: $index", "WebFeature")
        webView?.simulateClick(index)
    }
}
