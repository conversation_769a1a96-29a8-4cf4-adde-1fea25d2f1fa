package com.ainirobot.features.web

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.AssetManager
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.CookieManager
import android.webkit.GeolocationPermissions
import android.webkit.JavascriptInterface
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ImageView
import com.ainirobot.agent.R
import com.ainirobot.agent.web.IWebViewCallback
import com.ainirobot.common.Config.ioScope
import com.ainirobot.common.runOnMain
import com.ainirobot.common.utils.DeviceOS
import com.ainirobot.common.utils.JavaUtil
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.widget.KProgressBar
import com.google.gson.Gson
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.ByteArrayInputStream
import java.net.HttpURLConnection
import java.net.URL
import java.nio.charset.Charset


class FloatWebView(
    private val context: Context
) {

    companion object {

        init {
            JavaUtil.hookWebView()
        }
    }

    private var rootView: View? = null
    private var webView: WebView? = null
    private var backBtn: ImageView? = null
    private var forwardBtn: ImageView? = null
    private var refreshBtn: ImageView? = null
    private var progressBar: KProgressBar? = null

    private var isErrorPage = false

    var webViewCallback: IWebViewCallback? = null

    @SuppressLint("InflateParams")
    private fun show() {
        if (webView != null) {
            return
        }
        val vm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val layoutParams = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.TYPE_PHONE,
            0,
            PixelFormat.TRANSLUCENT
        )
        layoutParams.gravity = Gravity.TOP
        LayoutInflater.from(context).inflate(R.layout.float_window_web_view, null).let { view ->
            rootView = view
            webView = view.findViewById<WebView>(R.id.web_view).also {
                initWebView(it)
            }
            view.findViewById<ImageView>(R.id.close_btn).setOnClickListener { dismiss() }
            backBtn = view.findViewById<ImageView>(R.id.back_btn).also {
                it.setOnClickListener { goBack() }
            }
            forwardBtn = view.findViewById<ImageView>(R.id.forward_btn).also {
                it.setOnClickListener { goForward() }
            }
            refreshBtn = view.findViewById<ImageView>(R.id.refresh_btn).also {
                it.setOnClickListener { reload() }
            }
            progressBar = view.findViewById(R.id.progress_bar)
            vm.addView(view, layoutParams)
        }
    }

    fun dismiss(updatePage: Boolean = true): Boolean {
        clearView()
        try {
            webViewCallback?.onClose(updatePage)
        } catch (e: Exception) {
            e.printStackTrace()
            android.os.Process.killProcess(android.os.Process.myPid())
        }
        return true
    }

    private fun clearView() {
        val rootView = rootView ?: return
        runOnMain {
            (context.getSystemService(Context.WINDOW_SERVICE) as WindowManager).removeView(rootView)
            releaseWebView()
        }
        this.rootView = null
    }

    private fun releaseWebView() {
        val webView = webView ?: return
        webView.stopLoading()
        webView.clearHistory()
        webView.loadDataWithBaseURL(null, "", "text/html", "utf-8", null)
        webView.pauseTimers()
        webView.webChromeClient = null
        webView.parent?.also {
            if (it is ViewGroup) {
                it.removeView(webView)
            }
        }
        webView.removeAllViews()
        webView.destroy()
        this.webView = null
    }

    @SuppressLint("SetJavaScriptEnabled", "JavascriptInterface")
    private fun initWebView(webView: WebView) {
        webView.setInitialScale((ResourceInjector.SCALE*100).toInt())
        webView.settings.apply {
            javaScriptEnabled = true
            javaScriptCanOpenWindowsAutomatically = true
            allowContentAccess = false
            allowFileAccess = false
            useWideViewPort = true // 将图片调整到适合WebView的大小
            loadWithOverviewMode = true // 缩放至屏幕的大小
            setGeolocationEnabled(true) // 启用地理定位

            databaseEnabled = true
            domStorageEnabled = true
            setSupportZoom(true)
            displayZoomControls = false

            cacheMode = WebSettings.LOAD_CACHE_ELSE_NETWORK
            // 启用混合模式
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            javaScriptCanOpenWindowsAutomatically = false
            loadsImagesAutomatically = true
            defaultTextEncodingName = "UTF-8"
            userAgentString = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; ) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.61 Chrome/126.0.6478.61 Not/A)Brand/8  Safari/537.36"
        }
        webView.webViewClient = object : WebViewClient() {

            private var isRedirect = false

            override fun onPageStarted(view: WebView, url: String, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                KLog.d("onPageStarted: $url", "FloatWebView")
                progressBar?.visibility = View.VISIBLE
                isRedirect = false
                isErrorPage = false
            }

            override fun onPageFinished(view: WebView, url: String) {
                super.onPageFinished(view, url)
                updateButtonState()
                progressBar?.visibility = View.GONE
                KLog.d("onPageFinished: $url, isRedirect: $isRedirect", "FloatWebView")

                // 如果是网页跳转则不执行JS
                if (isRedirect || url.startsWith("file:///android_asset/")) {
                    return
                }

                // 注入meta标签，部分网页（如：携程）预注入无效，此处需要二次处理
                view.evaluateJavascript(
                    """var meta = document.querySelector('meta[name="viewport"]');
                            if (!meta) {
                              meta = document.createElement('meta');
                              meta.name = 'viewport';
                              document.head.appendChild(meta);
                            }
                            meta.content = 'width=device-width, initial-scale=${ResourceInjector.SCALE}';""",
                    null
                )

                val jsCode = readJavaScriptFile(context, "webviewJs.js")
                view.evaluateJavascript(jsCode) { result ->
                    KLog.d("evaluateJavascript result: $result", "FloatWebView")
                }
            }

            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                KLog.d("shouldOverrideUrlLoading result: ${request?.url}", "FloatWebView")
                val url = request?.url?.toString() ?: return super.shouldOverrideUrlLoading(view, request)
                if(!url.startsWith("http:") && !url.startsWith("https:")) {
                    return super.shouldOverrideUrlLoading(view, request)
                }
                isRedirect = true
                view?.loadUrl(url)
                return true
            }

            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                KLog.d("onReceivedError result: ${request?.url}, ${error?.errorCode}", "FloatWebView")
                if (request != null && request.isForMainFrame) {
                    isErrorPage = true
                    if (DeviceOS.language.codeName == "zh_CN") {
                        view?.loadUrl("file:///android_asset/error_domestic.html")
                    } else {
                        view?.loadUrl("file:///android_asset/error_oversea.html")
                    }
                }
            }

            private fun extractCharset(contentType: String): String {
                var charset = "UTF-8"
                val parts = contentType.split(";".toRegex()).dropLastWhile { it.isEmpty() }
                    .toTypedArray()
                for (part in parts) {
                    if (part.trim { it <= ' ' }.startsWith("charset=")) {
                        charset = part.split("=".toRegex(), limit = 2)
                            .toTypedArray()[1].trim { it <= ' ' }
                        break
                    }
                }
                return charset
            }

            override fun shouldInterceptRequest(
                view: WebView,
                request: WebResourceRequest
            ): WebResourceResponse? {
                // 预注入meta标签
//                if (request.isForMainFrame) {
//                    try {
//                        val reqUrl = request.url.toString()
//                        val url = URL(reqUrl)
//                        val connection = url.openConnection() as HttpURLConnection
//
//                        // 复制原始请求头（关键步骤）
//                        val headers = request.requestHeaders
//                        for ((key, value) in headers) {
//                            connection.setRequestProperty(key, value)
//                        }
//
//                        // 写入 Cookie 到请求头
//                        val cookieManager = CookieManager.getInstance()
//                        val cookie = cookieManager.getCookie(reqUrl)
//                        if (!cookie.isNullOrEmpty()) {
//                            connection.setRequestProperty("Cookie", cookie)
//                        }
//
//                        connection.connect()
//
//                        // 从响应头获取 Content-Type
//                        val contentType = connection.contentType
//                        if (contentType!= null && contentType.startsWith("text/html")) {
//                            val charset = extractCharset(contentType)
//                            KLog.d("start inject meta", "FloatWebView")
//                            val output = ResourceInjector(Charset.forName(charset))
//                                .injectMeta(connection.inputStream)
//                            KLog.d("end inject meta", "FloatWebView")
//                            if (output != null) {
//                                return WebResourceResponse(
//                                    "text/html",
//                                    charset,
//                                    ByteArrayInputStream(output.toByteArray())
//                                )
//                            }
//                        }
//                    } catch (e: Exception) {
//                        e.printStackTrace()
//                    }
//                }
                return super.shouldInterceptRequest(view, request)
            }
        }
        webView.webChromeClient = object : WebChromeClient() {

            override fun onProgressChanged(view: WebView, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                progressBar?.progress = newProgress / 100.0f
            }

            override fun onGeolocationPermissionsShowPrompt(
                origin: String?,
                callback: GeolocationPermissions.Callback?
            ) {
                callback?.invoke(origin, true, false)
            }
        }
    }

    private inner class WebViewInterface(
        private val callback: IWebViewCallback?
    ) {

        @JavascriptInterface
        fun onMessage(message: String) {
            KLog.d( "onWebViewMessage Received json message from WebView: $message","FloatWebView")
            if (message.startsWith("dom:")) {
                // viewModel?.reportInterfaceState(data.substring(4)) // 处理 'dom:' 消息
                if (isErrorPage) {
                    callback?.onMessage(
                        "webview_dom",
                        "<Page Error, Content is Null>"
                    )
                } else {
                    callback?.onMessage("webview_dom", message.substring(4))
                }
            } else if (message.startsWith("anchor:")) {
                var textContent = message.substring(7) // 处理 'anchor:' 消息
                textContent = textContent.replace(Regex("[\\n\\r]"), " ")
                    .replace(Regex("\\s+"), " ")
                    .trim()
                callback?.onMessage("anchor", textContent)
            } else {
                KLog.d("onWebViewMessage else", "FloatWebView")
            }
        }

        @JavascriptInterface
        fun onExecuteFinish() {
            KLog.d("onExecuteFinish", "FloatWebView")
            ioScope.launch {
                delay(1000)
                <EMAIL>?.onPageFinished()
            }
        }
    }

    private fun readJavaScriptFile(context: Context, fileName: String): String {
        val assetManager: AssetManager = context.assets
        return assetManager.open(fileName).bufferedReader().use { it.readText() }
    }

    private fun updateButtonState() {
        backBtn?.isEnabled = webView?.canGoBack() ?: false
        forwardBtn?.isEnabled = webView?.canGoForward() ?: false
        refreshBtn?.let {
            if (!it.isEnabled) {
                it.isEnabled = true
            }
        }
    }

    fun simulateClick(index: Int) {
        val data = mapOf("type" to "click", "index" to index)
        val json = Gson().toJson(data)
        KLog.d("FloatWebView", "click: $json")
        webView?.evaluateJavascript("javascript:receiveMessage('$json')", null);
    }

    private fun goBack() {
        webView?.apply {
            if (canGoBack()) {
                goBack()
            }
        }
    }

    private fun goForward() {
        webView?.apply {
            if (canGoForward()) {
                goForward()
            }
        }
    }

    private fun reload() {
        webView?.reload()
//        webView?.evaluateJavascript("javascript:getA()", null);
    }

    fun loadUrl(url: String) {
        show()
        webView?.apply {
            removeJavascriptInterface("Android")
            addJavascriptInterface(WebViewInterface(webViewCallback), "Android")
        }
        KLog.d("openUrl: $url", "FloatWebView")
        webView?.loadUrl(url)
    }
}