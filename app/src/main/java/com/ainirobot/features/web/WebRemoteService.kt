package com.ainirobot.features.web

import android.app.Service
import android.content.Intent
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import com.ainirobot.agent.web.IRemoteWebView
import com.ainirobot.agent.web.IWebViewCallback
import com.ainirobot.common.utils.KLog


class WebRemoteService : Service() {

    private val handler = Handler(Looper.getMainLooper())

    private val binder = object : IRemoteWebView.Stub() {

        override fun loadUrl(url: String) {
            handler.post {
                webView?.loadUrl(url)
            }
        }

        override fun simulateClick(index: Int) {
            handler.post {
                webView?.simulateClick(index)
            }
        }

        override fun closeWebView(updatePage: Boolean) {
            handler.post {
                webView?.dismiss(updatePage)
            }
        }

        override fun registerCallback(callback: IWebViewCallback?) {
            <EMAIL>?.webViewCallback = callback
        }
    }

    private var webView: FloatWebView? = null

    override fun onCreate() {
        super.onCreate()
        KLog.d("onCreate", "WebRemoteService")
        webView = FloatWebView(this)
    }

    override fun onBind(intent: Intent?): IBinder {
        KLog.d("onBind", "WebRemoteService")
        return binder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        KLog.d("onUnbind", "WebRemoteService")
        webView = null
        return super.onUnbind(intent)
    }

    override fun onDestroy() {
        super.onDestroy()
        KLog.d("onDestroy", "WebRemoteService")
        handler.postDelayed({
            android.os.Process.killProcess(android.os.Process.myPid())
        }, 1000)
    }
}