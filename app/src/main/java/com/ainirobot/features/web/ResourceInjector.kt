package com.ainirobot.features.web

import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.nio.charset.Charset

class ResourceInjector(
    private val charset: Charset
) {

    companion object {

        const val SCALE = 0.5

//        private const val META_INJECT_CONTENT = """<meta name="viewport" content="width=device-width, initial-scale=0.5">"""
        private const val META_INJECT_CONTENT = """<meta name="viewport" content="width=device-width, initial-scale=${SCALE}, maximum-scale=1.0, user-scalable=no">"""

    }

    fun injectMeta(input: InputStream): ByteArrayOutputStream? {
        return try {
            val outputStream = ByteArrayOutputStream()
            val reader = input.bufferedReader(charset)
            val buffer = CharArray(1024)
            val sb = StringBuilder()
            var foundHead = false
            var foundViewport = false

            while (reader.read(buffer).also { if (it == -1) return@also }.let { count ->
                if (count > 0) {
                    sb.append(buffer, 0, count)

                    // 查找 <head 位置
                    if (!foundHead) {
                        val headIndex = sb.indexOf("<head", ignoreCase = true)
                        if (headIndex != -1) {
                            foundHead = true
                            outputStream.write(sb.substring(0, headIndex + 5).toByteArray(charset))
                            sb.delete(0, headIndex + 5)
                        }
                    }

                    // 在head标签内查找viewport
                    if (foundHead && !foundViewport) {
                        val viewportIndex = sb.indexOf("""name="viewport"""", ignoreCase = true)
                        if (viewportIndex != -1) {
                            // 找到viewport，需要替换整个meta标签
                            val metaStart = sb.lastIndexOf("<meta", viewportIndex)
                            val metaEnd = sb.indexOf(">", viewportIndex) + 1
                            if (metaStart != -1 && metaEnd != -1) {
                                outputStream.write(sb.substring(0, metaStart).toByteArray(charset))
                                outputStream.write(META_INJECT_CONTENT.toByteArray(charset))
                                sb.delete(0, metaEnd)
                                foundViewport = true
                            }
                        }
                    }

                    // 查找 </head> 位置
                    val headEndIndex = sb.indexOf("</head>", ignoreCase = true)
                    if (headEndIndex != -1) {
                        if (!foundViewport) {
                            // 如果没找到viewport，在</head>前插入
                            outputStream.write(sb.substring(0, headEndIndex).toByteArray(charset))
                            outputStream.write(META_INJECT_CONTENT.toByteArray(charset))
                            outputStream.write(sb.substring(headEndIndex).toByteArray(charset))
                        } else {
                            outputStream.write(sb.toString().toByteArray(charset))
                        }
                        sb.clear()
                    }

                    // 如果缓冲区太大，输出部分内容
                    if (sb.length > 4096) {
                        val saveLength = 1024  // 保留一部分以防标签被分割
                        outputStream.write(sb.substring(0, sb.length - saveLength).toByteArray(charset))
                        sb.delete(0, sb.length - saveLength)
                    }
                }
                count != -1
            }) { }

            // 输出剩余内容
            if (sb.isNotEmpty()) {
                outputStream.write(sb.toString().toByteArray(charset))
            }

            reader.close()
            outputStream
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}