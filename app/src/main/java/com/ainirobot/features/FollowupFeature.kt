package com.ainirobot.features

import com.ainirobot.agent.core.CommonMessage
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.bridge.executor.ActionEvents
import com.ainirobot.common.Config
import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.common.utils.KLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.LinkedList
import java.util.Queue

object FollowupFeature {

    private data class Followup(
        val planId: String,
        val runId: String,
        val expiredTime: Long
    ) {

        val isExpired: Boolean
            get() = System.currentTimeMillis() > expiredTime
    }

    /**
     * 需要在plan执行完成后调用followup的action列表
     */
    private val actionsAfterPlan = setOf(
        ActionEvents.NAVIGATE_REC_START,
        ActionEvents.OUTDOOR_NAVIGATE_START,
        ActionEvents.Server.SAY_WELCOME_OR_FAREWELL_MESSAGE,
        ActionEvents.Server.SEND_MESSAGE,
        ActionEvents.START_DANCE,
        ActionEvents.HEAD_NOD,
        ActionEvents.INTERVIEW_START,
//        ActionEvents.GUIDE_INTRODUCTION,
        ActionEvents.INTERVIEW_START_PHOTO,
        ActionEvents.OPEN_WEB_URL,
        ActionEvents.OUTDOOR_NAVIGATE_START,
        ActionEvents.CRUISE,
        ActionEvents.MOVE_DIRECTION,
        ActionEvents.TURN_DIRECTION,
        ActionEvents.COME_FAR,
        ActionEvents.CONFIGURE_WELCOME_MESSAGE,
        ActionEvents.REGISTER,
        ActionEvents.Server.WEATHER,
        ActionEvents.Server.CALENDAR,
    )

    /**
     * 需要在TTS播报完成后调用followup的action列表
     */
    private val actionsAfterTTS = emptySet<String>()

    private val lock = Mutex()

    private val queueAfterPlan: Queue<Followup> = LinkedList()
    private val queueAfterTTS: Queue<Followup> = LinkedList()
    @Volatile
    private var currentRunId = ""
    @Volatile
    private var currentAction = ""

    /**
     * 从池子中挑选对应的followup，并添加到执行队列
     */
    suspend fun pickFollowup(planId: String, runId: String, actionName: String) {
        if (Config.isMultiActionVersion || actionName.isEmpty()) {
            return
        }
        val isInActionAfterPlan = actionsAfterPlan.contains(actionName)
        val isInActionAfterTTS = actionsAfterTTS.contains(actionName)
        if (!isInActionAfterPlan && !isInActionAfterTTS) {
            return
        }
//        KLog.d("xx=======>pickFollowup: $runId, $actionName")
        val item  = Followup(planId, runId, System.currentTimeMillis() + 180000)
        lock.withLock {
            if (isInActionAfterPlan) {
                queueAfterPlan.add(item)
            } else {
                queueAfterTTS.add(item)
            }
        }
        this.currentRunId = runId
        this.currentAction = actionName
    }

    /**
     * 执行plan后的followup
     */
    suspend fun executeAfterPlan(planId: String, runId: String) {
        if (Config.isMultiActionVersion) {
            return
        }
//        KLog.d("xx=====11==>executeAfterPlan: $planId, $runId")
        for (i in 0 until 5) {
            if (checkFollowup(planId, runId)) {
                break
            }
            delay(1000)
        }
//        KLog.d("xx=====22==>executeAfterPlan: $planId, $runId")
        lock.withLock {
            var followup = queueAfterPlan.poll()
            while (followup != null) {
                if (!followup.isExpired &&
                    followup.planId == planId &&
                    followup.runId == runId) {
                    sendFollowup()
                    break
                }
                followup = queueAfterPlan.poll()
            }
        }
    }

    private suspend fun checkFollowup(planId: String, runId: String): Boolean {
        return lock.withLock {
            var found = false
            for (item in queueAfterPlan) {
                if (item.planId == planId && item.runId == runId) {
                    found = true
                    break
                }
            }
            found
        }
    }

    /**
     * 执行TTS后的followup
     */
    suspend fun executeAfterTTS() {
        if (Config.isMultiActionVersion) {
            return
        }
        lock.withLock {
            queueAfterTTS.poll()?.let { sendFollowup() }
        }
    }

    private fun sendFollowup() {
        MessageSender.sendMessage(
            CommonMessage(
                name = "cmd",
                content = mapOf("command" to "generate_follow-up")
            ),
            false
        )
        RobotConsole.print("Generate follow-up")
    }
}