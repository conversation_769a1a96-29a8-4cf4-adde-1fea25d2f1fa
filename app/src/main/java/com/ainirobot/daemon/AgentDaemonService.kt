package com.ainirobot.daemon

import android.app.Service
import android.content.ComponentName
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.os.Process
import android.util.Log
import com.ainirobot.agent.IAgent
import com.ainirobot.agent.service.AgentService
import kotlinx.coroutines.*

class AgentDaemonService : Service() {

    companion object {
        private const val TAG = "AgentDaemonService"

        private const val MESSAGE_TIMEOUT = 20 * 60 * 1000L
        private const val CHECK_INTERVAL = 60 * 1000L // 60s检查一次

        private const val ACTION = "com.ainirobot.agent.action.AGENT"
    }

    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var isMonitoring = false

    private var agent: IAgent? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "DaemonService onCreate, PID: ${Process.myPid()}")
        bindAgentService()
        startMonitoring()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "DaemonService onStartCommand")
        return START_STICKY // 确保服务被杀死后自动重启
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun startMonitoring() {
        if (isMonitoring) return

        isMonitoring = true
        serviceScope.launch {
            while (isActive && isMonitoring) {
                try {
                    checkAgentConnect()
                    delay(CHECK_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "Error in monitoring loop", e)
                    delay(CHECK_INTERVAL)
                }
            }
        }
    }

    private fun checkAgentConnect() {
        agent?.let {
            val status = it.status

            Log.d(TAG, "Check agent status: $status")

            if (!status["network"].toBoolean()) {
                return
            }

            val time = status["messageTime"]?.toLong() ?: 0
            if (System.currentTimeMillis() - time > MESSAGE_TIMEOUT) {
                killAgentService()
            }
        }
    }

    private fun killAgentService() {
        Log.d(TAG, "killAgentService")
        try {
            // 获取主进程包名
            val mainPackageName = packageName
            
            // 使用 am force-stop 命令杀死主进程
            val process = Runtime.getRuntime().exec("am force-stop $mainPackageName")
            process.waitFor()
            
            Log.d(TAG, "Main process killed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to kill main process", e)
            
            // 备用方案：杀死自己的进程，让系统重启整个应用
            try {
                Process.killProcess(Process.myPid())
            } catch (ex: Exception) {
                Log.e(TAG, "Failed to kill daemon process", ex)
            }
        }
    }

    private fun bindAgentService() {
        val intent = Intent(this, AgentService::class.java)
        intent.action = ACTION
        bindService(
            intent, object : ServiceConnection {
                override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
                    Log.d(TAG, "AgentService connected")
                    agent = IAgent.Stub.asInterface(service)
                }

                override fun onServiceDisconnected(name: ComponentName?) {
                    Log.d(TAG, "AgentService disconnected")
                    agent = null
                }
            },
            BIND_AUTO_CREATE
        )
    }

    override fun onDestroy() {
        Log.d(TAG, "DaemonService onDestroy")
        isMonitoring = false
        serviceScope.cancel()
        super.onDestroy()
    }
}
