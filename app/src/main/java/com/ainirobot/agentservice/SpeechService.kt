package com.ainirobot.agentservice

import android.app.Service
import android.content.ComponentName
import android.content.Intent
import android.content.ServiceConnection
import android.net.ConnectivityManager
import android.net.Network
import android.os.IBinder
import android.text.TextUtils
import android.util.Log
import com.ainirobot.agent.service.AgentService
import com.ainirobot.common.ConnectivityAssist
import com.ainirobot.common.utils.KLog
import com.ainirobot.coreservice.ISkill
import com.ainirobot.coreservice.client.Definition
import com.ainirobot.coreservice.client.RobotSettings
import com.ainirobot.speechasrservice.SpeechDelegate
import com.ainirobot.speechasrservice.SpeechServiceImpl
import com.ainirobot.speechasrservice.collection.VoiceDataCollection

class SpeechService : Service(), ServiceConnection {

    companion object {

        private const val TAG = "AgentSpeechService"

    }

    private val speechDelegate = SpeechDelegate()

    private val networkCallback = object : ConnectivityManager.NetworkCallback() {

        override fun onAvailable(network: Network) {
            if (speechDelegate.impl == null) {
                KLog.d("NetworkCallback onAvailable", "SpeechService")
                bindAgentService()
            }
        }
    }

    @Volatile
    private var isBoundAgentService = false

    override fun onCreate() {
        super.onCreate()
        KLog.d("onCreate", TAG)
        ConnectivityAssist.register(networkCallback)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        KLog.d("onStartCommand: ${intent?.`package`}, Action: ${intent?.action}", TAG)
        return START_NOT_STICKY
    }

    override fun onBind(intent: Intent?): IBinder {
        KLog.d("onBind: ${intent?.`package`}, Action: ${intent?.action}", TAG)
        return if (VoiceDataCollection.match(intent)) {
            KLog.d("VoiceDataCollection matched", TAG)
            VoiceDataCollection.getInstance()
        } else {
            KLog.d("SpeechServiceImpl matched", TAG)
            bindAgentService()
            SpeechServiceImpl(speechDelegate)
        }
    }

    private fun bindAgentService() {
        val systemOSType = RobotSettings.getGlobalSettings(
            this,
            Definition.ROBOT_SETTINGS_SYSTEM_OS_TYPE,
            ""
        )
        Log.d(TAG, "bindAgentService systemOSType=$systemOSType")
        if (!TextUtils.equals(systemOSType, Definition.OSType.AGENTOS.value)) {
            Log.d(TAG, "bindAgentService return because osType is not agent")
            return
        }
        if (isBoundAgentService) {
            return
        }
        val connected = ConnectivityAssist.isConnected
        KLog.d("bindAgentService, isNetworkConnected: $connected", "SpeechService")
        if (connected) {
            isBoundAgentService = true
            bindService(
                Intent(this, AgentService::class.java),
                this,
                BIND_AUTO_CREATE
            )
        }
    }

    override fun onUnbind(intent: Intent?): Boolean {
        KLog.d("onUnbind: ${intent?.`package`}, Action: ${intent?.action}", TAG)
//        if (!VoiceDataCollection.match(intent)) {
//            unbindService(this)
//        }
        return super.onUnbind(intent)
    }

    override fun onServiceConnected(name: ComponentName, service: IBinder) {
        KLog.d("onServiceConnected: ${name.className}", TAG)
        when (name.className) {
            "com.ainirobot.speechasrservice.collection.VoiceDataCollection" -> {
                // 处理VoiceDataCollection的相关逻辑
                KLog.d("VoiceDataCollection connected", TAG)
            }
            "com.ainirobot.agent.service.AgentService" -> {
                // 处理SpeechServiceImpl的相关逻辑
                KLog.d("AgentService connected", TAG)
                speechDelegate.impl = ISkill.Stub.asInterface(service)
            }
            else -> {
                KLog.e("未知的服务连接: ${name.className}", TAG)
            }
        }
    }

    override fun onServiceDisconnected(name: ComponentName) {
        KLog.d("onServiceDisconnected: ${name.className}", TAG)
        when (name.className) {
            "com.ainirobot.agent.service.AgentService" -> {
                speechDelegate.impl = null
                isBoundAgentService = false
            }
        }
    }

    override fun onDestroy() {
        KLog.d("onDestroy", TAG)
        ConnectivityAssist.unregister(networkCallback)
        isBoundAgentService = false
        super.onDestroy()
    }
}