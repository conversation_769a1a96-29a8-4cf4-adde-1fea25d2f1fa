package com.ainirobot.common.network

import com.ainirobot.agent.BuildConfig
import com.ainirobot.agent.service.RobotStatus
import com.ainirobot.common.Config
import com.ainirobot.common.Config.ioScope
import com.ainirobot.common.Config.prettyGson
import com.ainirobot.common.debug.Env
import com.ainirobot.common.utils.DateUtil.dateFormat
import com.ainirobot.common.utils.DeviceOS
import com.ainirobot.common.utils.KLog
import kotlinx.coroutines.launch

object WebHookApi : BaseApi() {

    private const val URL = "https://open.feishu.cn/open-apis/bot/v2/hook/e3ec413f-ab39-49f2-a1e1-fad845e9ce36"

    fun sendAsync(message: String, tag: String = "message") {
        sendAsync(mapOf(tag to message))
    }

    private fun sendAsync(messages: Map<String, *>) {
//        if (Config.releaseMode) return
        ioScope.launch {
            val result = requestPOST<String?>(
                URL,
                mapOf(
                    "msg_type" to "text",
                    "content" to mapOf(
                        "text" to prettyGson.toJson(
                            messages + mapOf(
                                RobotStatus.KEY_DEVICE_ID to DeviceOS.deviceId,
                                RobotStatus.KEY_ROBOT_NAME to DeviceOS.robotName,
                                RobotStatus.KEY_ENTERPRISE_ID to DeviceOS.enterpriseId,
                                "agent_env" to Env.agentEnvName,
                                RobotStatus.KEY_ACTION_VERSION to Config.actionVersion,
                                "app_version" to BuildConfig.VERSION_NAME,
                                "region_version" to BuildConfig.REGION_VERSION,
                                "current_time" to System.currentTimeMillis().dateFormat()
                            )
                        )
                    )
                )
            )
            KLog.d("WebHookApi send result: $result")
        }
    }
}