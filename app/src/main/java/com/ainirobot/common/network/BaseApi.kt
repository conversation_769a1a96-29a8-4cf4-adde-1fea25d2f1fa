package com.ainirobot.common.network

import io.ktor.client.call.body
import io.ktor.client.request.forms.formData
import io.ktor.client.request.forms.submitForm
import io.ktor.client.request.forms.submitFormWithBinaryData
import io.ktor.client.request.get
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.utils.io.InternalAPI

abstract class BaseApi {

    suspend inline fun <reified T> requestGET(url: String, header: Map<String, String>? = null): T? {
        try {
            val response = requestClient.get(url) {
                header?.forEach {
                    headers.append(it.key, it.value)
                }
            }
            if (response.status == HttpStatusCode.OK) {
                return response.body()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    suspend inline fun <reified T> requestPOST(url: String, body: Any, header: Map<String, String>? = null): T? {
        try {
            val response = requestClient.post(url) {
                header?.forEach {
                    headers.append(it.key, it.value)
                }
                setBody(body)
            }
            if (response.status == HttpStatusCode.OK) {
                return response.body()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    suspend inline fun <reified T> submitForm(url: String, params: Map<String, String>, header: Map<String, String>? = null): T? {
        try {
            val response = requestClient.submitForm(
                url,
                Parameters.build {
                    params.forEach { (key, value) ->
                        append(key, value)
                    }
                }
            ) {
                header?.forEach {
                    headers.append(it.key, it.value)
                }
            }
            if (response.status == HttpStatusCode.OK) {
                return response.body()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    @OptIn(InternalAPI::class)
    suspend inline fun <reified T> submitFormWithBinaryData(url: String, params: Map<String, Any>, header: Map<String, String>? = null): T? {
        try {
            val response = requestClient.submitFormWithBinaryData(
                url,
                formData = formData {
                    params.forEach { (key, value) ->
                        append(key, value)
                    }
                }
            ) {
                header?.forEach {
                    headers.append(it.key, it.value)
                }
            }
            if (response.status == HttpStatusCode.OK) {
                return response.body()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }
}