package com.ainirobot.common.network

import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.KLog.e
import com.ainirobot.coreservice.client.SystemApi
import com.ainirobot.coreservice.client.listener.CommandListener
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.suspendCancellableCoroutine
import org.json.JSONException
import org.json.JSONObject

object RemoteApi {

    data class Person(
        val id: String,
        val fullName: String,
        val organization: String,
        val avatarUrl: String
    )

    private fun getPersonList(personName: String, callback: (persons: List<Person>) -> Unit) {
        try {
            SystemApi.getInstance().requestPost(
                (System.currentTimeMillis() % 1000000).toInt(),
                "/capi/v1/robot/person_list",
                mapOf(
                    "get_action" to "aios_send_person_messages",
                    "filter_full_name" to personName,
                    "page_rows" to "5",
                ),
                "application/x-www-form-urlencoded",
                object : CommandListener() {

                    override fun onResult(result: Int, message: String?, extraData: String?) {
                        KLog.d("getPersonList result: $result, message: $message, extraData: $extraData", "RemoteApi")
                        if (result != 1 || message.isNullOrEmpty()) {
                            callback(emptyList())
                        } else {
                            try {
                                val jsonArray = JSONObject(message).optJSONObject("data")?.optJSONArray("person_list")
                                if (jsonArray == null || jsonArray.length() == 0) {
                                    callback(emptyList())
                                } else {
                                    val persons = mutableListOf<Person>()
                                    for (i in 0 until jsonArray.length()) {
                                        val person = jsonArray.optJSONObject(i)
                                        val personInfo = person.optJSONObject("person") ?: continue
                                        persons.add(
                                            Person(
                                                person.optString("person_id"),
                                                personInfo.optString("full_name") ?: "",
                                                personInfo.optString("dept") ?: "",
                                                personInfo.optString("avatar_url") ?: ""
                                            )
                                        )
                                    }
                                    callback(persons)
                                }
                            } catch (e: JSONException) {
                                KLog.e("Failed to parse JSON: ${e.message}", "RemoteApi")
                                RobotConsole.print("getPersonList result Failed to parse JSON: ${e.message}")
                                callback(emptyList())
                            }
                        }
                    }
                }
            )
        } catch (e: Exception) {
            e.e("RemoteApi")
            callback(emptyList())
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun getPersonListSync(personName: String): List<Person> {
        return suspendCancellableCoroutine { continuation ->
            getPersonList(personName) { persons ->
                continuation.resume(persons)  { e ->
                    e.e("RemoteApi")
                }
            }
        }
    }
}