package com.ainirobot.common.network

import com.ainirobot.agent.service.RobotStatus
import com.ainirobot.common.Config
import com.ainirobot.common.DataStore
import com.ainirobot.common.debug.Env
import com.ainirobot.common.utils.DeviceOS
import com.google.gson.annotations.SerializedName

data class RoomInfo(
    @SerializedName("code")
    val code: Int = 0,
    @SerializedName("msg")
    val message: String = "",
    @SerializedName("token")
    val token: String,
    @SerializedName("url")
    val url: String
)

object UserApi : BaseApi() {

    private fun String.encode(): String {
        return this.replace(" ", "%20")
    }

    suspend fun getRoomInfo(): RoomInfo? {
        val params = try {
            mapOf(
                RobotStatus.KEY_ENTERPRISE_ID to DeviceOS.enterpriseId.encode(),
                RobotStatus.KEY_DEVICE_ID to DeviceOS.deviceId.encode(),
                RobotStatus.KEY_GROUP_ID to DeviceOS.groupId.encode(),
                RobotStatus.KEY_CLIENT_ID to DeviceOS.clientId.encode(),
                RobotStatus.KEY_PRODUCT_ID to DataStore.productId.encode(),
                RobotStatus.KEY_PRODUCT_MODEL to DeviceOS.productMode.encode(),
                RobotStatus.KEY_ACTION_VERSION to Config.actionVersion,
                "updated_at" to System.currentTimeMillis()
            )
        } catch (e: Exception) {
            e.printStackTrace()
            android.os.Process.killProcess(android.os.Process.myPid())
            return null
        }
        return requestGET("${Env.agentUrl}&${params.entries.joinToString("&")}")
    }
}