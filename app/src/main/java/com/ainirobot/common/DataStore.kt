package com.ainirobot.common

import com.tencent.mmkv.MMKV

object DataStore {

    interface Callback {

        fun onDataChanged(key: String)

    }

    private val callbacks = mutableListOf<Callback>()

    fun addCallback(callback: Callback) {
        synchronized(callbacks) {
            callbacks.add(callback)
        }
    }

    fun removeCallback(callback: Callback) {
        synchronized(callbacks) {
            callbacks.remove(callback)
        }
    }

    private fun invoke(key: String){
        synchronized(callbacks) {
            callbacks.forEach { it.onDataChanged(key) }
        }
    }

    /**
     * 麦克风是否静音
     */
    var microphoneMuted: Boolean
        get() = getBoolean("key_microphone_muted", false)
        set(value) {
            set("key_microphone_muted", value)
        }

    /**
     * 是否开启免唤醒
     */
    var isEnableWakeFree: Boolean
        get() = getBoolean("key_enable_wake_free", true)
        set(value) {
            set("key_enable_wake_free", value)
        }

    var geoLocation: String
        get() = getString("key_geo_location", "")
        set(value) {
            set("key_geo_location", value)
        }

    /**
     * 当前电量
     */
    var batteryLevel: Int
        get() = getInt("key_battery_level", 0)
        set(value) {
            set("key_battery_level", value)
        }

    /**
     * 当前apk包名
     */
    var currentPackageName: String
        get() = getString("key_current_package_name", "")
        set(value) {
            set("key_current_package_name", value)
        }

    /**
     * 当前OPK应用Id
     */
    var currentAppId: String
        get() = getString("key_current_app_id", "")
        set(value) {
            set("key_current_app_id", value)
        }

    /**
     * 当前页面Id
     */
    var currentPageId: String
        get() = getString("key_current_page_id", "")
        set(value) {
            set("key_current_page_id", value)
        }

    /**
     * 当前AgentId
     */
    var currentAgentId: String
        get() = getString("key_current_agent_id", Config.AGENT_ID_LAUNCHER)
        set(value) {
            set("key_current_agent_id", value)
        }

    /**
     * 当前Agent的思考模式
     */
    var agentThoughtMode: String
        get() = getString("key_current_agent_thought_mode", "turbo")
        set(value) {
            set("key_current_agent_thought_mode", value)
        }

    /**
     * 推销人设信息
     */
    var agentProfileInfo: String
        get() = getString("key_robot_agent_character_info", "")
        set(value) {
            set("key_robot_agent_character_info", value)
        }

    /**
     * 当前Agent的设备级人设信息
     */
    var agentProfileDevice: String
        get() = getString("key_robot_agent_character_info", "")
        set(value) {
            set("key_robot_agent_character_info", value)
        }


    /**
     * 当前Agent的企业级人设信息
     */
    var agentProfileEnterprise: String
        get() = getString("key_agent_profile_enterprise", "")
        set(value) {
            set("key_agent_profile_enterprise", value)
        }

    val agentProfile: String
        get() = agentProfileDevice.ifEmpty {
            agentProfileEnterprise.ifEmpty {
                agentProfileInfo
            }
        }

    /**
     * 当前机器人地图信息
     */
    var mapInfo: String
        get() = getString("key_robot_current_mapInfo", "")
        set(value) {
            set("key_robot_current_mapInfo", value)
        }


    /**
     * 产品id
     */
    var productId: String
        get() = getString("key_current_product_id", "")
        set(value) {
            set("key_current_product_id", value)
        }

    /**
     * 用户是否已经手动设置过Spokesman
     */
    var isSpokesmanSet: Boolean
        get() = getBoolean("key_user_is_set_spokesman", false)
        set(value) {
            set("key_user_is_set_spokesman", value)
        }

    /**
     * 当前的Spokesman，只用来去重判断，访问请使用DeviceOS.spokesman
     */
    var currentSpokesman: String
        get() = getString("key_current_spokesman", "")
        set(value) = set("key_current_spokesman", value)

    private val kv: MMKV = MMKV.mmkvWithID("agent_service", MMKV.MULTI_PROCESS_MODE)

    operator fun set(key: String, value: String) {
        val old = kv.decodeString(key)
        kv.encode(key, value)
        if (old != value) {
            invoke(key)
        }
    }

    operator fun set(key: String, value: Int) {
        val old = kv.decodeInt(key)
        kv.encode(key, value)
        if (old != value) {
            invoke(key)
        }
    }

    operator fun set(key: String, value: Boolean) {
        val old = kv.decodeBool(key)
        kv.encode(key, value)
        if (old != value) {
            invoke(key)
        }
    }

    operator fun set(key: String, value: Float) {
        val old = kv.decodeFloat(key)
        kv.encode(key, value)
        if (old != value) {
            invoke(key)
        }
    }

    operator fun set(key: String, value: Long) {
        val old = kv.decodeLong(key)
        kv.encode(key, value)
        if (old != value) {
            invoke(key)
        }
    }

    operator fun set(key: String, value: Double) {
        val old = kv.decodeDouble(key)
        kv.encode(key, value)
        if (old != value) {
            invoke(key)
        }
    }

    fun getString(key: String, default: String): String {
        return kv.decodeString(key, default) ?: default
    }

    fun getInt(key: String, default: Int): Int {
        return kv.decodeInt(key, default)
    }

    fun getBoolean(key: String, default: Boolean): Boolean {
        return kv.decodeBool(key, default)
    }

    fun getFloat(key: String, default: Float): Float {
        return kv.decodeFloat(key, default)
    }

    fun getLong(key: String, default: Long): Long {
        return kv.decodeLong(key, default)
    }

    fun getDouble(key: String, default: Double): Double {
        return kv.decodeDouble(key, default)
    }
}