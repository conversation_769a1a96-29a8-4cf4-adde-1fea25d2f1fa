package com.ainirobot.common

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities

object ConnectivityAssist {

    private lateinit var impl: ConnectivityManager

    fun initialize(context: Context) {
        impl = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    /**
     * 是否连接网络
     */
    val isConnected: Boolean get() {
        val network = impl.activeNetwork ?: return false
        val networkCapabilities = impl.getNetworkCapabilities(network) ?: return false
        return networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }

    fun register(callback: ConnectivityManager.NetworkCallback) {
        impl.registerDefaultNetworkCallback(callback)
    }

    fun unregister(callback: ConnectivityManager.NetworkCallback) {
        impl.unregisterNetworkCallback(callback)
    }
}