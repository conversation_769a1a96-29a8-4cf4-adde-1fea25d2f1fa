package com.ainirobot.common

import com.ainirobot.bi.RTExceptionalEvent
import kotlin.system.exitProcess


object CrashCatcher : Thread.UncaughtExceptionHandler {

    fun initialize() {
        Thread.setDefaultUncaughtExceptionHandler(this)
    }

    override fun uncaughtException(t: Thread, e: Throwable) {
        try {
            report(t, e)
            Thread.sleep(3000)
        } catch (tr: Throwable) {
            tr.printStackTrace()
        } finally {
            android.os.Process.killProcess(android.os.Process.myPid())
            exitProcess(1)
        }
    }

    private fun report(t: Thread, e: Throwable) {
        e.printStackTrace()
        RTExceptionalEvent()
            .setTag("crash_exception")
            .setLevel(2)
            .setType(1)
            .setMessage("Thread: ${t.name}, Exception: ${e.message ?: "Unknown error"}")
            .setException(e)
            .report()
    }
}
