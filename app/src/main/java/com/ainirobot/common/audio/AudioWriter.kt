package com.ainirobot.common.audio

import android.os.Handler
import android.os.HandlerThread
import com.ainirobot.common.utils.DateUtil.dateFormat
import java.io.File
import java.io.RandomAccessFile

object AudioWriter {

    private val handler = Handler(
        HandlerThread("AudioWriter").apply { start() }.looper
    )

    @Volatile
    private var audioFile: File? = null

    private var sampleRate = 16000

    /**
     * 开始录音
     */
    fun startRecord(sid: String, sampleRate: Int): String {
        val fileName = "${System.currentTimeMillis().dateFormat("yyyy-MM-dd_HH-mm-ss.SSS")}-$sid.wav"
        handler.post {
            this.sampleRate = sampleRate
            val dir = File("/sdcard/audio")
            if (!dir.exists()) {
                dir.mkdirs()
            }
            
            // 检查并清理旧缓存文件
            cleanupCacheFiles(dir)
            
            audioFile = File(dir, fileName)
        }
        return fileName
    }

    /**
     * 清理旧的录音缓存文件
     * 当文件数量超过N个时，删除最早的M个文件
     */
    private fun cleanupCacheFiles(dir: File) {
        val files = dir.listFiles { file -> file.isFile && file.extension == "wav" }
        if (files != null && files.size > 6800) {
            files.sortBy { it.lastModified() }
            files.take(200).forEach { it.delete() }
        }
    }

    fun stopRecord() {
        handler.post {
            val wavFile = audioFile ?: return@post
            reWriteWavHeader(wavFile)
            audioFile = null
        }
    }

    private fun reWriteWavHeader(file: File) {
        try {
            val randomFile = RandomAccessFile(file, "rw")
            randomFile.seek(0)
            val header = WavHeader(
                randomFile.length().toInt(),
                sampleRate,
                1.toShort(), // 单声道
                16.toShort()
            )
            randomFile.write(header.header)
            randomFile.close()
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    fun write(data: ByteArray, size: Int, stereo: Boolean) {
        val newData = ByteArray(size)
        System.arraycopy(data, 0, newData, 0, size)
        handler.post {
            writeWavData(newData, size, stereo)
        }
    }

    private fun writeWavData(data: ByteArray, size: Int, stereo: Boolean) {
        val file = audioFile ?: return
        val buffer: ByteArray
        if (stereo) {
            buffer = ByteArray(size / 2) // 左声道
            val rightBuffer = ByteArray(size / 2) // 右声道
            WavHeader.separateData(data, buffer, rightBuffer, size)
        } else {
            buffer = data
        }
        WavHeader.saveData(file, buffer, sampleRate, 1)
    }
}