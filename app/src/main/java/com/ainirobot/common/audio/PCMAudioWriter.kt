package com.ainirobot.common.audio

import com.ainirobot.common.utils.DateUtil.dateFormat
import java.io.File
import java.io.FileOutputStream

object PCMAudioWriter {

    /**
     * 录音最大时长，单位毫秒
     */
    private const val RECORD_MAX__DURATION = 600000L

    private val audioDir = File("/sdcard/audio/pcm")
    private var audioFile: File? = null
    private var startTime = 0L
    @Volatile
    var isRecording = false
        private set

    init {
        if (!audioDir.exists()) {
            audioDir.mkdirs()
        }
    }

    fun startRecord() {
        isRecording = true
    }

    fun stopRecord() {
        isRecording = false
    }

    private fun initRecord(): String {
        startTime = System.currentTimeMillis()
        val fileName = "${System.currentTimeMillis().dateFormat("yyyy-MM-dd_HH-mm-ss.SSS")}.pcm"
        audioFile = File(
            audioDir,
            fileName
        )
        return fileName
    }

    fun write(data: ByteArray, size: Int) {
        if (!isRecording) return
        if (System.currentTimeMillis() - startTime > RECORD_MAX__DURATION) {
            initRecord()
            return
        }
        val file = audioFile ?: return
        val os = if (file.exists()) {
            FileOutputStream(file, true)
        } else {
            FileOutputStream(file, false)
        }
        os.write(data, 0, size)
        os.flush()
        os.close()
    }
}