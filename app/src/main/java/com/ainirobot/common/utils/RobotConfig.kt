package com.ainirobot.common.utils

import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi

object RobotConfig {
    var popupFacePrivacy: Boolean = true
    var companyInFaceWhiteList: Boolean = false

    /**
     * 是否启用了推销模式
     */
    val promoteModeActive: Boolean get() = getRobotSettingInt("promote_mode_active") == 1

    /**
     * 主动搭讪设置开关
     */
    val promoteModeEnableAutoChat: Boole<PERSON> get() = getRobotSettingInt("promote_mode_enable_auto_chat") == 1

    /**
     * 主动搭讪时间间隔
     */
    val promoteModeAutoChatInterval: Int get() = getRobotSettingInt("promote_mode_auto_chat_interval")

    private fun getRobotSettingInt(key: String): Int {
        return try {
            RobotSettingApi.getInstance().getRobotInt(key)
        } catch (e: Exception) { 0 }
    }

}