package com.ainirobot.common.utils

import java.io.StringReader
import java.io.StringWriter
import javax.xml.transform.OutputKeys
import javax.xml.transform.TransformerFactory
import javax.xml.transform.stream.StreamResult
import javax.xml.transform.stream.StreamSource

object TextUtil {

    fun String.formatXml(): String {
        return try {
            // 创建TransformerFactory对象
            val transformerFactory = TransformerFactory.newInstance()
            val transformer = transformerFactory.newTransformer()

            // 设置输出属性，用于美化格式
            transformer.setOutputProperty(OutputKeys.INDENT, "yes")
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4")

            // 读取XML字符串
            val source = StreamSource(StringReader(this))

            // 用于保存结果的StringWriter
            val result = StringWriter()

            // 进行转换并格式化
            transformer.transform(source, StreamResult(result))

            // 返回格式化后的XML字符串
            result.toString()
        } catch (e: Exception) {
            this
        }
    }

    /**
     * 判断字符是否是标点符号
     */
    fun isPunctuation(char: Char): Boolean {
        return char.isDefined() && Character.getType(char).toByte().let {
            it == Character.DASH_PUNCTUATION ||
                    it == Character.START_PUNCTUATION ||
                    it == Character.END_PUNCTUATION ||
                    it == Character.CONNECTOR_PUNCTUATION ||
                    it == Character.OTHER_PUNCTUATION ||
                    it == Character.INITIAL_QUOTE_PUNCTUATION ||
                    it == Character.FINAL_QUOTE_PUNCTUATION
        }
    }

    /**
     * 判断字符是否是英文标点符号
     */
    fun isEnglishPunctuation(char: Char): Boolean {
        return char in setOf(',', '.', '?', '!', ';', ':')
    }

    /**
     * 找出两个字符串中第一个不同的字符的索引
     */
    fun firstDiffIndex(str1: CharSequence, str2: CharSequence): Int {
        val minLength = minOf(str1.length, str2.length)
        for (i in 0 until minLength) {
            if (str1[i] != str2[i]) return i
        }
        return minLength // 如果一个字符串是另一个的前缀，则返回较短字符串的长度
    }
}