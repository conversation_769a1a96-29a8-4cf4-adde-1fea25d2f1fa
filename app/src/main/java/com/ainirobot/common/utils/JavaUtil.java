package com.ainirobot.common.utils;

import android.annotation.SuppressLint;
import android.os.Build;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class JavaUtil {

    @SuppressLint({"SoonBlockedPrivateApi", "PrivateApi", "DiscouragedPrivateApi"})
    public static void hookWebView() {
        int sdkInt = Build.VERSION.SDK_INT;
        try {
            Class<?> factoryClass = Class.forName("android.webkit.WebViewFactory");
            Field field = factoryClass.getDeclaredField("sProviderInstance");
            field.setAccessible(true);
            Object sProviderInstance = field.get(null);
            if (sProviderInstance != null) {
                KLog.INSTANCE.d("sProviderInstance isn't null", "JavaUtil");
                return;
            }
            Method getProviderClassMethod;
            if (sdkInt > 22) { // above 22
                getProviderClassMethod = factoryClass.getDeclaredMethod("getProviderClass");
            } else if (sdkInt == 22) { // method name is a little different
                getProviderClassMethod = factoryClass.getDeclaredMethod("getFactoryClass");
            } else { // no security check below 22
                KLog.INSTANCE.d("Don't need to Hook WebView", "JavaUtil");
                return;
            }
            getProviderClassMethod.setAccessible(true);
            Class<?> providerClass = (Class<?>) getProviderClassMethod.invoke(factoryClass);
            Class<?> delegateClass = Class.forName("android.webkit.WebViewDelegate");
            if (providerClass == null) {
                KLog.INSTANCE.e("providerClass is null", "JavaUtil");
                return;
            }
            Constructor<?> providerConstructor = providerClass.getConstructor(delegateClass);
            providerConstructor.setAccessible(true);
            Constructor<?> declaredConstructor = delegateClass.getDeclaredConstructor();
            declaredConstructor.setAccessible(true);
            sProviderInstance = providerConstructor.newInstance(declaredConstructor.newInstance());
            KLog.INSTANCE.d("sProviderInstance:${sProviderInstance}", "JavaUtil");
            field.set("sProviderInstance", sProviderInstance);
        } catch (Throwable e) {
            KLog.INSTANCE.d("Hook WebView failed", "JavaUtil");
        }
    }

}
