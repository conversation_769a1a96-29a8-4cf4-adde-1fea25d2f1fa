package com.ainirobot.common.utils.location;

import android.content.Context;
import android.util.Log;

import com.baidu.location.BDAbstractLocationListener;
import com.baidu.location.LocationClient;
import com.baidu.location.LocationClientOption;
import com.baidu.location.LocationClientOption.LocationMode;


/**
 * <AUTHOR>
 */
public class LocationService {
    private static final String TAG = LocationService.class.getSimpleName();
    private LocationClient client = null;
    private LocationClientOption mOption, DIYoption;
    private final Object objLock = new Object();

    public LocationService(Context locationContext) {
        synchronized (objLock) {
            if (client == null) {
                try {
                    LocationClient.setAgreePrivacy(true);
                    client = new LocationClient(locationContext);
                    client.setLocOption(getDefaultLocationClientOption());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public boolean registerListener(BDAbstractLocationListener listener) {
        boolean isSuccess = false;
        if (listener != null && client != null) {
            client.registerLocationListener(listener);
            isSuccess = true;
        }
        return isSuccess;
    }

    public void unregisterListener(BDAbstractLocationListener listener) {
        if (listener != null && client != null) {
            client.unRegisterLocationListener(listener);
        }
    }

    /***
     * @return isSuccessSetOption
     */
    public boolean setLocationOption(LocationClientOption option) {
        boolean isSuccess = false;
        if (option != null && client != null) {
            if (client.isStarted())
                client.stop();
            DIYoption = option;
            client.setLocOption(option);
            isSuccess = true;
        }
        return isSuccess;
    }

    public LocationClientOption getOption() {
        return DIYoption;
    }

    /***
     *
     * @return DefaultLocationClientOption
     */
    public LocationClientOption getDefaultLocationClientOption() {
        if (mOption == null) {
            mOption = new LocationClientOption();
            mOption.setLocationMode(LocationMode.Hight_Accuracy);//可选，默认高精度，设置定位模式，高精度，低功耗，仅设备
//            if (OPTIONAL) {
//                mOption.setCoorType("bd09ll");//可选，默认gcj02，设置返回的定位结果坐标系，如果配合百度地图使用，建议设置为bd09ll;
			mOption.setScanSpan(10000);//可选，默认0，即仅定位一次，设置发起定位请求的间隔需要大于等于1000ms才是有效的
            mOption.setIsNeedAddress(true);//可选，设置是否需要地址信息，默认不需要
            mOption.setIsNeedLocationDescribe(true);//可选，设置是否需要地址描述
//                mOption.setNeedDeviceDirect(false);//可选，设置是否需要设备方向结果
//                mOption.setLocationNotify(false);//可选，默认false，设置是否当gps有效时按照1S1次频率输出GPS结果
//                mOption.setIgnoreKillProcess(true);//可选，默认true，定位SDK内部是一个SERVICE，并放到了独立进程，设置是否在stop的时候杀死这个进程，默认不杀死
//                mOption.setIsNeedLocationDescribe(true);//可选，默认false，设置是否需要位置语义化结果，可以在BDLocation.getLocationDescribe里得到，结果类似于“在北京天安门附近”
//                mOption.setIsNeedLocationPoiList(true);//可选，默认false，设置是否需要POI结果，可以在BDLocation.getPoiList里得到
//                mOption.SetIgnoreCacheException(false);//可选，默认false，设置是否收集CRASH信息，默认收集
//            }
        }
        return mOption;
    }

    public void start() {
        synchronized (objLock) {
            if (client != null) {
                if (!client.isStarted()) {
                    client.start();
                    Log.i(TAG, "client start over");
                } else {
                    client.restart();
                    Log.i(TAG, "client restart:");
                }
            } else {
                Log.e(TAG, "client is null");
            }
        }
    }

    public void stop() {
        synchronized (objLock) {
            if (client != null && client.isStarted()) {
                client.stop();
            }
        }
    }

}