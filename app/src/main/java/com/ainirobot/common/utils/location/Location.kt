package com.ainirobot.common.utils.location

import com.google.gson.annotations.SerializedName

data class Location(
    @SerializedName("latitude")
    val latitude: Double,
    @SerializedName("longitude")
    val longitude: Double,
    @SerializedName("country")
    val country: String,
    @SerializedName("province")
    val province: String,
    @SerializedName("city")
    val city: String,
    @SerializedName("district")
    val district: String,
    @SerializedName("town")
    val town: String,
    @SerializedName("street")
    val street: String,
    @SerializedName("describe")
    val describe: String,
    @SerializedName("location_type")
    val locationType: String = "baidu" // Default to baidu location type
)