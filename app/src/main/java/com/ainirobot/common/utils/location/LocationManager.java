package com.ainirobot.common.utils.location;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.ainirobot.common.Config;
import com.ainirobot.common.DataStore;
import com.ainirobot.coreservice.client.ProductInfo;
import com.baidu.location.Address;
import com.baidu.location.BDAbstractLocationListener;
import com.baidu.location.BDLocation;

public class LocationManager {

    public interface OnLocatedListener {

        void onLocationSuccess(String location);
    }

    static final String TAG = LocationManager.class.getSimpleName();

    private final static int FAIL_RETRY_TIMES = 10;
    private int currentRetryTimes;
    private LocationService locationService;
    private Handler handler;
    private volatile OnLocatedListener onLocatedListener;

    public void init(Context context) {
        handler = new Handler(Looper.getMainLooper());
        initLocationService(context);
    }

    private void initLocationService(Context context) {
        Log.i(TAG, "initLocationService");
        locationService = new LocationService(context);
        locationService.setLocationOption(locationService.getDefaultLocationClientOption());
    }

    public void setOnLocatedListener(OnLocatedListener onLocatedListener) {
        Log.i(TAG, "setOnLocatedListener called with: " + onLocatedListener);
        this.onLocatedListener = onLocatedListener;
    }

    public void startLocation() {
        if (locationService == null) {
            Log.w(TAG, "locationService is null");
            return;
        }
        Log.i(TAG, "startLocation called, current listener: " + onLocatedListener);
        currentRetryTimes = 0;
        locationService.registerListener(mListener);
        handler.post(new Runnable() {
            @Override
            public void run() {
                locationService.start();
            }
        });
    }

    public void stopLocation() {
        Log.i(TAG, "stopLocation");
        onStop();
    }

    /***
     * Stop location service
     */
    private void onStop() {
        Log.i(TAG, "onStop");
        if (locationService == null) return;
        Log.i(TAG, "onStop Real stop");
        locationService.unregisterListener(mListener); //注销掉监听
        locationService.stop(); //停止定位服务
    }

    private void locationFail() {
        Log.w(TAG, "locationFail called, currentRetryTimes: " + currentRetryTimes);
        if (++currentRetryTimes > FAIL_RETRY_TIMES) {
            Log.w(TAG, "locationFail wont retry!");
            stopLocation();
            return;
        }
    }

    private void locationSuccess(String latitude, String longitude) {
        Log.i(TAG, "locationSuccess called: latitude:" + latitude + ",longitude:" + longitude);
        stopLocation();
    }

    private final BDAbstractLocationListener mListener = new BDAbstractLocationListener() {
        @Override
        public void onReceiveLocation(BDLocation location) {
            Log.i(TAG, "onReceiveLocation begin, location: " + location);
            if (null != location) {
                int locType = location.getLocType();
                Log.i(TAG, "onReceiveLocation locType: " + locType);
                if (locType != BDLocation.TypeServerError
                        && locType != BDLocation.TypeOffLineLocationFail
                        && locType != BDLocation.TypeCriteriaException
                        && locType != BDLocation.TypeOffLineLocation) {
                    Address locationAddress = location.getAddress();
                    String country = ProductInfo.isOverSea() ? "" : locationAddress.country;
                    String province = ProductInfo.isOverSea() ? "" : locationAddress.province;
                    String city = ProductInfo.isOverSea() ? "" : locationAddress.city;
                    String district = ProductInfo.isOverSea() ? "" : locationAddress.district;
                    String town = ProductInfo.isOverSea() ? "" : locationAddress.town;
                    String street = ProductInfo.isOverSea() ? "" : locationAddress.street;
                    String describe = ProductInfo.isOverSea() ? "" : location.getLocationDescribe();
                    double latitude = location.getLatitude();
                    double longitude = location.getLongitude();
                    locationSuccess(String.valueOf(latitude), String.valueOf(longitude));
                    int locationType;
                    switch (locType) {
                        case BDLocation.TypeNetWorkLocation:
                            locationType = 1;
                            break;
//                        case BDLocation.TypeOffLineLocation:
//                            locationType = 3;
//                            break;
                        case BDLocation.TypeGpsLocation:
                            locationType = 2;
                            break;
                        default:
                            locationType = locType;
                            break;
                    }
                    currentRetryTimes = 0;

                    String sb = "time : " +
                            location.getTime() +
                            "\nerror code : " +
                            locType +
                            "\nlatitude : " +
                            latitude +
                            "\nlontitude : " +
                            longitude +
                            "\ncountry : " +
                            country +
                            "\nprovince : " +
                            province +
                            "\ncity : " +
                            city +
                            "\ndistrict : " +
                            district +
                            "\ntown : " +
                            town +
                            "\nstreet : " +
                            street +
                            "\ndescribe : " +
                            describe;
                    Log.i(TAG, sb);
                    final String finalLocation = Config.INSTANCE.getGson().toJson(
                            new Location(
                                    latitude,
                                    longitude,
                                    country,
                                    province,
                                    city,
                                    district,
                                    town,
                                    street,
                                    describe,
                                    "baidu" // Explicitly setting location type to baidu
                            )
                    );
                    Log.i(TAG, "Location JSON: " + finalLocation);
//                    final String finalLocation = province + city + district + town + describe;
                    DataStore.INSTANCE.setGeoLocation(finalLocation);
                    Log.i(TAG, "DataStore.geoLocation is set, current listener: " + onLocatedListener);
                    if (onLocatedListener != null) {
                        Log.i(TAG, "Calling onLocationSuccess with listener: " + onLocatedListener);
                        onLocatedListener.onLocationSuccess(finalLocation);
                        Log.i(TAG, "onLocationSuccess called");
                    } else {
                        Log.w(TAG, "onLocatedListener is null, cannot report location");
                    }
                } else {
                    Log.w(TAG, "locType is " + locType);
                    locationFail();
                }
            } else {
                Log.w(TAG, "location is null");
            }
        }

        /**
         * 回调定位诊断信息，开发者可以根据相关信息解决定位遇到的一些问题
         * 自动回调，相同的 diagnosticType 只会回调一次
         *
         * @param locType           当前定位类型
         * @param diagnosticType    诊断类型（1~9）
         * @param diagnosticMessage 具体的诊断信息释义
         */
        @Override
        public void onLocDiagnosticMessage(int locType, int diagnosticType, String diagnosticMessage) {
            Log.e(TAG, "onLocDiagnosticMessage: locType=" + locType
                    + " diagnosticType=" + diagnosticType
                    + " diagnosticMessage=" + diagnosticMessage);
        }
    };

    private LocationManager() {
    }

    private static class Holder {
        private static final LocationManager INSTANCE = new LocationManager();
    }

    public static LocationManager getInstance() {
        return Holder.INSTANCE;
    }
}
