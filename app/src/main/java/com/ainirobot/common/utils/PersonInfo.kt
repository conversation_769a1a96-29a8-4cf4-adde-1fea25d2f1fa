package com.ainirobot.common.utils

data class PersonInfo(
    val age: Int,
    val angle: Int,
    val angleInView: Double,
    val bodyX: Int,
    val bodyY: Int,
    val bodyheight: Int,
    val bodywidth: Int,
    val distance: Double,
    val faceAngleX: Double,
    val faceAngleY: Double,
    val faceX: Int,
    val faceY: Int,
    val faceheight: Int,
    val facewidth: Int,
    val fake_face: Boolean,
    val gender: String,
    val glasses: Int,
    val headSpeed: Int,
    val id: Int,
    val isNewUser: <PERSON><PERSON>an,
    val isStaff: Boolean,
    val latency: Int,
    val mouthmove_score: Double,
    val mouthstate: Int,
    val name: String,
    val other_face: <PERSON><PERSON><PERSON>,
    val role_id: Int,
    val timestamp: Long,
    val ukfBodyOmega: Double,
    val ukfBodyVel: Double,
    val with_body: Boolean,
    val with_face: <PERSON><PERSON><PERSON>
)
