package com.ainirobot.common.utils

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import com.ainirobot.agent.R
import com.ainirobot.bridge.executor.impl.SendMessageExecutor.PictureData
import com.ainirobot.bridge.executor.impl.SendMessageExecutor.PictureFile
import com.ainirobot.common.Config.gson
import com.ainirobot.common.utils.KLog.e
import com.ainirobot.coreservice.client.SystemApi
import com.ainirobot.coreservice.client.listener.CommandListener
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.suspendCancellableCoroutine
import livekit.org.webrtc.ContextUtils
import java.io.File
import java.io.FileOutputStream

object ImageUtil {

    /**
     * 拍照并上传图片，返回图片地址
     * @param scale 缩放比例
     * @param canAddBg 是否添加背景
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun takePictureSync(scale: Int, canAddBg: Boolean): Pair<String?, String?> {
        return suspendCancellableCoroutine { continuation ->
            takePicture(scale, canAddBg) { path, url ->
                continuation.resume(Pair(path, url)) { e ->
                    e.e("ImageUtil")
                }
            }
        }
    }

    /**
     * 拍照并上传图片，返回图片地址
     * @param scale 缩放比例
     * @param canAddBg 是否添加背景
     * @param callback 回调
     */
    fun takePicture(scale: Int, canAddBg: Boolean, callback: (path: String?, url: String?) -> Unit) {
        try {
            val takePictureStatus =  SystemApi.getInstance().takePicture(
                (System.currentTimeMillis() % 1000000).toInt(),
                "",
                object : CommandListener() {

                    override fun onResult(result: Int, message: String?, extraData: String?) {
                        KLog.d("takePicture result: $result, message: $message, extraData: $extraData", "ImageUtil")
                        if (message.isNullOrEmpty()) {
                            callback.invoke(null, null)
                        } else {
                            var picturePath = gson.fromJson(message, PictureFile::class.java)?.pictures?.get(0)
                            if (picturePath.isNullOrEmpty()) {
                                callback.invoke(null, null)
                            } else {
                                if (scale > 0) {
                                    picturePath = scaleImage(picturePath, scale, canAddBg)
                                }
                                val uploadFileStatus = SystemApi.getInstance().uploadFile(
                                    (System.currentTimeMillis() % 1000000).toInt(),
                                    picturePath,
                                    "aios_rpim_image",
                                    object : CommandListener() {
                                        override fun onResult(result: Int, message: String?, extraData: String?) {
                                            KLog.d("uploadFile result: $result, message: $message, extraData: $extraData", "ImageUtil")
                                            if (message.isNullOrEmpty()) {
                                                callback.invoke(picturePath,null)
                                            } else {
                                                try {
                                                    val pictureData = gson.fromJson(message, PictureData::class.java)
                                                    KLog.d("uploadFile pictureData: $pictureData", "ImageUtil")
                                                    callback.invoke(picturePath, pictureData?.data?.url)
                                                } catch (e: Exception) {
                                                    e.e("ImageUtil")
                                                    callback.invoke(picturePath, null)
                                                }
                                            }
                                        }
                                    }
                                )
                                if (uploadFileStatus < 0) {
                                    callback.invoke(picturePath, null)
                                }
                            }
                        }
                    }
                }
            )
            if (takePictureStatus < 0) {
                KLog.d("takePicture failed takePictureStatus: $takePictureStatus", "ImageUtil")
                callback.invoke(null, null)
            }
        } catch (e: Exception) {
            e.e("ImageUtil")
            callback.invoke(null, null)
        }
    }

    fun scaleImage(filePath: String, scale: Int, canAddBg: Boolean): String {
        if (!File(filePath).exists()) {
            return filePath
        }
        var bitmap = BitmapFactory.decodeFile(filePath, BitmapFactory.Options().also { it.inSampleSize = scale })
        if (canAddBg) {
//            bitmap = addBackground(bitmap, 0xFFEBECED.toInt())
            bitmap = addBackgroundWithQRCodeBeside(bitmap, Color.WHITE)
        }
        return saveBitmapToFile(bitmap, filePath)
    }

    private fun addBackground(originalBitmap: Bitmap, color: Int): Bitmap {
        val width = originalBitmap.width * 3
        val height = originalBitmap.height * 3
        val resultBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(resultBitmap)
        canvas.drawColor(color)
        canvas.drawBitmap(originalBitmap, 0f, 0f, null)
        return resultBitmap
    }

    private fun saveBitmapToFile(bitmap: Bitmap, filePath: String): String {
        val file = File(filePath)
        val temp = File("${filePath}.tmp")
        if (temp.exists()) temp.delete()
        if (file.exists()) file.renameTo(temp)
        return try {
            val outputStream = FileOutputStream(file)
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream)
            outputStream.flush()
            outputStream.close()
            temp.delete()
            file.absolutePath
        } catch (e: Exception) {
            e.e("ImageUtil")
            file.delete()
            temp.renameTo(file)
            file.absolutePath
        }
    }

    private fun addBackgroundWithQRCodeBeside(originalBitmap: Bitmap, color: Int): Bitmap {
        // 获取应用的 Context
        val context = ContextUtils.getApplicationContext()
        
        // 从 drawable 读取二维码图片
        val qrCodeBitmapOriginal = BitmapFactory.decodeResource(context.resources, R.drawable.qrcode_group)

        // 缩小二维码为原来的一半
        val qrCodeBitmap = Bitmap.createScaledBitmap(
            qrCodeBitmapOriginal,
            qrCodeBitmapOriginal.width / 2,
            qrCodeBitmapOriginal.height / 2,
            true
        )

        // 缩放二维码的高度与原始图片一致
        val scaledQRCodeBitmap = Bitmap.createScaledBitmap(
            qrCodeBitmap,
            (qrCodeBitmap.width * originalBitmap.height) / qrCodeBitmap.height,
            originalBitmap.height,
            true
        )

        // 创建一个宽度为原始图片宽度的3倍，高度为原始图片高度的背景
        val backgroundWidth = originalBitmap.width * 2
        val backgroundHeight = originalBitmap.height
        val backgroundBitmap = Bitmap.createBitmap(backgroundWidth, backgroundHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(backgroundBitmap)
        canvas.drawColor(color)

        // 将原始图片绘制在背景的左侧
        val leftOriginal = 0f
        val topOriginal = 0f
        canvas.drawBitmap(originalBitmap, leftOriginal, topOriginal, null)

        // 绘制缩放后的二维码在原始图片的右侧
        val leftQRCode = originalBitmap.width.toFloat()
        val topQRCode = 0f
        canvas.drawBitmap(scaledQRCodeBitmap, leftQRCode, topQRCode, null)

        return backgroundBitmap
    }
}
