package com.ainirobot.common.utils

import android.graphics.Color
import com.ainirobot.agent.core.AudioInfo
import com.ainirobot.agent.core.CommonMessage
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.agent.core.SpeechPlayer
import com.ainirobot.common.Config.gson
import com.ainirobot.common.DataStore
import com.ainirobot.common.debug.CaptionWindow
import com.ainirobot.common.debug.RobotConsole
import com.google.gson.Gson
import java.lang.ref.WeakReference
import com.ainirobot.common.Config.ioScope
import kotlinx.coroutines.launch
import org.json.JSONException
import org.json.JSONObject

interface DataObserver {
    fun onDataChanged(key: String, data: Any)
}

object MessageSenderUtil {

    /**
     * 不能直接限制16K，原因是Json格式化后会添加转义符，导致实际数据量超过阈值；
     */
    private const val MAX_DATA_PACKET_SIZE = 10000

    private val observers = mutableListOf<WeakReference<DataObserver>>()

    fun sendAgentMessage(type: String?, message: String?) {
        var msg = message ?: ""
        RobotConsole.print("sendAgentMessage: $type")
        KLog.d("sendAgentMessage 详细信息 - 类型: '$type', 消息内容: '$msg'", "AgentSkillServer")
        
        if ("webview_dom" == type || "ChatVideoPlayerView" == type) {
            val byteArray = msg.toByteArray(Charsets.UTF_8)
            if (byteArray.size > MAX_DATA_PACKET_SIZE) {
                msg = String(byteArray.copyOf(MAX_DATA_PACKET_SIZE), Charsets.UTF_8)
            }
            MessageSender.sendMessage(
                CommonMessage(
                    name = "state.robot_state",
                    content = mapOf(
                        "interface_state" to mapOf(
                            "interface_info" to msg
                        )
                    )
                ),
                isSafety = false
            )
        } else if ("anchor" == type) {
            val byteArray = msg.toByteArray(Charsets.UTF_8)
            if (byteArray.size > MAX_DATA_PACKET_SIZE) {
                msg = String(byteArray.copyOf(MAX_DATA_PACKET_SIZE), Charsets.UTF_8)
            }
            MessageSender.sendMessage(
                CommonMessage(
                    name = "state.robot_state",
                    content = mapOf(
                        "interface_state" to mapOf(
                            "clickable_elements" to msg
                        )
                    )
                ),
                isSafety = false
            )
        }  else if ("robot_current_mapInfo" == type) {
            KLog.d("robot_current_mapInfo====: $msg", "AgentSkillServer")
            val jsonObject = JSONObject(msg)
            DataStore.mapInfo = msg
            MessageSender.sendMessage(
                CommonMessage(
                    name = "state.robot_state",
                    content = mapOf(
                        "map_id" to jsonObject.optString("map_id", ""),
                        "map_name" to jsonObject.optString("map_name", ""),
                    )
                ),
                isSafety = false
            )
        }else if ("indoor_location" == type) {
            MessageSender.sendMessage(
                CommonMessage(
                    name = "state.robot_state",
                    content = mapOf(
                        "indoor_location" to msg
                    )
                )
            )
        } else if("face_recognition" == type){
            val gson = Gson()
            val message = gson.fromJson(msg, FaceRecognitionMessage::class.java)

            // 打印日志
            KLog.d("GenerateWelcome face_recognition: Name: ${message.name}, ID: ${message.id}, TTS: ${message.tts} UUID: ${message.uuid}", "AgentSkillServer")

            MessageSender.sendMessage(
                CommonMessage(
                    name = "cmd",
                    content = mapOf(
                        "command" to "sync_face-info",
                        "kwargs" to mapOf(
                            "image_id" to message.uuid,
                            "user_name" to message.name,
                            "face_id" to message.id,
                            "personal_welcome_message" to message.tts
                        )
                    )
                )
            )

            MessageSender.sendMessage(
                CommonMessage(
                    name = "state.robot_state",
                    content = mapOf(
                        "face_id" to message.id
                    )
                )
            )
        } else if ("take_picture" == type) {
            KLog.d("GenerateWelcome take_picture: $msg", "AgentSkillServer")
            handleTakePictureEvent(msg)
        } else if ("update_faceid" == type) {
            val gson = Gson()
            val message = gson.fromJson(msg, FaceRecognitionMessage::class.java)

            // 打印日志
            KLog.d("GenerateWelcome face_recognition: Name: ${message.name}, ID: ${message.id}, UUID: ${message.uuid}", "AgentSkillServer")

            MessageSender.sendMessage(
                CommonMessage(
                    name = "state.robot_state",
                    content = mapOf(
                        "face_id" to message.id
                    )
                )
            )

        } else if("set_caption_window_show" == type) {
            val data = Gson().fromJson(msg, WindowShowInfo::class.java)
            KLog.d("setCaptionWindowShow show: ${data.show}", "AgentSkillServer")
            CaptionWindow.enable = data.show
        } else if("robot_status" == type) {
            val data = Gson().fromJson(msg, RobotStatus::class.java)
            KLog.d("robot_status visionStatus: ${data.visionStatus}", "AgentSkillServer")
            notifyDataChanged("robot_vision_enable", data.visionStatus)
        } else if("robot_motion_status" == type) {
            val data = Gson().fromJson(msg, RobotMotionStatus::class.java)
            KLog.d("robot_motion_status motionType: ${data.motionType} ,motionStatus: ${data.motionStatus}", "AgentSkillServer")
            notifyDataChanged("robot_motion_status", data)
        } else if("agent_id" == type) {
            val agentId = msg
            KLog.d("agent_id: $agentId", "AgentSkillServer")
            // 如果agent切换则清除聊天记录
            if (agentId != DataStore.currentAgentId) {
                MessageSender.sendMessage(
                    CommonMessage(
                        name = "cmd",
                        content = mapOf("command" to "clear_history")
                    ),
                    false
                )
            }
            DataStore.currentAgentId = agentId
            MessageSender.sendMessage(
                CommonMessage(
                    name = "state.robot_state",
                    content = mapOf(
                        "agent_id" to agentId
                    )
                )
            )
        } else if("answer_question_from_vision" == type) {
            var planId = ""
            var actionId = ""
            var runId = ""
            var eventName = "event.name"
            var originalActionName = ""
            var question = ""
            var userQuery = ""
            var currenySummary = ""

            try {
                val jsonObject = JSONObject(msg)
                planId = jsonObject.optString("plan_id", "")
                actionId = jsonObject.optString("action_id", "")
                runId = jsonObject.optString("run_id", "")
                eventName = jsonObject.optString("action", "event.name")
                originalActionName = jsonObject.optString("original_action_name", "")
                question = jsonObject.optString("question", "")
                userQuery = jsonObject.optString("_USER_QUERY", "")
                currenySummary = jsonObject.optString("_CURRENT_SUMMARY", "")
            } catch (e: JSONException) {
                KLog.e("Failed to parse JSON: ${e.message}", "AgentSkillServer")
                RobotConsole.print("answer_question_from_vision Failed to parse JSON: ${e.message}")
            } catch (e: Exception) {
                KLog.e("Error in answer_question_from_vision: ${e.message}", "AgentSkillServer")
                RobotConsole.print("answer_question_from_vision error: ${e.message}")
            }

            KLog.d("answer_question_from_vision params - planId: $planId, actionId: $actionId, runId: $runId, eventName: $eventName", "AgentSkillServer")

            ImageUtil.takePicture(1, false) { _, url ->
                if (url.isNullOrEmpty()) {
                    KLog.d("answer_question_from_vision takePicture failed", "AgentSkillServer")
                    RobotConsole.print("takePicture failed", Color.RED)
                    return@takePicture
                }

                KLog.d("answer_question_from_vision takePicture succeeded for URL: $url", "AgentSkillServer")

                ioScope.launch {
                    val result = MessageSender.executeActionSync(
                        eventName,
                        mapOf(
                            "image_url" to url,
                            "original_action_name" to originalActionName,
                            "question" to question,
                            "_USER_QUERY" to userQuery,
                            "_CURRENT_SUMMARY" to currenySummary,
                        ),
                        planId = planId,
                        actionId = actionId,
                        runId = runId,
                        timeoutMillis = 20000,
                        isSafety = false
                    )?.result
                    val audioResult = result?.get("audio_result")
                    if (audioResult != null
                        && audioResult is Map<*, *>
                        && audioResult.isNotEmpty()) {
                        SpeechPlayer.playSync(
                            AudioInfo(
                                isStream = audioResult["stream"] as Boolean,
                                text = audioResult["text"] as? String,
                                streamConfig = mapOf(
                                    "messages" to audioResult["messages"],
                                    "llm_config" to audioResult["llm_config"]
                                )
                            )
                        )
                    }
                }

            }
        } else if("enable_qa" == type) {
            try {
                KLog.d("GenerateWelcome enable_qa: $msg", "AgentSkillServer")
                if (!msg.isNullOrEmpty()) {
                    val jsonObject = JSONObject(msg)
                    val enableQa = jsonObject.optBoolean("enable_qa", false)
                    MessageSender.sendMessage(
                        CommonMessage(
                            name = "state.robot_state",
                            content = mapOf(
                                "enable_qa" to enableQa
                            )
                        )
                    )
                } else {
                    KLog.e("enable_qa message is null or empty", "AgentSkillServer")
                }
            } catch (e: Exception) {
                KLog.e("Failed to parse enable_qa message: ${e.message}", "AgentSkillServer")
            }
        } else {
            // 记录未处理的消息类型
            KLog.d("未处理的消息类型: '$type', 内容: '$msg'", "AgentSkillServer")
        }
    }

    private fun handleTakePictureEvent(msg: String) {
        var uuid: String? = null
        try {
            val message = gson.fromJson(msg, FaceRecognitionMessage::class.java)
            uuid = message.uuid
        } catch (e: Exception) {
            KLog.e("Error handling take_picture event: ${e.message}", "TakePictureUtil")
            e.printStackTrace()
        }

        if (uuid.isNullOrEmpty()) {
            return
        }

        ImageUtil.takePicture(1, false) { _, url ->
            if (url.isNullOrEmpty()) {
                KLog.d("GenerateWelcome takePicture failed for UUID: $uuid", "TakePictureUtil")
                RobotConsole.print("takePicture failed", Color.RED)
                return@takePicture
            }

            KLog.d("GenerateWelcome takePicture succeeded for UUID: $uuid. URL: $url", "TakePictureUtil")
            MessageSender.sendMessage(
                CommonMessage(
                    name = "cmd",
                    content = mapOf(
                        "command" to "generate_welcome",
                        "kwargs" to mapOf(
                            "image_url" to url,
                            "image_id" to uuid
                        )
                    )
                )
            )
        }
    }

    private fun notifyDataChanged(key: String, data: Any) {
        synchronized(observers) {
            observers.forEach {
                it.get()?.onDataChanged(key, data)
            }
        }
    }

    fun registerObserver(observer: DataObserver) {
        synchronized(observers) {
            observers.add(WeakReference(observer))
        }
    }

    fun unregisterObserver(observer: DataObserver) {
        synchronized(observers) {
            observers.removeIf { it.get() == observer }
        }
    }
}