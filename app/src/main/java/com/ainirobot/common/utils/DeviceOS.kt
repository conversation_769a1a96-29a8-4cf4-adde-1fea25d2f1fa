package com.ainirobot.common.utils

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioManager
import android.os.BatteryManager
import android.provider.Settings
import com.ainirobot.agent.R
import com.ainirobot.common.Config
import com.ainirobot.common.Config.gson
import com.ainirobot.common.DataStore
import com.ainirobot.common.utils.KLog.e
import com.ainirobot.coreservice.client.RobotSettings
import com.ainirobot.coreservice.client.SettingsUtil
import com.ainirobot.coreservice.client.SystemApi
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi
import com.ainirobot.coreservice.client.speech.entity.LangParamsEnum
import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig.TtsConfig
import com.google.gson.reflect.TypeToken
import java.util.TimeZone

object DeviceOS {

    const val ROBOT_SETTING_SPEAKER_ROLE = "robot_setting_speaker_role"

    /**
     * 设备最大屏幕亮度
     */
    const val MAX_SCREEN_BRIGHTNESS = 255

    val deviceId: String
        get() = RobotSettings.getSystemSn()

    /**
     * 机器人名字
     */
    val robotName: String get() = RobotSettings.getRobotName(Config.context)

    /**
     * 企业Id
     */
    val enterpriseId: String get() = getSettingString(RobotSettings.SETTINGS_GLOBAL_VOICE_CORP_ID)

    /**
     * 场景Id
     */
    val groupId: String get() = getSettingString(RobotSettings.SETTINGS_GLOBAL_VOICE_GROUP_ID)

    /**
     * 业务线Id
     */
    val clientId: String get() = getSettingString(RobotSettings.SETTINGS_GLOBAL_VOICE_CLIENT_ID)

    /**
     * 音量等级
     */
    private lateinit var volumeLevels: IntArray

    /**
     * 最大音量等级
     */
    val maxVolumeLevel: Int get() = volumeLevels.size - 1

    private val statusMonitor = object : BroadcastReceiver() {

        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                Intent.ACTION_BATTERY_CHANGED -> {
                    val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0)
                    val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, 0)
                    if (scale > 0) {
                        val batteryPct = (level * 100.0f / scale).toInt()
                        if (DataStore.batteryLevel != batteryPct) {
                            DataStore.batteryLevel = batteryPct
                        }
                    }
                }
            }
        }
    }

    private fun getSettingString(key: String): String {
        return try {
            SettingsUtil.getString(Config.context, key)
        } catch (e: Exception) { "" }
    }

    fun initialize() {
        volumeLevels = Config.context.resources.getIntArray(R.array.music_volume_steps)
        Config.context.registerReceiver(statusMonitor, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
    }

    fun destroy(context: Context) {
        context.unregisterReceiver(statusMonitor)
    }

    /**
     * 获取产品model，如：
     */
    val productMode: String
        get() = RobotSettings.getProductModel()

    /**
     * 获取语速
     */
    val speechRate: Int
        get() = RobotSettingApi.getInstance().getRobotInt(SettingsUtil.ROBOT_SETTING_SPEECH_SPEED_INT)

    /**
     * 获取是否支持多语言自动识别
     */
    val multiLingual: Int
        get() = RobotSettingApi.getInstance().getRobotInt(SettingsUtil.ROBOT_SETTING_VOICE_SUPPORT_MULTILINGUAL)

    /**
     * 获取VAD截断超时时间
     */
    val vadEndInt: Int
        get() = RobotSettingApi.getInstance().getRobotInt(SettingsUtil.ROBOT_SETTING_VAD_END_INT)

    /**
     * 获取发言人
     */
    val spokesman: String
        get() = RobotSettingApi.getInstance().getRobotString(ROBOT_SETTING_SPEAKER_ROLE)

    /**
     * 设置是否开启静止模式
     */
    var staticModeEnable: Boolean
        get() = RobotSettingApi.getInstance().getRobotInt("robot_static_mode_switch") == 1
        set(value) {
            RobotSettingApi.getInstance().setRobotInt("robot_static_mode_switch", if (value) 1 else 0)
        }

    fun getVolumeLevel(context: Context): Int {
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        val volume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)
        return volume2Level(volume)
    }

    private fun volume2Level(volume: Int): Int {
        for (i in volumeLevels.indices) {
            if (volume <= volumeLevels[i]) {
                return i
            }
        }
        return if (volume < 0) 0 else volumeLevels[volumeLevels.size - 1]
    }

    fun setVolumeLevel(context: Context, level: Int) {
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        audioManager.setStreamVolume(
            AudioManager.STREAM_MUSIC,
            volumeLevels[level.coerceIn(0, maxVolumeLevel)],
            0
        )
    }

    fun getScreenBrightness(context: Context): Int {
        return try {
            Settings.System.getInt(
                context.contentResolver,
                Settings.System.SCREEN_BRIGHTNESS
            )
        } catch (e: Settings.SettingNotFoundException) {
            e.e("DeviceOS")
            -1
        }
    }

    /**
     * 获取机器人速度
     * @return Pair<Float, Float> 第一个参数为当前速度，第二个参数为目标速度
     */
    fun getSpeed(): Pair<Float, Float>? {
        return try {
            val json = SystemApi.getInstance().lineSpeed
            KLog.d("Fetch robot speed: $json, DeviceId: $deviceId", "DeviceOS")
            if (json.isNullOrEmpty() || !json.startsWith("{")) {
                return null
            }
            val data = gson.fromJson<Map<String, String>>(
                json,
                object : TypeToken<Map<String, String>>() {}.type
            )
            val cs = data["linear_speed_x"]
            val ts = data["linear_speed_expect_x"]
            Pair(
                if (cs.isNullOrEmpty()) 0f else cs.toFloat(),
                if (ts.isNullOrEmpty()) 0f else ts.toFloat()
            )
        } catch (e: Exception) {
            e.e("DeviceOS")
            null
        }
    }

    /**
     * 获取时区
     */
    fun getTimezone(): String {
        return TimeZone.getDefault().id
    }

    /**
     * 获取语言
     */
    val language: LangParamsEnum get() = TtsConfig.getLangParams()

}
