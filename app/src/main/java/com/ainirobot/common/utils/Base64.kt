package com.ainirobot.common.utils

object Base64 {
    private const val ALPHABET = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
    private const val PADDING = '='

    /**
     * 将ByteArray编码为Base64字符串
     * @param input 要编码的字节数组
     * @return Base64编码后的字符串
     */
    fun encode(input: ByteArray): String {
        val output = StringBuilder()
        var padding = 0
        var position = 0

        while (position < input.size) {
            var b1 = input[position].toInt() and 0xFF
            var b2 = if (position + 1 < input.size) input[position + 1].toInt() and 0xFF else 0
            var b3 = if (position + 2 < input.size) input[position + 2].toInt() and 0xFF else 0

            val triple = (b1 shl 16) + (b2 shl 8) + b3

            output.append(ALPHABET[(triple shr 18) and 0x3F])
            output.append(ALPHABET[(triple shr 12) and 0x3F])

            if (position + 1 < input.size) {
                output.append(ALPHABET[(triple shr 6) and 0x3F])
            } else {
                output.append(PADDING)
                padding++
            }

            if (position + 2 < input.size) {
                output.append(ALPHABET[triple and 0x3F])
            } else {
                output.append(PADDING)
                padding++
            }

            position += 3
        }

        return output.toString()
    }

    /**
     * 将Base64字符串解码为ByteArray
     * @param input Base64编码的字符串
     * @return 解码后的字节数组
     */
    fun decode(input: String): ByteArray {
        // 创建反向查找表
        val reverseAlphabet = IntArray(128) { -1 }
        for (i in ALPHABET.indices) {
            reverseAlphabet[ALPHABET[i].code] = i
        }

        // 计算实际输出长度
        val inputLength = input.length
        var paddingCount = 0
        if (input.endsWith("==")) paddingCount = 2
        else if (input.endsWith("=")) paddingCount = 1

        val outputLength = (inputLength * 3) / 4 - paddingCount
        val result = ByteArray(outputLength)
        var outputIndex = 0

        // 每次处理4个字符
        var buffer = 0
        var bufferLength = 0

        for (c in input) {
            if (c == PADDING) break

            val value = if (c.code < 128) reverseAlphabet[c.code] else -1
            if (value == -1) continue  // 跳过非法字符

            buffer = (buffer shl 6) or value
            bufferLength += 6

            if (bufferLength >= 8) {
                bufferLength -= 8
                result[outputIndex++] = (buffer shr bufferLength).toByte()
            }
        }
        return result
    }
}