package com.ainirobot.common.utils

import com.elvishew.xlog.LogLevel
import com.elvishew.xlog.XLog

class IntervalPrinter(
    private val tag: String,
    private val interval: Int
) {

    private var lastTime = 0L

    fun d(message: String) {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastTime > interval) {
            lastTime = currentTime
            KLog.d(message, tag)
        }
    }
}

object KLog {

    private const val TAG = "AgentService"

    private var level: Int = 0

    fun initialize(level: Int = LogLevel.ALL) {
        XLog.init(level)
        KLog.level = level
    }

    fun String.d(
        tag: String = "",
        before: (() -> String)? = null,
        after: (() -> String)? = null
    ): String {
        if (level <= LogLevel.DEBUG) {
            val newTag = if (tag.isEmpty()) tag else "$tag "
            when {
                before != null && after != null -> {
                    XLog.tag(TAG).d("$newTag${before.invoke()}$this${after.invoke()}")
                }
                before != null -> {
                    XLog.tag(TAG).d("$newTag${before.invoke()}$this")
                }
                after != null -> {
                    XLog.tag(TAG).d("$newTag$this${after.invoke()}")
                }
                else -> {
                    XLog.tag(TAG).d("$newTag$this")
                }
            }
        }
        return this
    }

    fun <T> T.d(
        tag: String = "",
        before: (() -> String)? = null,
        after: (() -> String)? = null
    ): T {
        if (level <= LogLevel.DEBUG) {
            val newTag = if (tag.isEmpty()) tag else "$tag "
            when {
                before != null && after != null -> {
                    XLog.tag(tag).d("$newTag${before.invoke()}$this${after.invoke()}")
                }
                before != null -> {
                    XLog.tag(tag).d("$newTag${before.invoke()}$this")
                }
                after != null -> {
                    XLog.tag(tag).d("$newTag$this${after.invoke()}")
                }
                else -> {
                    XLog.tag(tag).d("$newTag$this")
                }
            }
        }
        return this
    }

    fun Throwable.e(
        tag: String = "",
        before: (() -> String)? = null,
        after: (() -> String)? = null
    ): Throwable {
        if (level <= LogLevel.ERROR) {
            val newTag = if (tag.isEmpty()) tag else "$tag "
            when {
                before != null && after != null -> {
                    XLog.tag(TAG).e("$newTag${before.invoke()}${after.invoke()}", this)
                }
                before != null -> {
                    XLog.tag(TAG).e("$newTag${before.invoke()}", this)
                }
                after != null -> {
                    XLog.tag(TAG).e("$newTag${after.invoke()}", this)
                }
                else -> {
                    XLog.tag(TAG).e("$newTag$message",this)
                }
            }
        }
        return this
    }

    fun d(message: String, tag: String = "") {
        if (level <= LogLevel.DEBUG) {
            val newTag = if (tag.isEmpty()) tag else "$tag "
            XLog.tag(TAG).d("$newTag$message")
        }
    }

    fun json(message: String, tag: String = "") {
        if (level <= LogLevel.DEBUG) {
            XLog.tag(if (tag.isEmpty()) TAG else "$TAG $tag").json(message)
        }
    }

    fun json(tag: String = "", info: (() -> String)) {
        if (level <= LogLevel.DEBUG) {
            XLog.tag(if (tag.isEmpty()) TAG else "$TAG $tag").json(info.invoke())
        }
    }

    fun e(message: String, tag: String = "") {
        if (level <= LogLevel.ERROR) {
            val newTag = if (tag.isEmpty()) tag else "$tag "
            XLog.tag(TAG).e("$newTag$message")
        }
    }
}