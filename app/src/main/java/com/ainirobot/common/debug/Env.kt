package com.ainirobot.common.debug

import android.app.AlertDialog
import android.content.Context
import android.os.Build
import android.os.PowerManager
import android.view.WindowManager
import android.widget.Toast
import com.ainirobot.agent.R
import com.ainirobot.common.runOnMain
import com.ainirobot.common.utils.KLog
import com.ainirobot.coreservice.client.Definition
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi
import com.ainirobot.speechasrservice.utils.SpeechConfig
import kotlinx.coroutines.delay

/**
 * 环境切换，用于切换Robot系统相关请求服务的正式、开发、灰度环境；
 * 主线：正式0、测试1、灰度2
 */
object Env {
    /**
     * 正式环境
     */
    private const val RELEASE: Int = 0

    /**
     * 测试环境
     */
    private const val TEST: Int = 1

    /**
     * AgentDEV环境，对应主线的灰度，因主线灰度废弃，所以AgentDEV和测试环境相同
     */
    private const val AGENT_DEV: Int = 2

    private val ENV_TITLE_MAPPING = mapOf(
        RELEASE to R.string.env_release,
        TEST to R.string.env_test,
        AGENT_DEV to R.string.env_dev
    )

    private val ENV_NAMES = arrayOf(
        "STABLE",
        "TEST",
        "AGENT_DEV",
    )

    private var speechConfig: SpeechConfig? = null

    val isInitialized: Boolean
        get() = agentUrl.isNotEmpty()

    val agentEnv: Int get() {
        val envInt = RobotSettingApi.getInstance().getRobotInt(Definition.ROBOT_SETTING_SYSTEM_ENV)
        KLog.d("agentEnv: $envInt", "Env")
        return if (envInt in 0..2) {
            envInt
        } else {
            RELEASE
        }
    }

    val agentUrl: String get() {
        return when (agentEnv) {
            AGENT_DEV -> {
                speechConfig?.preReleaseUrl?.agentUrl
            }
            TEST -> {
                speechConfig?.testUrl?.agentUrl
            }
            else -> {
                speechConfig?.releaseUrl?.agentUrl
            }
        } ?: ""
    }

    val agentEnvName: String
        get() = ENV_NAMES[agentEnv]

    fun initialize(config: SpeechConfig) {
        this.speechConfig = config
    }

    fun showSwitchDialog(context: Context) {
        val builder = AlertDialog.Builder(context)
            .setTitle(R.string.switch_env)
            .setMessage(
                context.getString(
                    R.string.switch_env_message,
                    context.getString(ENV_TITLE_MAPPING[agentEnv] ?: 0)
                )
            )

        builder.setNeutralButton(R.string.cancel) { dialog, _ ->
            dialog.dismiss()
        }
        when (agentEnv) {
            RELEASE -> {
                builder.setPositiveButton(R.string.env_dev) { dialog, _ ->
                    switchEnv(context, AGENT_DEV)
                    dialog.dismiss()
                }
                builder.setNegativeButton(R.string.env_test) { dialog, _ ->
                    switchEnv(context, TEST)
                    dialog.dismiss()
                }
            }
            AGENT_DEV -> {
                builder.setPositiveButton(R.string.env_release) { dialog, _ ->
                    switchEnv(context, RELEASE)
                    dialog.dismiss()
                }
                builder.setNegativeButton(R.string.env_test) { dialog, _ ->
                    switchEnv(context, TEST)
                    dialog.dismiss()
                }
            }
            TEST -> {
                builder.setPositiveButton(R.string.env_release) { dialog, _ ->
                    switchEnv(context, RELEASE)
                    dialog.dismiss()
                }
                builder.setNegativeButton(R.string.env_dev) { dialog, _ ->
                    switchEnv(context, AGENT_DEV)
                    dialog.dismiss()
                }
            }
        }

        val dialog = builder.create()
        // 设置 Window 类型为系统级别
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            dialog.window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY)
        } else {
            dialog.window?.setType(WindowManager.LayoutParams.TYPE_PHONE)
        }
        dialog.show()
    }

    private fun switchEnv(context: Context, env: Int) {
        RobotSettingApi.getInstance().setRobotInt(Definition.ROBOT_SETTING_SYSTEM_ENV, env)
        Toast.makeText(
            context,
            context.getString(
                R.string.switch_env_success,
                context.getString(ENV_TITLE_MAPPING[env] ?: 0)
            ),
            Toast.LENGTH_SHORT
        ).show()
        runOnMain {
            delay(1000)
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            powerManager.reboot("switch_env")
        }
    }
}