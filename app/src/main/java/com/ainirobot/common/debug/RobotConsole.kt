package com.ainirobot.common.debug

import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ainirobot.agent.BuildConfig
import com.ainirobot.agent.R
import com.ainirobot.agent.core.CommonMessage
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.agent.service.FaceEventListener
import com.ainirobot.agent.service.FaceMonitorService
import com.ainirobot.agent.service.VirtualAgent
import com.ainirobot.common.Config
import com.ainirobot.common.ConnectivityAssist
import com.ainirobot.common.DataStore
import com.ainirobot.common.runOnMain
import com.ainirobot.common.utils.DateUtil.dateFormat
import com.ainirobot.common.utils.DeviceOS
import com.ainirobot.common.utils.KLog
import com.lzf.easyfloat.EasyFloat
import com.lzf.easyfloat.enums.ShowPattern
import com.lzf.easyfloat.enums.SidePattern
import java.util.LinkedList

@SuppressLint("StaticFieldLeak")
object RobotConsole : FaceEventListener, DataStore.Callback {

    private const val TAG_CONSOLE = "robot_console"
    private const val TAG_MICRO_CONSOLE = "robot_micro_console"

    private const val MSG_UPDATE_TIMESTAMP = 1001

    private val handler = object : Handler(Looper.getMainLooper()) {

        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_UPDATE_TIMESTAMP -> {
                    if (allowUpdateTimestamp) {
                        timestampView?.text = System.currentTimeMillis().dateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                        sendEmptyMessageDelayed(MSG_UPDATE_TIMESTAMP, 16)
                    }
                }
            }
        }
    }

    private data class Item(
        var text: String,
        val color: Int = Color.WHITE,
        val time: Long = System.currentTimeMillis()
    ) {

        fun format(): String {
            if (time == 0L) {
                return text
            }
            return "${time.dateFormat("yyyy-MM-dd HH:mm:ss.SSS")} $text"
        }
    }

    private class ItemAdapter : RecyclerView.Adapter<ItemAdapter.ItemViewHolder>() {

        private val itemList: MutableList<Item> = LinkedList()

        class ItemViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            val textView = view.findViewById<TextView>(R.id.text_view)!!
        }

        fun add(item: Item, notifyChange: Boolean = true) {
            if (itemList.size > 10000) {
                clear()
                return
            }
            itemList.add(item)
            if (notifyChange) {
                notifyItemInserted(itemList.size - 1)
            }
        }

        fun update(item: Item, notifyChange: Boolean = true) {
            if (itemList.isEmpty()) {
                add(item)
                return
            }
            val last = itemList.last()
            if (last.color != item.color || !item.text.startsWith(last.text)) {
                add(item)
                return
            }
            last.text = item.text
            if (notifyChange) {
                notifyItemChanged(itemList.size - 1)
            }
        }

        @SuppressLint("NotifyDataSetChanged")
        fun clear() {
            itemList.clear()
            notifyDataSetChanged()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.view_item_console, parent, false)
            return ItemViewHolder(view)
        }

        override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
            val item = itemList[position]
            holder.textView.setTextColor(item.color)
            holder.textView.text = item.format()
        }

        override fun getItemCount() = itemList.size
    }

    private lateinit var agent: VirtualAgent
    @Volatile
    private var isShown = false
    private var timestampView: TextView? = null
    private var reconnectBtn: TextView? = null
    private var microphoneBtn: TextView? = null
    private var envView: TextView? = null
    private var faceView: ImageView? = null
    private var stateView: RecyclerView? = null
    private val stateAdapter = ItemAdapter()
    private var consoleView: RecyclerView? = null
    private val consoleAdapter = ItemAdapter()

    private var connectStatusImageView: ImageView? = null

    private var microphoneEnable: Boolean = false
        set(value) {
            field = value
            if (isShown) {
                runOnMain {
                    microphoneBtn?.let {
                        if (value) {
                            it.setText(R.string.microphone_opened)
                            it.setTextColor(Color.WHITE)
                        } else {
                            it.setText(R.string.microphone_closed)
                            it.setTextColor(Color.RED)
                        }
                    }
                }
            }
        }

    private var allowUpdateTimestamp = false

    fun enableReconnectBtn(enable: Boolean) {
        if (isShown) {
            runOnMain {
                reconnectBtn?.isEnabled = enable
            }
        }
    }

    private var lastState: String = ""
    fun changeState(state: String) {
        if (isShown) {
            runOnMain {
                stateAdapter.add(Item(state, Color.WHITE,0L))
                stateView?.scrollToPosition(stateAdapter.itemCount - 1)
            }
        }
        runOnMain {
            when (state) {
                "connected" -> {
                    if (lastState != "subscribed") {
                        connectStatusImageView?.setImageResource(R.drawable.ic_unsubscribed)
                        lastState = state
                    }
                }
                "disconnected", "failed" -> {
                    connectStatusImageView?.setImageResource(R.drawable.ic_disconnected)
                    lastState = state
                }
                "subscribed" -> {
                    connectStatusImageView?.setImageResource(R.drawable.ic_connected)
                    lastState = state
                }
                "unsubscribed" -> {
                    connectStatusImageView?.setImageResource(R.drawable.ic_unsubscribed)
                    lastState = state
                }
            }
        }
    }

    fun print(log: String, color: Int = Color.WHITE, isReplace: Boolean = false) {
        runOnMain {
            val lastIndex = consoleAdapter.itemCount - 1
            if (isReplace) {
                consoleAdapter.update(Item(log, color), isShown)
            } else {
                consoleAdapter.add(Item(log, color), isShown)
            }
            consoleView?.let {
                val lm = it.layoutManager as LinearLayoutManager
                if (lastIndex - lm.findLastVisibleItemPosition() < 10) {
                    it.scrollToPosition(consoleAdapter.itemCount - 1)
                }
            }
        }
    }

    fun initialize(agent: VirtualAgent) {
        this.agent = agent
        FaceMonitorService.registerListener(this)
    }

    fun show() {
        if (isShown || !Config.debugMode) {
            return
        }
        showConsole()
        dismissMicroConsole()
    }

    fun dismiss() {
        if (!isShown) {
            return
        }
        dismissConsole()
        showMicroConsole()
    }

    @SuppressLint("SetTextI18n")
    private fun showConsole() {
        val context = Config.context
        EasyFloat.with(context)
            .setTag(TAG_CONSOLE)
            .setShowPattern(ShowPattern.ALL_TIME)
            .setImmersionStatusBar(true)
            .setDragEnable(false)
            .setGravity(Gravity.TOP or Gravity.END, 165, 3)
            .setLayout(R.layout.float_window_robot_console) {
                it.findViewById<View>(R.id.close_btn).setOnClickListener {
                    dismiss()
                }
                it.findViewById<TextView?>(R.id.timestamp_view)?.let { view ->
                    timestampView = view
                    view.setOnClickListener {
                        allowUpdateTimestamp =!allowUpdateTimestamp
                        if (allowUpdateTimestamp) {
                            handler.sendEmptyMessage(MSG_UPDATE_TIMESTAMP)
                        } else {
                            handler.removeMessages(MSG_UPDATE_TIMESTAMP)
                        }
                    }
                    view.text = System.currentTimeMillis().dateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                }
                reconnectBtn = it.findViewById(R.id.reconnect_btn)
                reconnectBtn?.setOnClickListener {
                    if (ConnectivityAssist.isConnected) {
                        print("手动重连中...")
                        KLog.d("Click reconnect button", "RobotConsole")
                        agent.reconnect()
                    } else {
                        Toast.makeText(context, R.string.network_invalid_tips, Toast.LENGTH_SHORT).show()
                    }
                }
                microphoneBtn = it.findViewById(R.id.microphone_btn)
                microphoneBtn?.setOnClickListener {
                    microphoneEnable = !microphoneEnable
                    DataStore.microphoneMuted = !microphoneEnable
                }
                it.findViewById<RecyclerView?>(R.id.state_list_view)?.let { view ->
                    stateView = view
                    view.layoutManager = LinearLayoutManager(context)
                    view.adapter = stateAdapter
                }
                it.findViewById<RecyclerView>(R.id.console_list_view)?.let { view ->
                    consoleView = view
                    view.layoutManager = LinearLayoutManager(context).also { llm ->
                        view.postDelayed(
                            {
                                llm.scrollToPosition(consoleAdapter.itemCount - 1)
                            },
                            200
                        )
                    }
                    view.adapter = consoleAdapter
                }
                it.findViewById<TextView>(R.id.clear_btn).setOnClickListener {
                    consoleAdapter.clear()
                    stateAdapter.clear()
                    // 清除对话历史记录
                    MessageSender.sendMessage(
                        CommonMessage(
                            name = "cmd",
                            content = mapOf("command" to "clear_history")
                        ),
                        false
                    )
                }
                it.findViewById<TextView>(R.id.env_switch_btn).let { view ->
                    view.setOnClickListener {  Env.showSwitchDialog(context) }
                    envView = view
                }
                it.findViewById<TextView>(R.id.vision_btn).setOnClickListener {
                    startVisionActivity(context)
                }
                it.findViewById<TextView>(R.id.agent_env_view).text = Env.agentEnvName
                it.findViewById<TextView>(R.id.robot_name_view).text = DeviceOS.robotName
                it.findViewById<TextView>(R.id.app_version_view).text =
                    "P: v${BuildConfig.PRODUCT_VERSION}\nC: v${BuildConfig.VERSION_NAME}\nA: ${Config.actionVersion}"
                it.findViewById<ImageView?>(R.id.face_view)?.let { view ->
                    view.visibility = if (FaceMonitorService.isFaceAppear) View.VISIBLE else View.GONE
                    faceView = view
                }
                isShown = true
                microphoneEnable = !DataStore.microphoneMuted
            }.show()
        DataStore.addCallback(this)
    }

    override fun onDataChanged(key: String) {
        if (key == "key_microphone_muted") {
            microphoneEnable = !DataStore.getBoolean(key, false)
        }
    }

    private fun startVisionActivity(context: Context) {
        val intent = Intent("com.ainirobot.datacollection.ACTION_CONFIG").apply {
            setClassName("com.ainirobot.datacollection", "com.ainirobot.datacollection.ConfigActivity")
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        try {
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            e.printStackTrace()
        }
    }

    private fun dismissConsole() {
        DataStore.removeCallback(this)
        EasyFloat.dismiss(TAG_CONSOLE)
        timestampView = null
        reconnectBtn = null
        microphoneBtn = null
        stateView = null
        consoleView = null
        isShown = false
    }

    private fun showMicroConsole() {
        val context = Config.context
        EasyFloat.with(context)
            .setTag(TAG_MICRO_CONSOLE)
            .setShowPattern(ShowPattern.ALL_TIME)
            .setSidePattern(SidePattern.RESULT_SIDE)
            .setImmersionStatusBar(true)
            .setAnimator(null)
            .setDragEnable(false)
            .setGravity(Gravity.TOP or Gravity.END, 165, 0)
            .setLayout(R.layout.float_window_robot_micro_console) {
                it.findViewById<ImageView>(R.id.show_console_bt).let { view ->
                    view.setOnClickListener {
                        show()
                    }
                    when (lastState) {
                        "connected", "unsubscribed" -> view.setImageResource(R.drawable.ic_unsubscribed)
                        "disconnected", "failed" -> view.setImageResource(R.drawable.ic_disconnected)
                        "subscribed" -> view.setImageResource(R.drawable.ic_connected)
                    }
                    connectStatusImageView = view
                }
            }.show()
    }

    private fun dismissMicroConsole() {
        EasyFloat.dismiss(TAG_MICRO_CONSOLE)
        connectStatusImageView = null
    }

    override fun onFaceAppear() {
        runOnMain {
            faceView?.visibility = View.VISIBLE
            KLog.d("Face is appear", "RobotConsole")
            print("Face is appear", Color.GREEN)
        }
    }

    override fun onFaceDisappear() {
        runOnMain {
            faceView?.visibility = View.GONE
            KLog.d("Face is disappear", "RobotConsole")
            print("Face is disappear", Color.RED)
        }
    }
}