package com.ainirobot.common.debug

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.StaticLayout
import android.text.TextUtils
import android.text.method.ScrollingMovementMethod
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.HorizontalScrollView
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import androidx.core.animation.addListener
import com.ainirobot.agent.R
import com.ainirobot.agent.base.Transcription
import com.ainirobot.agent.bus.AgentBus
import com.ainirobot.agent.core.SpeechPlayer
import com.ainirobot.bi.RTQueryTrace
import com.ainirobot.common.utils.DeviceOS
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.TextUtil
import com.ainirobot.common.widget.AnimImageView
import com.ainirobot.common.widget.GradientMarqueeTextView
import com.ainirobot.common.widget.GradientTypewriterView
import com.ainirobot.common.widget.LoadingView
import com.ainirobot.common.widget.StreamTextView
import com.ainirobot.common.widget.TypewriterView
import com.ainirobot.common.widget.WebpTextView
import com.github.penfeizhou.animation.loader.AssetStreamLoader
import com.github.penfeizhou.animation.webp.WebPDrawable
import java.lang.ref.WeakReference


object CaptionWindow {

    interface CaptionObserver {

        fun onCaptionChanged(caption: Transcription)

    }

    private enum class CaptionStatus {
        IDLE,
        LISTENING,
        THINKING,
        USER_SPEAKING,
        AI_SPEAKING,
    }

    private class Segment {
        var transcription: Transcription? = null
        var valid: Boolean = false

        override fun toString(): String {
            return "Segment(transcription=$transcription, valid=$valid)"
        }
    }

    private const val MSG_HIDE_DYNAMIC_ISLAND = 1000
    private const val MSG_HIDE_CAPTION = 1001
    private const val MSG_UPDATE_CAPTION = 1002
    private const val MSG_STATUS_LISTENING = 1003
    private const val MSG_STATUS_THINKING = 1004
    private const val MSG_UPDATE_DYNAMIC_ISLAND_TEXT = 1005
    private const val MSG_UPDATE_AI_PROGRESS_TEXT = 1006
    private const val MSG_PROCESS_NEXT_QUEUE_ITEM = 1007
    private const val MSG_QUERY_BY_TEXT = 1008

    private const val DELAY_TIME = 6000L
    private const val MIN_MESSAGE_DISPLAY_TIME = 400L

    private const val BALL_TARGET_SCALE = 0.75f

    private const val SEPARATOR = "..."

    private lateinit var contextRef: WeakReference<Context>
    private val context: Context? get() {
        return contextRef.get()
    }
    private lateinit var captionLayoutRef: WeakReference<View>
    private val captionLayout: View? get() {
        return captionLayoutRef.get()
    }
    private lateinit var islandLayoutRef: WeakReference<View>
    private val islandLayout: View? get() {
        return islandLayoutRef.get()
    }
    private lateinit var islandLoadingViewRef: WeakReference<LoadingView>
    private val islandLoadingView: LoadingView? get() {
        return islandLoadingViewRef.get()
    }
    private lateinit var userCaptionViewRef: WeakReference<StreamTextView>
    private val userCaptionView: StreamTextView? get() {
        return userCaptionViewRef.get()
    }
    private lateinit var aiCaptionLayoutRef: WeakReference<View>
    private val aiCaptionLayout: View? get() {
        return aiCaptionLayoutRef.get()
    }
    private lateinit var aiCaptionScrollViewRef: WeakReference<ScrollView>
    private val aiCaptionScrollView: ScrollView? get() {
        return aiCaptionScrollViewRef.get()
    }
    private lateinit var aiCaptionViewRef: WeakReference<TextView>
    private val aiCaptionView: TextView? get() {
        return aiCaptionViewRef.get()
    }
    private lateinit var aiProgressContainerRef: WeakReference<HorizontalScrollView>
    private val aiProgressContainer: HorizontalScrollView? get() {
        return aiProgressContainerRef.get()
    }
    private lateinit var aiProgressViewRef: WeakReference<GradientMarqueeTextView>
    private val aiProgressView: GradientMarqueeTextView? get() {
        return aiProgressViewRef.get()
    }
    private val aiProgressViews = mutableListOf<GradientTypewriterView>()

    private lateinit var listeningViewRef: WeakReference<WebpTextView>
    private val listeningView: WebpTextView? get() {
        return listeningViewRef.get()
    }
    private lateinit var ballViewRef: WeakReference<AnimImageView>
    private val ballView: AnimImageView? get() {
        return ballViewRef.get()
    }
    private lateinit var sweepViewRef: WeakReference<AnimImageView>
    private val sweepView: AnimImageView? get() {
        return sweepViewRef.get()
    }

    private lateinit var titleViewRef: WeakReference<TypewriterView>
    private val titleView: TypewriterView? get() {
        return titleViewRef.get()
    }

    private lateinit var generatedByAiViewRef: WeakReference<TypewriterView>
    private val generatedByAiView: View? get() {
        return generatedByAiViewRef.get()
    }

    private val userText = StringBuilder()
    private val aiText = StringBuilder()

    @Volatile
    private var status: CaptionStatus = CaptionStatus.IDLE

    @Volatile
    var enable: Boolean = true
        set(value) {
            field = value
            if (!value) {
                handler.post {
                    clearAiCaption()
                    hide(true)
                    hideDynamicIsland(true)
                }
            }
        }

    var captionObserver: CaptionObserver? = null

    private val voiceSegments = mutableMapOf<String, Segment>()

    private val messageQueue = mutableListOf<String>()
    private var isProcessingQueue = false

    private val handler = object : Handler(Looper.getMainLooper()) {

        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_HIDE_DYNAMIC_ISLAND -> {
                    hideDynamicIsland(true)
                }
                MSG_HIDE_CAPTION -> {
                    if (msg.arg1 == MSG_STATUS_LISTENING) {
                        onUserSpeakStart("", true)
                    } else {
                        hide(true)
                    }
                }
                MSG_UPDATE_CAPTION -> {
                    updateCaption(msg.obj as Transcription)
                }
                MSG_STATUS_LISTENING -> {
                    listening()
                }
                MSG_STATUS_THINKING -> {
                    thinking()
                }
                MSG_UPDATE_DYNAMIC_ISLAND_TEXT -> {
                    hideDynamicIsland(false)
                    titleView?.setTypingText(msg.obj as String) {
                        islandLoadingView?.show()
                    }
                }
                MSG_UPDATE_AI_PROGRESS_TEXT -> {
                    val text = msg.obj as String
                    messageQueue.add(text)
                    KLog.d("MSG_UPDATE_AI_PROGRESS_TEXT text: $text, isProcessingQueue: $isProcessingQueue ", "CaptionWindow")
                    if (!isProcessingQueue) {
                        processNextQueueItem()
                    }
                }
                MSG_PROCESS_NEXT_QUEUE_ITEM -> {
                    processNextQueueItem()
                }
                MSG_QUERY_BY_TEXT -> {
                    val text = msg.obj as String
                    updateQueryByTextCaption(text)
                }
            }
        }
    }

    private fun updateQueryByTextCaption(text: String) {
        status = CaptionStatus.USER_SPEAKING;
        KLog.d("updateQueryByTextCaption enable: $enable status: $status", "CaptionWindow")
        clearAiCaption()
        clearProgressViews()
        val view = userCaptionView ?: return
        val bvw = ballView ?: return
        val lvw = listeningView ?: return
        view.show()
        bvw.show(0)
        aiCaptionLayout?.visibility = View.GONE
        generatedByAiView?.visibility = View.GONE

        AnimatorSet().apply {
            playTogether(
                ObjectAnimator.ofFloat(bvw, "scaleX", bvw.scaleX, 1.0f),
                ObjectAnimator.ofFloat(bvw, "scaleY", bvw.scaleY, 1.0f),
                ObjectAnimator.ofFloat(bvw, "translationX", bvw.translationX, 0f),
                ObjectAnimator.ofFloat(bvw, "translationY", bvw.translationY, 0f)
            )
            duration = 100
            addListener(
                onEnd = {
                    ballView?.tag = null
                }
            )
            bvw.tag = this
        }.start()

        if (lvw.visibility == View.VISIBLE && lvw.tag == null) {
            // 缩小并移动到球球右上方
            AnimatorSet().apply {
                playTogether(
                    ObjectAnimator.ofFloat(lvw, "scaleX", lvw.scaleX, 0.8f),
                    ObjectAnimator.ofFloat(lvw, "scaleY", lvw.scaleY, 0.8f),
                    ObjectAnimator.ofFloat(lvw, "translationX", lvw.translationX, -65f),
                    ObjectAnimator.ofFloat(lvw, "translationY", lvw.translationY, -72f)
                )
                duration = 200
                addListener(
                    onEnd = {
                        listeningView?.tag = null
                    }
                )
                lvw.tag = this
            }.start()
        }

        aiProgressContainer?.visibility = View.GONE

        view.setText(text)
        view.show()
    }

    private fun processNextQueueItem() {
        if (messageQueue.isEmpty()) {
            isProcessingQueue = false
            return
        }

        isProcessingQueue = true
        val text = messageQueue.removeAt(0)
        if (!AgentBus.onAgentStatusChanged("processing", text) && enable) {
            aiProgressView?.setText(text)
        }
        // Schedule next item processing after minimum display time
        handler.sendEmptyMessageDelayed(MSG_PROCESS_NEXT_QUEUE_ITEM, MIN_MESSAGE_DISPLAY_TIME)
    }

    private fun clearStatus(view: View?, onlyClearAnim: Boolean = false) {
        KLog.d("clearStatus view: $view, onlyClearAnim: $onlyClearAnim", "CaptionWindow")
        if (view == null) {
            return
        }
        val anim = view.tag
        if (anim is Animator && anim.isRunning) {
            anim.cancel()
        }
        if (!onlyClearAnim) {
            view.scaleX = 1.0f
            view.scaleY = 1.0f
            view.translationX = 0.0f
            view.translationY = 0.0f
        }
    }
    
    private fun clearProgressViews() {
        aiProgressView?.setText("")
        aiProgressViews.clear()
        aiProgressContainer?.visibility = View.GONE
    }

    private fun listening() {
        KLog.d("listening", "CaptionWindow")
        if (AgentBus.onAgentStatusChanged("listening")) {
            status = CaptionStatus.LISTENING
            return
        }
        if (!enable) {
            return
        }
        isProcessingQueue = false
        messageQueue.clear()
        clearProgressViews()
        val bvw = ballView ?: return
        val lvw = listeningView ?: return
        status = CaptionStatus.LISTENING
        hide(false)
        clearStatus(ballView, true)
        clearStatus(listeningView, true)  // 清除 listeningView 的动画状态
        ballView?.show(0)
        sweepView?.show()
        userCaptionView?.hide(false)
        listeningView?.setText(context?.getString(R.string.user_speaking_tips_2))
        
        // 重置 listeningView 的位置和大小
        lvw.scaleX = 1.0f
        lvw.scaleY = 1.0f
        lvw.translationX = 0f
        lvw.translationY = 0f
        lvw.visibility = View.VISIBLE
        
        aiCaptionLayout?.visibility = View.GONE
        generatedByAiView?.visibility = View.GONE

        clearStatus(bvw, true)
        
        AnimatorSet().apply {
            playTogether(
                ObjectAnimator.ofFloat(bvw, "scaleX", bvw.scaleX, 1.0f),
                ObjectAnimator.ofFloat(bvw, "scaleY", bvw.scaleY, 1.0f),
                ObjectAnimator.ofFloat(bvw, "translationX", bvw.translationX, 0f),
                ObjectAnimator.ofFloat(bvw, "translationY", bvw.translationY, 0f)
            )
            duration = 100
            addListener(
                onEnd = {
                    ballView?.tag = null
                }
            )
            bvw.tag = this
        }.start()
    }

    private fun thinking() {
        KLog.d("thinking enable: $enable status: $status", "CaptionWindow")
        if (status == CaptionStatus.THINKING || status == CaptionStatus.IDLE) {
            return
        }
        if (AgentBus.onAgentStatusChanged("thinking")) {
            status = CaptionStatus.THINKING
            return
        }
        if (!enable) {
            return
        }
        val bvw = ballView ?: return
        status = CaptionStatus.THINKING

        hide(false)
        bvw.show(1)
        aiCaptionLayout?.visibility = View.GONE
        generatedByAiView?.visibility = View.GONE
        isProcessingQueue = false
        listeningView?.visibility = View.GONE
        aiProgressContainer?.visibility = View.VISIBLE


        AnimatorSet().apply {
            playTogether(
                ObjectAnimator.ofFloat(bvw, "scaleX", bvw.scaleX, 1.0f),
                ObjectAnimator.ofFloat(bvw, "scaleY", bvw.scaleY, 1.0f),
                ObjectAnimator.ofFloat(bvw, "translationX", bvw.translationX, 0f),
                ObjectAnimator.ofFloat(bvw, "translationY", bvw.translationY, 0f)
            )
            duration = 100
            addListener(
                onEnd = {
                    ballView?.tag = null
                }
            )
            bvw.tag = this
        }.start()
    }

    private fun updateCaption(caption: Transcription) {
        KLog.d("updateCaption caption: $caption", "CaptionWindow")
        if (!enable) {
            return
        }
        hide(false)
        sweepView?.hide()

        aiProgressContainer?.visibility = View.GONE
        if (caption.isUserSpeaking) {
            onUserSpeakingUI(caption)
        } else {
            onAiSpeakingUI(caption)
        }
    }

    private var userTextLineHeight = -1
    @SuppressLint("SetTextI18n")
    private fun onUserSpeakingUI(caption: Transcription) {
        KLog.d("onUserSpeakingUI caption: $caption status：$status", "CaptionWindow")
        clearAiCaption()
        // Clear progress views when user is speaking
        clearProgressViews()
        listeningView?.setText(context?.getString(R.string.user_speaking_tips_2))
        val view = userCaptionView ?: return
        val bvw = ballView ?: return
        val lvw = listeningView ?: return
        val text = caption.text
        if (text.isEmpty()) {
            return
        }
        view.show()
        bvw.show(0)
        aiCaptionLayout?.visibility = View.GONE
        generatedByAiView?.visibility = View.GONE

        if (lvw.visibility == View.VISIBLE && lvw.tag == null) {
            // 缩小并移动到球球右上方
            AnimatorSet().apply {
                playTogether(
                    ObjectAnimator.ofFloat(lvw, "scaleX", lvw.scaleX, 0.8f),
                    ObjectAnimator.ofFloat(lvw, "scaleY", lvw.scaleY, 0.8f),
                    ObjectAnimator.ofFloat(lvw, "translationX", lvw.translationX, -65f),
                    ObjectAnimator.ofFloat(lvw, "translationY", lvw.translationY, -72f)
                )
                duration = 200
                addListener(
                    onEnd = {
                        listeningView?.tag = null
                    }
                )
                lvw.tag = this
            }.start()
        }
        
        aiProgressContainer?.visibility = View.GONE

        synchronized(userText) {
            KLog.d("onUserSpeakingUI, caption.final: ${caption.final}, userText: $userText, text: $text")
            view.setTypingText(userText.toString() + text, !caption.final)
            if (caption.final) {
                userText.append(text)
                if (text.isNotEmpty()) {
                    val lastChar = text[text.length - 1]
                    if (TextUtil.isPunctuation(lastChar)) {
                        if (TextUtil.isEnglishPunctuation(lastChar)) {
                            userText.append(" ")
                        }
                    } else {
                        userText.append(SEPARATOR)
                    }
                }
            }
        }

        if (view.width > 0) {
            if (userTextLineHeight == -1) {
                val singleLine = StaticLayout.Builder.obtain(
                    view.resources.getText(R.string.caption_measure_text),
                    0,
                    2,
                    view.paint,
                    view.width
                ).setIncludePad(true).build()
                userTextLineHeight = singleLine.height
            }
            
        }

        if (bvw.translationX != 0f
            && bvw.translationY != 0f
            && bvw.tag == null) {
            AnimatorSet().apply {
                playTogether(
                    ObjectAnimator.ofFloat(bvw, "scaleX", bvw.scaleX, 1.0f),
                    ObjectAnimator.ofFloat(bvw, "scaleY", bvw.scaleY, 1.0f),
                    ObjectAnimator.ofFloat(bvw, "translationX", bvw.translationX, 0f),
                    ObjectAnimator.ofFloat(bvw, "translationY", bvw.translationY, 0f)
                )
                duration = 100
                addListener(
                    onEnd = {
                        ballView?.tag = null
                    }
                )
                bvw.tag = this
            }.start()
        }

        status = CaptionStatus.USER_SPEAKING
    }

    @SuppressLint("SetTextI18n")
    private fun onAiSpeakingUI(caption: Transcription) {
        KLog.d("onAiSpeakingUI caption: $caption  status：$status", "CaptionWindow")
        synchronized(userText) {
            userText.clear()
        }
        val layout = aiCaptionLayout ?: return
        val scrollView = aiCaptionScrollView ?: return
        val view = aiCaptionView ?: return
        val bvw = ballView ?: return
        val text = caption.text
        if (text.isEmpty()) {
            return
        }
        userCaptionView?.hide()
        listeningView?.visibility = View.GONE
        aiProgressContainer?.visibility = View.GONE
        layout.visibility = View.VISIBLE
        generatedByAiView?.visibility = View.VISIBLE
        bvw.show(2)

        synchronized(aiText) {
            KLog.d("onAiSpeakingUI, caption.final: ${caption.final}, aiText: $aiText, text: $text")
            view.text = aiText.toString() + text
            if (caption.final) {
                aiText.append(text)
                val lastChar = text[text.length - 1]
                if (TextUtil.isPunctuation(lastChar)) {
                    if (TextUtil.isEnglishPunctuation(lastChar)) {
                        aiText.append(" ")
                    }
                } else {
                    aiText.append(SEPARATOR)
                }
            }
        }
        if (view.width > 0) {
            scrollView.post {
                scrollView.scrollTo(0, view.height - scrollView.height)
            }
        }
        if ((view.height == 0 || bvw.height == 0)) {
            view.post { onAiSpeakingUI(caption) }
            return
        }
        if (status != CaptionStatus.AI_SPEAKING) {
            clearStatus(layout, true)
            val res = layout.resources
            val viewHeight = res.getDimensionPixelSize(R.dimen.ai_caption_bar_height)
            val viewMarginHor = res.getDimensionPixelSize(R.dimen.caption_bar_padding_hor)
            val viewMarginBottom = res.getDimensionPixelSize(R.dimen.ai_caption_bar_margin_bottom)
            val ballMarginStart = res.getDimensionPixelSize(R.dimen.caption_ball_margin_start)
            val ballMarginBottom = res.getDimensionPixelSize(R.dimen.caption_ball_margin_bottom)
            val ballTranslationX = (viewMarginHor - ballMarginStart)* BALL_TARGET_SCALE
            val ballTranslationY = viewHeight + viewMarginBottom - bvw.height*0.4f - ballMarginBottom

            AnimatorSet().apply {
                playTogether(
                    ObjectAnimator.ofFloat(layout, "alpha", layout.alpha, 1f),
                    ObjectAnimator.ofFloat(layout, "scaleX", layout.scaleX, 1.0f),
                    ObjectAnimator.ofFloat(layout, "scaleY", layout.scaleY, 1.0f),
                )
                duration = 200
                addListener(
                    onEnd = {
                        aiCaptionLayout?.tag = null
                    }
                )
                layout.tag = this
                start()
            }

            AnimatorSet().apply {
                playTogether(
                    ObjectAnimator.ofFloat(bvw, "scaleX", bvw.scaleX, BALL_TARGET_SCALE),
                    ObjectAnimator.ofFloat(bvw, "scaleY", bvw.scaleY, BALL_TARGET_SCALE),
                    ObjectAnimator.ofFloat(bvw, "translationX", bvw.translationX, ballTranslationX),
                    ObjectAnimator.ofFloat(bvw, "translationY", bvw.translationY, -ballTranslationY)
                )
                duration = 200
                addListener(
                    onEnd = {
                        ballView?.tag = null
                    }
                )
                bvw.tag = this
                start()
            }
            status = CaptionStatus.AI_SPEAKING
        }
    }

    private fun addLayout(
        context: Context,
        layoutId: Int,
        width: Int,
        height: Int,
        gravity: Int,
        onLayout: (View) -> Unit
    ) {
        KLog.d("addLayout layoutId: $layoutId, width: $width, height: $height, gravity: $gravity", "CaptionWindow")
        val vm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val layoutParams = WindowManager.LayoutParams(
            width,
            height,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_PHONE
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
            PixelFormat.TRANSLUCENT
        )
        layoutParams.gravity = gravity
        val view = LayoutInflater.from(context).inflate(layoutId, null)
        vm.addView(view, layoutParams)
        onLayout(view)
    }

    @SuppressLint("InflateParams")
    fun show(context: Context) {
        KLog.d("show context: $context", "CaptionWindow")
        this.contextRef = WeakReference(context)
        this.addLayout(
            context,
            R.layout.float_window_dynamic_island,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            Gravity.TOP or Gravity.CENTER_HORIZONTAL
        ) {
            this.titleViewRef = WeakReference(it.findViewById(R.id.title_view))
            this.islandLoadingViewRef = WeakReference(
                it.findViewById<LoadingView?>(R.id.loading_view).apply {
                    setDotColor(Color.WHITE)
                }
            )
            this.islandLayoutRef = WeakReference(it)
        }

        this.addLayout(
            context,
            R.layout.float_window_robot_caption,
            WindowManager.LayoutParams.MATCH_PARENT,
            context.resources.getDimensionPixelSize(R.dimen.caption_bar_height),
            Gravity.BOTTOM
        ) {
            userCaptionViewRef = WeakReference(
                it.findViewById<StreamTextView>(R.id.user_caption_view).apply {
                    // 移除 ScrollingMovementMethod，因为 StreamTextView 现在自己处理滚动
                    // movementMethod = ScrollingMovementMethod()
                }
            )
            aiCaptionScrollViewRef = WeakReference(
                it.findViewById(R.id.ai_caption_scroll_view)
            )
            aiCaptionLayoutRef = WeakReference(
                it.findViewById(R.id.ai_caption_container)
            )
            aiCaptionViewRef = WeakReference(
                it.findViewById(R.id.ai_caption_view)
            )
            aiProgressContainerRef = WeakReference(
                it.findViewById(R.id.ai_progress_container)
            )
            aiProgressViewRef = WeakReference(
                it.findViewById(R.id.ai_progress_view)
            )
            listeningViewRef = WeakReference(
                it.findViewById(R.id.listening_view)
            )
            listeningView?.setText(CaptionWindow.context?.getString(R.string.user_speaking_tips_2))
            generatedByAiViewRef = WeakReference(
                it.findViewById(R.id.generated_by_ai)
            )
            ballViewRef = WeakReference(
                it.findViewById<AnimImageView>(R.id.ai_ball_view).also { iv ->
                    if (DeviceOS.language.codeName.startsWith("ar")) {
                        iv.setAnim(
                            arrayOf(
                                WebPDrawable(AssetStreamLoader(context, "ai_listening_ball_rtl.webp")),
                                WebPDrawable(AssetStreamLoader(context, "ai_thinking.webp")),
                                WebPDrawable(AssetStreamLoader(context, "ai_play_tts_ball.webp")),
                            )
                        )
                    } else {
                        iv.setAnim(
                            arrayOf(
                                WebPDrawable(AssetStreamLoader(context, "ai_listening_ball.webp")),
                                WebPDrawable(AssetStreamLoader(context, "ai_thinking.webp")),
                                WebPDrawable(AssetStreamLoader(context, "ai_play_tts_ball.webp")),
                            )
                        )
                    }
                }
            )
            sweepViewRef = WeakReference(
                it.findViewById<AnimImageView>(R.id.ai_sweep_view).also { iv ->
                    iv.setAnim(
                        arrayOf(
                            WebPDrawable(AssetStreamLoader(context, "ai_listening_sweep.webp"))
                        )
                    )
                }
            )
            captionLayoutRef = WeakReference(it)
        }
        handler.sendEmptyMessage(MSG_HIDE_CAPTION)
        handler.sendEmptyMessage(MSG_HIDE_DYNAMIC_ISLAND)
    }

    private fun hideDynamicIsland(hide: Boolean) {
        KLog.d("hideDynamicIsland hide: $hide", "CaptionWindow")
        val islandLayout = this.islandLayout ?: return
        if (hide) {
            if (islandLayout.visibility == View.GONE) {
                return
            }
            islandLoadingView?.hide()
            titleView?.hide(
                fadeOut = {
                    islandLayout.alpha = it
                },
                completion = {
                    islandLayout.visibility = View.GONE
                }
            )
        } else {
            islandLayout.alpha = 1.0f
            islandLayout.visibility = View.VISIBLE
        }
    }

    private fun hide(hide: Boolean) {
        KLog.d("hide hide: $hide", "CaptionWindow")
        val view = this.captionLayout ?: return
        if (hide) {
            if (AgentBus.onAgentStatusChanged("reset_status")) {
                status = CaptionStatus.IDLE
                return
            }
            if (view.visibility == View.VISIBLE) {
                clearStatus(ballView)
                clearStatus(aiCaptionView)
                clearStatus(listeningView)  // 清除 listeningView 的动画状态
                synchronized(userText) {
                    userText.clear()
                }
                clearAiCaption()
                isProcessingQueue = false
                messageQueue.clear()
                clearProgressViews()
                ballView?.hide()
                sweepView?.hide()
                listeningView?.visibility = View.GONE
                view.visibility = View.GONE
            }
            status = CaptionStatus.IDLE
        } else if (view.visibility == View.GONE) {
            view.visibility = View.VISIBLE
        }
    }

    fun onUserSpeakStart(sid: String, valid: Boolean) {
        KLog.d("onUserSpeakStart sid: $sid, valid: $valid, enable: $enable", "CaptionWindow")
        if (valid) {
            if (status != CaptionStatus.LISTENING && status != CaptionStatus.USER_SPEAKING) {
                handler.sendEmptyMessage(MSG_STATUS_LISTENING)
            }
            handler.removeMessages(MSG_HIDE_CAPTION)
            handler.sendEmptyMessageDelayed(MSG_HIDE_CAPTION, DELAY_TIME*2)
        }

        if (sid.isEmpty()) {
            return
        }

        synchronized(voiceSegments) {
            val segment = voiceSegments[sid]
            if (segment == null) {
                voiceSegments[sid] = Segment().also { it.valid = valid }
                return
            }
            segment.valid = valid
            val transcription = segment.transcription
            if (transcription != null && transcription.final) {
                if (segment.valid) {
                    setCaption(transcription)
                } else {
                    voiceSegments.remove(sid)
                }
            }
        }
    }

    fun onUserSpeakEnd() {
        KLog.d("onUserSpeakEnd enable: $enable", "CaptionWindow")
        handler.sendEmptyMessageDelayed(MSG_HIDE_CAPTION, DELAY_TIME*2)
    }

    fun setCaption(caption: Transcription) {
        KLog.d("setCaption enable: $enable, status: $status, caption: $caption, text: ${caption.text}, isUserSpeaking: ${caption.isUserSpeaking}", "CaptionWindow")
        if (caption.error.isNotEmpty()) {
            RobotConsole.print("final: ${caption.final}, ${caption.error}", Color.RED)
        }
        if (caption.isUserSpeaking) {
            if (caption.final) {
                RTQueryTrace.reportCaptionReceived(caption.sid)
            }
            synchronized(voiceSegments) {
                val segment = voiceSegments[caption.sid]
                KLog.d("segment sid: ${caption.sid}, segment: $segment", "CaptionWindow")
                if (segment == null) {
                    voiceSegments[caption.sid] =
                        Segment().also { it.transcription = caption }
                    return
                }
                segment.transcription = caption

                if (!segment.valid) {
                    RobotConsole.print(
                        "${caption.speaker}: ${caption.text}, Valid: false",
                        Color.RED,
                        true
                    )
                    return
                }
                RobotConsole.print(
                    "${caption.speaker}: ${caption.text}, Valid: true",
                    0xFF5DADE2.toInt(),
                    true
                )

                if (caption.final) {
                    handler.removeMessages(MSG_STATUS_THINKING)
                    handler.sendEmptyMessageDelayed(MSG_STATUS_THINKING, 500L)
                    voiceSegments.remove(caption.sid)

                    handler.removeMessages(MSG_HIDE_CAPTION)
                    handler.sendEmptyMessageDelayed(MSG_HIDE_CAPTION, DELAY_TIME*2)
                }
            }
        } else {
            if (status == CaptionStatus.USER_SPEAKING) {
                return
            }

            handler.removeMessages(MSG_STATUS_THINKING)
            KLog.d("caption.isAISpeaking ${caption.speaker}: ${caption.text}", "CaptionWindow")

            handler.removeMessages(MSG_HIDE_CAPTION)
            handler.sendEmptyMessageDelayed(MSG_HIDE_CAPTION, DELAY_TIME)
        }

        captionObserver?.onCaptionChanged(caption)

        if (caption.text.isEmpty()) {
            return
        }

        if (ConnectivityHelper.onReceived(caption) || AgentBus.onTranscribed(caption)) {
            return
        }

        // Always update UI for both user and AI speech
        handler.obtainMessage(MSG_UPDATE_CAPTION, 0, 0, caption).sendToTarget()
    }

    fun clearUserCaption() {
        KLog.d("clearUserCaption", "CaptionWindow")
        synchronized(userText) {
            userText.clear()
        }
        handler.sendEmptyMessage(MSG_HIDE_CAPTION)
        handler.sendEmptyMessage(MSG_HIDE_DYNAMIC_ISLAND)
    }

    fun clearAiCaption() {
        KLog.d("clearAiCaption: $aiText", "CaptionWindow")
        synchronized(aiText) {
            aiText.clear()
        }
    }

    fun onPlanReceived() {
        KLog.d("onPlanReceived", "CaptionWindow")
        handler.removeMessages(MSG_STATUS_THINKING)
        synchronized(userText) {
            userText.clear()
        }
    }

    fun actionCompleteSuccess(name: String) {
        KLog.d("actionCompleteSuccess", "CaptionWindow")
        if (TextUtils.equals(name, "orion.agent.action.EXIT") ||
            TextUtils.equals(name, "orion.agent.action.BACK")) {
            this.clearAiCaption()
            synchronized(userText) {
                userText.clear()
            }
            handler.sendEmptyMessage(MSG_HIDE_CAPTION)
        }
    }

    fun queryByTextThinking(text: String) {
        KLog.d("triggerThinking enable: $enable, status: $status", "CaptionWindow")
        if (!enable) {
            return
        }
        synchronized(userText) {
            userText.clear()
        }

        SpeechPlayer.stop()
        handler.removeMessages(MSG_HIDE_CAPTION)
        handler.obtainMessage(
            MSG_QUERY_BY_TEXT,
            0, 0,
            text
        ).sendToTarget()
        handler.sendEmptyMessageDelayed(MSG_STATUS_THINKING, 1000L)
        val ctx = this.context ?: return
        updateAIExecutionProgress(ctx.getString(R.string.ai_thinking_tips) + SEPARATOR, false)
        handler.sendEmptyMessageDelayed(MSG_HIDE_CAPTION, DELAY_TIME*2)
    }

    fun updateAIExecutionProgress(text: String, prefix: Boolean = true) {
        KLog.d("updateAIExecutionProgress: $text, status: $status, prefix: $prefix, enable: $enable", "CaptionWindow")
        if (text.isEmpty()) {
            return
        }
        val ctx = this.context ?: return
        handler.obtainMessage(
            MSG_UPDATE_AI_PROGRESS_TEXT,
            0, 0,
            if (prefix) ctx.getString(R.string.island_text_prefix, text) else text
        ).sendToTarget()
        handler.removeMessages(MSG_HIDE_CAPTION)
        handler.sendEmptyMessageDelayed(MSG_HIDE_CAPTION, DELAY_TIME*2)
    }
}