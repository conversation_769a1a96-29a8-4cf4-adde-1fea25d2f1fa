package com.ainirobot.common.debug

import com.ainirobot.agent.base.Transcription
import com.ainirobot.agent.base.utils.DigitalUtil
import com.ainirobot.agent.core.CommonMessage
import com.ainirobot.agent.core.Connectivity
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.agent.core.SpeechPlayer
import com.ainirobot.common.Config.ioScope
import com.ainirobot.common.DataStore
import com.ainirobot.common.utils.DateUtil.dateFormat
import com.ainirobot.common.utils.KLog
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

enum class TestMode {
    MESSAGE,
    TTS,
    ASR
}

interface TestPrinter {

    fun onPrint(content: String)

}

object ConnectivityHelper {

    private const val TTS_TEST_TEXT = "故宫，即北京故宫，是中国明清两代的皇家宫殿，旧称紫禁城。它始建于明成祖永乐四年（1406年），建成于永乐十八年（1420年），位于北京市中心。故宫是世界上现存规模最大、保存最为完整的木质结构古建筑群之一，占地面积约72万平方米，有宫殿近千座，房屋数以万计。\n" +
            "故宫整体建筑布局严谨，以中轴线为中心，左右对称。太和殿是故宫的核心建筑，象征着皇权的至高无上。故宫不仅是中国古代建筑艺术的瑰宝，也是世界文化遗产，收藏了大量珍贵文物，展现了中国古代文化的辉煌。"

    var mode: TestMode = TestMode.MESSAGE

    var printer: TestPrinter? = null

    var isStop = true
        private set

    private fun print(vararg message: String) {
        val text = message.joinToString(" ")
        KLog.d(text, "ConnectivityHelper")
        printer?.onPrint(text)
    }

    private fun sendTestMessage() {
        ioScope.launch {
            while (mode == TestMode.MESSAGE && !isStop) {
                val timestamp = System.currentTimeMillis()
                val messageId = DigitalUtil.uuid()
                val connectivity = Connectivity(
                    serverTimestamp = 0,
                    clientTimestamp = timestamp
                )
                print("send message: messageId=$messageId, client_timestamp=${timestamp}, client_timestamp_format=${timestamp.dateFormat("yyyy-MM-dd HH:mm:ss.SSS")}")
                val result = MessageSender.sendTestMessageSync(
                    messageId = messageId,
                    content = connectivity,
                    timeoutMillis = 10000
                )
                if (result != null) {
                    print(
                        "receive message: status=${if (result.clientTimestamp > 0) "success" else "fail"}, messageId=$messageId,",
                        "client_timestamp=${result.clientTimestamp}, client_timestamp_format=${result.clientTimestamp.dateFormat("yyyy-MM-dd HH:mm:ss.SSS")},",
                        "server_timestamp=${result.serverTimestamp}, server_timestamp_format=${result.serverTimestamp.dateFormat("yyyy-MM-dd HH:mm:ss.SSS")},",
                        "cost=${System.currentTimeMillis() - result.clientTimestamp}ms"
                    )
                } else {
                    print(
                        "receive message: status=timeout, messageId=$messageId",
                        "cost=${System.currentTimeMillis() - timestamp}ms"
                    )
                }
                delay(1000)
            }
        }
    }

    private fun sendTestTTS() {
        ioScope.launch {
            while (mode == TestMode.TTS && !isStop) {
                val timestamp = System.currentTimeMillis()
                print("start play tts")
                val result = SpeechPlayer.playSync(TTS_TEST_TEXT, 120000)
                val cost = System.currentTimeMillis() - timestamp
                if (result != null) {
                    print("play tts ${if (result.status) "success" else "fail"}, cost=${cost}ms")
                } else {
                    print("play tts timeout, cost=${cost}ms")
                }
                delay(1000)
            }
        }
    }

    private fun sendTestASR() {
        DataStore.microphoneMuted = false
        MessageSender.sendMessage(
            CommonMessage(
                name = "state.robot_state",
                content = mapOf(
                    "disable_plan" to true
                )
            ),
            isSafety = false
        )
    }

    fun start() {
        isStop = false
        when (mode) {
            TestMode.MESSAGE -> sendTestMessage()
            TestMode.TTS -> sendTestTTS()
            TestMode.ASR -> sendTestASR()
        }
    }

    fun onReceived(transcription: Transcription): Boolean {
        return if (!isStop && (mode == TestMode.ASR || mode == TestMode.TTS)) {
            if (transcription.isUserSpeaking) {
                print("User speaking: ${transcription.text}")
            } else {
                print("Robot speaking: ${transcription.text}")
            }
            true
        } else false
    }

    fun stop() {
        DataStore.microphoneMuted = true
        isStop = true
        SpeechPlayer.stop()
        MessageSender.sendMessage(
            CommonMessage(
                name = "state.robot_state",
                content = mapOf(
                    "disable_plan" to false
                )
            ),
            isSafety = false
        )
    }
}