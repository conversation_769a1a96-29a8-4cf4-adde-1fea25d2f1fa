package com.ainirobot.common.widget

import android.animation.Animator

abstract class SimpleAnimatorListener : Animator.AnimatorListener {

    private var isCancel = false

    override fun onAnimationStart(animation: Animator) {
        isCancel = false
    }

    override fun onAnimationEnd(animation: Animator) {
        this.onEnd(isCancel)
    }

    override fun onAnimationCancel(animation: Animator) {
        isCancel = true
    }

    override fun onAnimationRepeat(animation: Animator) {

    }

    abstract fun onEnd(isCancel: <PERSON>olean)
}