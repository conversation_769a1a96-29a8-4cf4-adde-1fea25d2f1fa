package com.ainirobot.common.widget

import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView
import com.ainirobot.agent.R

class CustomRecyclerView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyle: Int = 0
) : RecyclerView(context, attrs, defStyle) {

    private var maxHeight = 0

    init {
        context.theme.obtainStyledAttributes(
            attrs, R.styleable.CustomRecyclerView, 0, 0
        ).apply {
            try {
                maxHeight = getDimensionPixelSize(R.styleable.CustomRecyclerView_maxHeight, 0)
            } finally {
                recycle()
            }
        }
    }

    override fun onMeasure(widthSpec: Int, heightSpec: Int) {
        val maxHeightSpec = if (maxHeight > 0) {
            MeasureSpec.makeMeasureSpec(maxHeight, MeasureSpec.AT_MOST)
        } else {
            heightSpec
        }
        super.onMeasure(widthSpec, maxHeightSpec)
    }
}