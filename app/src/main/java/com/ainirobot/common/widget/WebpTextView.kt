package com.ainirobot.common.widget

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.TypedArray
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.ainirobot.agent.R
import com.github.penfeizhou.animation.loader.AssetStreamLoader
import com.github.penfeizhou.animation.loader.ResourceStreamLoader
import com.github.penfeizhou.animation.webp.WebPDrawable

@SuppressLint("AppCompatCustomView")
class WebpTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : TextView(context, attrs, defStyleAttr) {

    private var webpDrawable: WebPDrawable? = null
    private var webpAssetPath: String? = null
    private var webpResourceId: Int = 0
    private var webpSize: Int = 0
    private var webpSpacing: Int = 0
    
    // 气泡背景相关
    private val bgPaint = Paint().apply {
        isAntiAlias = true
        color = 0x33FFFFFF.toInt() // 半透明灰色背景
        style = Paint.Style.FILL
    }
    
    private val bgRect = RectF()
    private val cornerRadius: Float
    private var showBubbleBackground: Boolean = true

    init {
        // 初始化圆角半径
        cornerRadius = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            10f,
            context.resources.displayMetrics
        )
        
        // 设置默认padding，确保文本不会贴边
        if (paddingLeft == 0 && paddingRight == 0 && paddingTop == 0 && paddingBottom == 0) {
            val defaultPadding = TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP,
                4f,
                context.resources.displayMetrics
            ).toInt()
            setPadding(defaultPadding, defaultPadding, defaultPadding, defaultPadding)
        }
        
        // 获取自定义属性
        context.theme.obtainStyledAttributes(
            attrs,
            R.styleable.WebpTextView,
            0, 0
        ).apply {
            try {
                webpAssetPath = getString(R.styleable.WebpTextView_webpAsset)
                webpResourceId = getResourceId(R.styleable.WebpTextView_webpResource, 0)
                webpSize = getDimensionPixelSize(
                    R.styleable.WebpTextView_webpSize,
                    resources.getDimensionPixelSize(R.dimen.webp_default_size)
                )
                webpSpacing = getDimensionPixelSize(
                    R.styleable.WebpTextView_webpSpacing,
                    resources.getDimensionPixelSize(R.dimen.webp_default_spacing)
                )
            } finally {
                recycle()
            }
        }

        // 初始化WebP
        setupWebp()
    }

    private fun setupWebp() {
        // 停止旧的动画
        webpDrawable?.stop()
        
        // 优先使用asset路径
        webpDrawable = when {
            !webpAssetPath.isNullOrEmpty() -> {
                try {
                    WebPDrawable(AssetStreamLoader(context, webpAssetPath!!))
                } catch (e: Exception) {
                    e.printStackTrace()
                    null
                }
            }
            webpResourceId != 0 -> {
                try {
                    WebPDrawable(ResourceStreamLoader(context, webpResourceId))
                } catch (e: Exception) {
                    e.printStackTrace()
                    null
                }
            }
            else -> null
        }

        webpDrawable?.let { drawable ->
            // 设置WebP尺寸
            drawable.setBounds(0, 0, webpSize, webpSize)
            
            // 设置循环播放（0 表示无限循环）
            drawable.setLoopLimit(0)
            
            // 确保可见
            drawable.setVisible(true, true)
            
            // 在下一帧设置drawable，确保View已经完全初始化
            post {
                // 设置为右侧drawable
                setCompoundDrawablesRelativeWithIntrinsicBounds(
                    null, null, drawable, null
                )
                
                // 设置间距
                compoundDrawablePadding = webpSpacing
                
                // 开始播放
                drawable.start()
            }
        }
    }

    /**
     * 设置来自assets的WebP文件
     */
    fun setWebpFromAsset(assetPath: String) {
        stopAnimation()
        webpAssetPath = assetPath
        setupWebp()
    }

    /**
     * 设置来自resources的WebP文件
     */
    fun setWebpFromResource(resourceId: Int) {
        stopAnimation()
        webpResourceId = resourceId
        setupWebp()
    }

    /**
     * 设置WebP大小
     */
    fun setWebpSize(size: Int) {
        webpSize = size
        webpDrawable?.setBounds(0, 0, webpSize, webpSize)
        // 刷新drawable
        setCompoundDrawablesRelativeWithIntrinsicBounds(
            compoundDrawablesRelative[0],
            compoundDrawablesRelative[1],
            webpDrawable,
            compoundDrawablesRelative[3]
        )
    }

    /**
     * 开始播放动画
     */
    fun startAnimation() {
        webpDrawable?.start()
    }

    /**
     * 停止播放动画
     */
    fun stopAnimation() {
        webpDrawable?.stop()
    }

    /**
     * 动画是否正在播放
     */
    fun isAnimationRunning(): Boolean {
        return webpDrawable?.isRunning ?: false
    }

    override fun onDraw(canvas: Canvas) {
        // 绘制气泡背景
        if (showBubbleBackground && visibility == VISIBLE) {
            bgRect.set(
                0f,
                0f,
                width.toFloat(),
                height.toFloat()
            )
            canvas.drawRoundRect(bgRect, cornerRadius, cornerRadius, bgPaint)
        }
        
        // 调用父类方法绘制文本和drawable
        super.onDraw(canvas)
    }

    /**
     * 设置是否显示气泡背景
     */
    fun setShowBubbleBackground(show: Boolean) {
        showBubbleBackground = show
        invalidate()
    }

    /**
     * 设置气泡背景颜色
     */
    fun setBubbleBackgroundColor(color: Int) {
        bgPaint.color = color
        invalidate()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        // View附加到窗口时开始播放
        startAnimation()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // View从窗口分离时停止播放，释放资源
        stopAnimation()
    }

    override fun setVisibility(visibility: Int) {
        super.setVisibility(visibility)
        // 根据可见性控制动画
        when (visibility) {
            VISIBLE -> startAnimation()
            else -> stopAnimation()
        }
    }
}