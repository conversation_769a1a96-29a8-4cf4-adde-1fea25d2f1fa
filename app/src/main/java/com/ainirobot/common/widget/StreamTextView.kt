package com.ainirobot.common.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.widget.TextView
import com.ainirobot.common.utils.TextUtil

@SuppressLint("AppCompatCustomView")
class StreamTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : TextView(context, attrs, defStyleAttr) {

    // 固定行高，确保中英文显示一致
    private val fixedLineHeight: Float

    init {
        maxLines = 2
        ellipsize = null

        fixedLineHeight = context.resources.displayMetrics.density * 18f
        
        // 设置行间距，使实际行高等于 fixedLineHeight
        // setLineSpacing 的第一个参数是额外的行间距（像素）
        // 第二个参数是行间距倍数
        post {
            val currentLineHeight = paint.fontMetrics.bottom - paint.fontMetrics.top
            val extraSpacing = fixedLineHeight - currentLineHeight
            setLineSpacing(extraSpacing, 1f)
        }
    }

    @Volatile
    private var fadeInAnim: NumberAnimator? = null

    fun setTypingText(typingText: String, anim: Boolean = true) {
        clearAnim()
        val text = this.text.toString()
        if (!anim || text == typingText) {
            this.text = typingText
            return
        }
        if (text.length > typingText.length) {
            startTextAnim(0, typingText)
        } else {
            startTextAnim(TextUtil.firstDiffIndex(text, typingText), typingText)
        }
    }

    private fun startTextAnim(startIndex: Int, typingText: String) {
        val endIndex = typingText.length - 1
        if (startIndex >= endIndex) {
            this.text = typingText
            return
        }
        this.text = typingText.substring(0, startIndex + 1)
        fadeInAnim = NumberAnimator.ofValue(startIndex, endIndex).apply {
            updateListener = object : NumberAnimator.OnUpdateListener {
                override fun onUpdate(value: Int) {
                    if (fadeInAnim == null) {
                        return
                    }
                    <EMAIL> = typingText.substring(0, value + 1)
                }
            }
            start()
        }
    }

    fun hide(clear: Boolean = true) {
        if (this.visibility == GONE) {
            return
        }
        clearAnim()
        this.visibility = GONE
        if (clear) {
            this.text = ""
        }
    }

    fun show() {
        if (this.visibility == VISIBLE) {
            return
        }
        this.visibility = VISIBLE
    }

    private fun clearAnim() {
        fadeInAnim?.stop()
        fadeInAnim = null
    }

    override fun onTextChanged(
        text: CharSequence?,
        start: Int,
        lengthBefore: Int,
        lengthAfter: Int
    ) {
        super.onTextChanged(text, start, lengthBefore, lengthAfter)
        
        // 在文本改变后自动滚动到最后两行
        post {
            val layout = layout ?: return@post
            if (layout.lineCount > maxLines) {
                // 使用固定行高计算滚动位置
                val lineHeight = fixedLineHeight
                
                // 计算需要滚动的距离
                // 总行数 - 2 = 需要隐藏的行数
                val hiddenLines = layout.lineCount - maxLines
                val scrollY = (hiddenLines * lineHeight).toInt()
                
                // 滚动到计算的位置
                scrollTo(0, scrollY)
            } else {
                // 如果行数不超过2行，不需要滚动
                scrollTo(0, 0)
            }
        }
    }
}