package com.ainirobot.common.widget

import android.animation.Animator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.widget.TextView

@SuppressLint("AppCompatCustomView")
class TypewriterView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : TextView(context, attrs, defStyleAttr) {

    private var fadeInDuration = 300L
    private var fadeOutDuration = 100L

    private var fadeInAnim: Animator? = null
    private var fadeOutAnim: Animator? = null

    private var process = 0.0f

    private var typingText = ""

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (fadeInAnim?.isRunning == true) {
            fadeInAnim?.cancel()
        }
        if (fadeOutAnim?.isRunning == true) {
            fadeOutAnim?.cancel()
        }
    }

    fun setTypingText(text: String, completion: (() -> Unit)? = null) {
        if (text.isNotEmpty()) {
            typingText = text
            show(completion)
        }
    }

    private fun show(completion: (() -> Unit)?) {
        fadeInAnim?.apply {
            if (isRunning) {
                cancel()
            }
        }
        fadeOutAnim?.apply {
            if (isRunning) {
                cancel()
            }
        }
        this.visibility = VISIBLE
        fadeInAnim = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = fadeInDuration
            addUpdateListener {
                process = it.animatedValue as Float
                text = typingText.substring(0, (typingText.length * process).toInt())
            }
            addListener(object : SimpleAnimatorListener() {
                override fun onEnd(isCancel: Boolean) {
                    if (isCancel) {
                        return
                    }
                    fadeInAnim = null
                    completion?.invoke()
                }
            })
            start()
        }
    }

    fun hide(
        fadeOut: ((progress: Float) -> Unit)? = null,
        completion: (() -> Unit)? = null
    ) {
        if (fadeInAnim?.isRunning == true) {
            fadeInAnim?.cancel()
        }
        if (fadeOutAnim?.isRunning == true) {
            return
        }
        fadeOutAnim = ValueAnimator.ofFloat(process, 0f).apply {
            duration = fadeOutDuration
            addUpdateListener {
                val progress = it.animatedValue as Float
                if (progress < 0.15) {
                    fadeOut?.invoke(progress)
                }
                text = typingText.substring(0, (typingText.length * process).toInt())
            }
            addListener(object : SimpleAnimatorListener() {
                override fun onEnd(isCancel: Boolean) {
                    if (isCancel) {
                        return
                    }
                    process = 0.0f
                    fadeOutAnim = null
                    visibility = GONE
                    completion?.invoke()
                }
            })
            start()
        }
    }
}