// GradientMarqueeTextView.kt
package com.ainirobot.common.widget

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.LinearGradient
import android.graphics.RectF
import android.graphics.Shader
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import android.view.animation.LinearInterpolator

class GradientMarqueeTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    @Volatile
    private var gradientAnim: ValueAnimator? = null

    private val paint = Paint().apply {
        textSize = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_SP,
            8f,
            context.resources.displayMetrics
        )
        isAntiAlias = true
        color = 0xFFFFFFFF.toInt()
    }
    private val bgPaint = Paint().apply {
        isAntiAlias = true
        color = 0x33FFFFFF.toInt() // 半透明灰色背景
        style = Paint.Style.FILL
    }

    private val bgRect = RectF()
    private val cornerRadius = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP,
        10f,
        context.resources.displayMetrics
    )

    private var text: String = ""
    private var highlightOffset: Float = 0f // 当前高亮偏移量

    init {
    }

    fun setText(newText: String) {
        text = newText
        requestLayout() // 请求重新测量布局
        invalidate()
        start()
    }
    
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val textWidth = paint.measureText(text)
        val textHeight = paint.textSize
        
        val desiredWidth = (textWidth + paddingStart + paddingEnd).toInt()
        val desiredHeight = (textHeight + paddingTop + paddingBottom).toInt()
        
        val measuredWidth = resolveSize(desiredWidth, widthMeasureSpec)
        val measuredHeight = resolveSize(desiredHeight, heightMeasureSpec)
        
        setMeasuredDimension(measuredWidth, measuredHeight)
    }

    private fun startHighlightAnimation() {
        val textWidth = paint.measureText(text)
        gradientAnim = ValueAnimator.ofFloat(-0.5f, 1.5f).apply {
            duration = 2000
            repeatCount = ValueAnimator.INFINITE
            interpolator = LinearInterpolator()
            addUpdateListener { animator ->
                highlightOffset = animator.animatedValue as Float * textWidth
                invalidate()
            }
            start()
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        if (text.isEmpty()) return

        val textWidth = paint.measureText(text)
        
        // 计算文本位置（居中）
        val fontMetrics = paint.fontMetrics
        val x = paddingStart.toFloat()
        val y = height / 2f - (fontMetrics.top + fontMetrics.bottom) / 2f
        
        // 绘制背景
        bgRect.set(
            0f,
            0f,
            textWidth + paddingStart + paddingEnd,
            height.toFloat()
        )
        canvas.drawRoundRect(bgRect, cornerRadius, cornerRadius, bgPaint)

        // 创建渐变效果，类似 GradientTypewriterView
        val gradient = LinearGradient(
            highlightOffset, 0f,
            highlightOffset + textWidth * 0.5f, 0f,
            intArrayOf(
                0x80FFFFFF.toInt(),
                0xFFFFFFFF.toInt(),
                0x80FFFFFF.toInt()
            ),
            floatArrayOf(0f, 0.5f, 1f),
            Shader.TileMode.CLAMP
        )

        paint.shader = gradient
        canvas.drawText(text, x, y, paint)
        paint.shader = null
    }

    fun start() {
        stop()
        startHighlightAnimation()
    }

    fun stop() {
        gradientAnim?.cancel()
        invalidate()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stop()
        gradientAnim?.removeAllListeners()
        gradientAnim = null
    }

    fun hide() {
        visibility = GONE
        stop()
    }

    fun show() {
        visibility = VISIBLE
        start()
    }
}
