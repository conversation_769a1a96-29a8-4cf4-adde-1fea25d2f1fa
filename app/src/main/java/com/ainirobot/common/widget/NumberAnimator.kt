package com.ainirobot.common.widget

import android.os.Handler
import android.os.Looper
import android.os.Message
import java.util.LinkedList
import java.util.Queue

class NumberAnimator private constructor() {

    interface OnUpdateListener {
        fun onUpdate(value: Int)
    }

    companion object {

        private const val DURATION = 16L

        private const val MSG_UPDATE = 1001

        private val animPool: Queue<NumberAnimator> = LinkedList()

        fun ofValue(start: Int, end: Int): NumberAnimator {
            synchronized(animPool) {
                val anim = animPool.poll()
                if (anim != null) {
                    anim.start = start
                    anim.end = end
                    return anim
                }
            }
            return NumberAnimator().also { it.start = start; it.end = end  }
        }
    }

    private val handler = object : Handler(Looper.getMainLooper()) {

        override fun handleMessage(msg: Message) {
            if (msg.what == MSG_UPDATE && isRunning) {
                val current = msg.arg1
                <EMAIL> = current
                updateListener?.onUpdate(current)
                if (current == end) {
                    release()
                    return
                }
                val next = if (start < end) {
                    current + 1
                } else {
                    current - 1
                }
                sendMessageDelayed(
                    obtainMessage(MSG_UPDATE, next, 0),
                    DURATION
                )
            }
        }
    }

    private var start: Int = 0
    private var end: Int = 0
    private var value: Int = 0
    @Volatile
    private var isRunning = false
    @Volatile
    private var isReleased = false

    var updateListener: OnUpdateListener? = null

    fun start() {
        if (isRunning) {
            return
        }
        isRunning = true
        handler.sendMessageDelayed(
            handler.obtainMessage(MSG_UPDATE, start, 0),
            DURATION
        )
    }

    fun stop() {
        isRunning = false
        handler.removeMessages(MSG_UPDATE)
        release()
    }

    private fun release() {
        start = 0
        end = 0
        isRunning = false
        updateListener = null
        isReleased = true
        synchronized(animPool) {
            animPool.offer(this)
        }
    }
}