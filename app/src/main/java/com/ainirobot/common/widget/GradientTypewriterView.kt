package com.ainirobot.common.widget

import android.animation.Animator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.Shader
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import android.view.animation.LinearInterpolator

@SuppressLint("AppCompatCustomView")
class GradientTypewriterView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var fadeInDuration = 500L
    private var fadeOutDuration = 100L

    private var fadeInAnim: Animator? = null
    private var fadeOutAnim: Animator? = null
    private var gradientAnim: ValueAnimator? = null

    private var process = 0.0f
    private var highlightOffset: Float = 0f

    private var typingText = ""
    
    private val textPaint = Paint().apply {
        isAntiAlias = true
        textSize = TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_SP,
            6f,
            context.resources.displayMetrics
        )
        color = 0xFFFFFFFF.toInt()
    }
    
    private val bgPaint = Paint().apply {
        isAntiAlias = true
        color = 0x33FFFFFF.toInt() // 半透明灰色背景
        style = Paint.Style.FILL
    }
    
    private val bgRect = RectF()
    private val cornerRadius = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP,
        10f,
        context.resources.displayMetrics
    )

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (fadeInAnim?.isRunning == true) {
            fadeInAnim?.cancel()
        }
        if (fadeOutAnim?.isRunning == true) {
            fadeOutAnim?.cancel()
        }
        gradientAnim?.cancel()
        gradientAnim?.removeAllListeners()
        gradientAnim = null
    }

    fun setText(text: String) {
        setTypingText(text)
    }

    fun setTypingText(text: String, completion: (() -> Unit)? = null) {
        if (text.isNotEmpty()) {
            typingText = text
            show(completion)
        }
    }

    private fun show(completion: (() -> Unit)?) {
        fadeInAnim?.apply {
            if (isRunning) {
                cancel()
            }
        }
        fadeOutAnim?.apply {
            if (isRunning) {
                cancel()
            }
        }
        gradientAnim?.cancel()
        
        this.visibility = VISIBLE
        
        // 打字动画
        fadeInAnim = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = fadeInDuration
            addUpdateListener {
                process = it.animatedValue as Float
                invalidate()
            }
            addListener(object : SimpleAnimatorListener() {
                override fun onEnd(isCancel: Boolean) {
                    if (isCancel) {
                        return
                    }
                    fadeInAnim = null
                    completion?.invoke()
                }
            })
            start()
        }
        
        // 渐变动画
        startGradientAnimation()
    }

    private fun startGradientAnimation() {
        gradientAnim = ValueAnimator.ofFloat(-0.5f, 1.5f).apply {
            duration = 2000
            repeatCount = ValueAnimator.INFINITE
            interpolator = LinearInterpolator()
            addUpdateListener { animator ->
                val textWidth = textPaint.measureText(typingText)
                highlightOffset = animator.animatedValue as Float * textWidth
                invalidate()
            }
            start()
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val displayText = typingText.substring(0, (typingText.length * process).toInt())
        val textWidth = textPaint.measureText(displayText)
        val textHeight = textPaint.textSize
        
        val desiredWidth = (textWidth + paddingStart + paddingEnd).toInt()
        val desiredHeight = (textHeight + paddingTop + paddingBottom).toInt()
        
        val measuredWidth = resolveSize(desiredWidth, widthMeasureSpec)
        val measuredHeight = resolveSize(desiredHeight, heightMeasureSpec)
        
        setMeasuredDimension(measuredWidth, measuredHeight)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        if (typingText.isEmpty()) return
        
        // 计算显示的文本
        val displayText = typingText.substring(0, (typingText.length * process).toInt())
        if (displayText.isEmpty()) return
        
        val textWidth = textPaint.measureText(displayText)
        
        // 计算文本位置（居中）
        val fontMetrics = textPaint.fontMetrics
        val textHeight = fontMetrics.bottom - fontMetrics.top
        val x = paddingStart.toFloat()
        val y = height / 2f - (fontMetrics.top + fontMetrics.bottom) / 2f
        
        // 绘制背景
        bgRect.set(
            0f,
            0f,
            textWidth + paddingStart + paddingEnd,
            height.toFloat()
        )
        canvas.drawRoundRect(bgRect, cornerRadius, cornerRadius, bgPaint)
        
        // 创建渐变
        val gradient = LinearGradient(
            highlightOffset, 0f,
            highlightOffset + textWidth * 0.5f, 0f,
            intArrayOf(
                0x80FFFFFF.toInt(),
                0xFFFFFFFF.toInt(),
                0x80FFFFFF.toInt()
            ),
            floatArrayOf(0f, 0.5f, 1f),
            Shader.TileMode.CLAMP
        )
        
        textPaint.shader = gradient
        
        canvas.drawText(displayText, x, y, textPaint)
        
        textPaint.shader = null
        
        // 请求重新测量，以便更新布局大小
        if (fadeInAnim?.isRunning == true) {
            requestLayout()
        }
    }

    fun hide(
        fadeOut: ((progress: Float) -> Unit)? = null,
        completion: (() -> Unit)? = null
    ) {
        if (fadeInAnim?.isRunning == true) {
            fadeInAnim?.cancel()
        }
        if (fadeOutAnim?.isRunning == true) {
            return
        }
        gradientAnim?.cancel()
        
        fadeOutAnim = ValueAnimator.ofFloat(process, 0f).apply {
            duration = fadeOutDuration
            addUpdateListener {
                val progress = it.animatedValue as Float
                process = progress
                if (progress < 0.15) {
                    fadeOut?.invoke(progress)
                }
                invalidate()
                requestLayout()
            }
            addListener(object : SimpleAnimatorListener() {
                override fun onEnd(isCancel: Boolean) {
                    if (isCancel) {
                        return
                    }
                    process = 0.0f
                    fadeOutAnim = null
                    visibility = GONE
                    completion?.invoke()
                }
            })
            start()
        }
    }
    
    fun show() {
        visibility = VISIBLE
        invalidate()
    }
}