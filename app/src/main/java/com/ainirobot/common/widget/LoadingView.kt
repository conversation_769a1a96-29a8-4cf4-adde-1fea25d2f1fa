package com.ainirobot.common.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.animation.ValueAnimator
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.annotation.ColorInt
import androidx.core.animation.addListener

class LoadingView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var dotColor: Int = Color.BLACK  // 默认颜色为灰色
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val dotCount = 4  // 圆点数量
    
    // 动画相关属性
    private var fadeInDuration: Long = 300    // 每个圆点淡入动画时长
    private var fadeOutDuration: Long = 80   // 每个圆点淡出动画时长
    private val alphas = FloatArray(dotCount) { 0f }  // 存储每个圆点的透明度
    private var isAnimating = false
    private var currentPhase = AnimPhase.NONE
    
    private var shouldContinue = false  // 添加控制循环的开关
    
    private var currentAnimator: ValueAnimator? = null  // 添加当前动画器的引用
    
    private enum class AnimPhase {
        NONE, FADE_IN, FADE_OUT
    }
    
    init {
        paint.color = dotColor
    }

    fun setDotColor(@ColorInt color: Int) {
        dotColor = color
        paint.color = color
        invalidate()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        // 确保宽度至少是高度的4倍，保证圆点不会重叠
        val height = MeasureSpec.getSize(heightMeasureSpec)
        val minWidth = height * 4
        val width = MeasureSpec.getSize(widthMeasureSpec).coerceAtLeast(minWidth)
        setMeasuredDimension(width, height)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val dotRadius = height / 2f
        val spacing = width.toFloat() / dotCount
        
        for (i in 0 until dotCount) {
            paint.alpha = (alphas[i] * 255).toInt()
            val cx = spacing * (i + 0.5f)
            val cy = height / 2f
            canvas.drawCircle(cx, cy, dotRadius, paint)
        }
    }

    // 开始循环动画
    fun start() {
        stop() // 先停止当前动画，确保从头开始
        shouldContinue = true
        isAnimating = true
        currentPhase = AnimPhase.FADE_IN
        startFadeInAnimation(0)
    }

    private fun startFadeInAnimation(dotIndex: Int) {
        if (!shouldContinue) return
        if (dotIndex >= dotCount) {
            currentPhase = AnimPhase.FADE_OUT
            startFadeOutAnimation(dotCount - 1)
            return
        }

        currentAnimator?.cancel()
        currentAnimator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = fadeInDuration
            interpolator = LinearInterpolator()
            addUpdateListener { animator ->
                alphas[dotIndex] = animator.animatedValue as Float
                invalidate()
            }
            addListener(onEnd = {
                if (shouldContinue) {
                    startFadeInAnimation(dotIndex + 1)
                }
            })
            start()
        }
    }

    private fun startFadeOutAnimation(dotIndex: Int) {
        if (!shouldContinue) return  // 检查是否应该继续
        if (dotIndex < 0) {
            isAnimating = false
            currentPhase = AnimPhase.NONE
            if (shouldContinue) {
                start()
            }
            return
        }

        currentAnimator?.cancel()  // 取消之前的动画
        currentAnimator = ValueAnimator.ofFloat(1f, 0f).apply {
            duration = fadeOutDuration
            interpolator = LinearInterpolator()
            addUpdateListener { animator ->
                alphas[dotIndex] = animator.animatedValue as Float
                invalidate()
            }
            addListener(onEnd = {
                if (shouldContinue) {  // 检查是否应该继续
                    startFadeOutAnimation(dotIndex - 1)
                }
            })
            start()
        }
    }

    // 设置动画时长的方法
    fun setAnimationDuration(fadeInDuration: Long, fadeOutDuration: Long) {
        this.fadeInDuration = fadeInDuration
        this.fadeOutDuration = fadeOutDuration
    }

    // 停止循环动画
    fun stop() {
        shouldContinue = false
        currentAnimator?.cancel()  // 取消当前动画
        isAnimating = false
        currentPhase = AnimPhase.NONE
        // 重置所有圆点的透明度
        alphas.fill(0f)
        invalidate()
    }

    // 当View被移除时确保清理动画
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stop()
        currentAnimator?.removeAllListeners()
        currentAnimator = null
    }

    fun hide() {
        visibility = GONE
        stop()
    }

    fun show() {
        visibility = VISIBLE
        start()
    }
}