package com.ainirobot.common.widget

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import coil.load
import coil.transform.CircleCropTransformation
import com.ainirobot.agent.R
import com.ainirobot.common.runOnMain

sealed class CommonDialog(val context: Context) : DialogInterface.OnDismissListener, DialogInterface.OnCancelListener {

    private var impl: Dialog? = null

    private var dismissListener: (() -> Unit)? = null
    private var cancelListener: (() -> Unit)? = null

    val isShowing: Boolean
        get() = impl?.isShowing == true

    abstract fun onCancel(): Boolean

    abstract fun onConfirm(): Boolean

    protected fun easyShow(dialog: Dialog, tag: String? = null, dismissDelay: Long = 0) {
        dialog.window?.run {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY)
            } else {
                setType(WindowManager.LayoutParams.TYPE_PHONE)
            }
        }
        dialog.setOnDismissListener(this)
        dialog.setOnCancelListener(this)
        impl = dialog
        runOnMain { EasyDialog.easyShow(this, tag, dismissDelay) }
    }

    fun setOnDismissListener(listener: () -> Unit): CommonDialog {
        dismissListener = listener
        return this
    }

    fun setOnCancelListener(listener: () -> Unit): CommonDialog {
        cancelListener = listener
        return this
    }

    fun showImpl() {
        impl?.show()
    }

    override fun onCancel(dialog: DialogInterface?) {
        cancelListener?.invoke()
    }

    override fun onDismiss(dialog: DialogInterface?) {
        dismissListener?.invoke()
    }

    fun cancel() {
        impl?.cancel()
    }

    fun dismiss() {
        impl?.dismiss()
    }
}

data class Item(
    val id: Int,
    val title: String,
    val subtitle: String? = null,
    val imageUrl: String? = null,
)

class SelectableDialog(
    context: Context,
    private val title: String,
    private val items: List<Item>,
    private val tag: String,
    private val dismissDelay: Long = 30000
) : CommonDialog(context) {

    private class ItemViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val avatarView = view.findViewById<ImageView>(R.id.avatar_view)!!
        val titleView = view.findViewById<TextView>(R.id.title_view)!!
        val subtitleView = view.findViewById<TextView>(R.id.subtitle_view)!!
    }

    private class ItemAdapter(
        private val items: List<Item>,
        private val onClickItemListener: (Item) -> Unit,
    ) : RecyclerView.Adapter<ItemViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.view_item_select, parent, false)
            return ItemViewHolder(view)
        }

        override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
            val item = items[position]
            holder.avatarView.load(item.imageUrl) {
                crossfade(true)
                placeholder(R.drawable.ic_avatar)
                transformations(CircleCropTransformation())
            }
            holder.titleView.text = item.title
            holder.subtitleView.text = item.subtitle
            holder.itemView.setOnClickListener {
                onClickItemListener(item)
            }
        }

        override fun getItemCount(): Int = items.size
    }

    fun show(onClickItemListener: (Item) -> Unit) {
        runOnMain { showInternal(onClickItemListener) }
    }

    private fun showInternal(onClickItemListener: (Item) -> Unit) {
        val dialog = Dialog(context, R.style.Theme_Dialog)
        dialog.setContentView(R.layout.dialog_list_select)
        dialog.findViewById<TextView>(R.id.title_view).text = title
        dialog.findViewById<View>(R.id.cancel_btn).setOnClickListener {
            dialog.cancel()
        }
        dialog.findViewById<RecyclerView>(R.id.recycler_view)?.let { view ->
            view.layoutManager = LinearLayoutManager(context)
            view.adapter = ItemAdapter(items) {
                onClickItemListener(it)
                dialog.dismiss()
            }
        }
        dialog.setCancelable(false)
        easyShow(dialog, tag, dismissDelay)
    }

    override fun onCancel(): Boolean {
        if (isShowing) {
            runOnMain {
                cancel()
            }
            return true
        }
        return false
    }

    override fun onConfirm(): Boolean {
        return false
    }
}

class KAlertDialog(
    context: Context,
    private val title: CharSequence,
    private val message: CharSequence? = null,
    private val layoutId: Int = -1,
    private val layoutInit: ((View) -> Unit)? = null,
    private val positiveText: String? = null,
    private val negativeText: String? = null,
    private val positiveListener: (() -> Unit)? = null,
    private val negativeListener: (() -> Unit)? = null,
    private val cancelable: Boolean = false,
    private val dismissDelay: Long = 30000
) : CommonDialog(context) {

    fun show(tag: String? = null) {
        runOnMain { showInternal(tag) }
    }

    private fun showInternal(tag: String?) {
        if (layoutId == -1 && message == null) {
            throw IllegalArgumentException("layoutId and message cannot be null at the same time")
        }
        val dialog = Dialog(context, R.style.Theme_Dialog)
        dialog.setCancelable(cancelable)
        dialog.setContentView(R.layout.alert_dialog)
        dialog.findViewById<TextView>(R.id.title_view).text = title
        val messageView = dialog.findViewById<TextView>(R.id.message_view)
        if (message != null) {
            messageView.text = message
        } else {
            val container = dialog.findViewById<FrameLayout>(R.id.content_view)
            messageView.visibility = View.GONE
            container.visibility = View.VISIBLE
            val newView = LayoutInflater.from(context).inflate(layoutId, container, false)
            layoutInit?.invoke(newView)
            container.addView(newView)
        }
        val positiveBtn = dialog.findViewById<TextView>(R.id.positive_btn)
        val negativeBtn = dialog.findViewById<TextView>(R.id.negative_btn)
        positiveBtn.apply {
            if (positiveText != null) {
                text = positiveText
            } else {
                visibility = View.GONE
                val params = negativeBtn.layoutParams as ConstraintLayout.LayoutParams
                params.leftToLeft = ConstraintLayout.LayoutParams.PARENT_ID
                params.rightToRight = ConstraintLayout.LayoutParams.PARENT_ID
                negativeBtn.requestLayout()
            }
            setOnClickListener {
                dialog.dismiss()
                positiveListener?.invoke()
            }
        }
        negativeBtn.apply {
            if (negativeText != null) {
                text = negativeText
            } else {
                visibility = View.GONE
                val params = positiveBtn.layoutParams as ConstraintLayout.LayoutParams
                params.leftToLeft = ConstraintLayout.LayoutParams.PARENT_ID
                params.rightToRight = ConstraintLayout.LayoutParams.PARENT_ID
                positiveBtn.requestLayout()
            }
            setOnClickListener {
                dialog.dismiss()
                negativeListener?.invoke()
            }
        }
        easyShow(dialog, tag, dismissDelay)
    }

    override fun onCancel(): Boolean {
        if (isShowing) {
            runOnMain {
                cancel()
            }
            return true
        }
        return false
    }

    override fun onConfirm(): Boolean {
        val listener = positiveListener
        if (isShowing && listener != null) {
            runOnMain {
                listener.invoke()
                dismiss()
            }
            return true
        }
        return false
    }
}