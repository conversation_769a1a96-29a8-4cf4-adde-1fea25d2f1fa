package com.ainirobot.common.widget

import android.os.Handler
import android.os.Looper
import android.os.Message
import java.lang.ref.WeakReference
import java.util.Deque
import java.util.LinkedList

object EasyDialog {

    private data class DialogEntity(
        val tag: String,
        val dialog:  WeakReference<CommonDialog>
    )
    
    private const val MSG_DISMISS_DIALOG = 1001

    private val dialogs: Deque<DialogEntity> = LinkedList()

    private val handler = object : Handler(Looper.getMainLooper()) {

        override fun handleMessage(msg: Message) {
            if (msg.what == MSG_DISMISS_DIALOG) {
                dismiss(msg.obj as String)
            }
        }
    }

    fun easyShow(dialog: CommonDialog, tag: String? = null, dismissDelay: Long) {
        if (tag == null && dismissDelay <= 0) {
            dialog.showImpl()
            return
        }

        val newTag = tag ?: "dialog_${System.currentTimeMillis()}"
        synchronized(dialogs) {
            val existingDialogRef = dialogs.getEntity(newTag)?.dialog
            if (existingDialogRef != null) {
                val existingDialog = existingDialogRef.get()
                if (existingDialog != null && existingDialog.isShowing) {
                    existingDialog.cancel()
                } else {
                    dialogs.removeEntity(newTag)
                }
            }
        }
        dialog.setOnDismissListener {
            synchronized(dialogs) {
                dialogs.removeEntity(newTag)
            }
            handler.removeMessages(MSG_DISMISS_DIALOG, newTag)
        }
        dialog.showImpl()

        handler.postDelayed(
            {
                synchronized(dialogs) {
                    dialogs.offerFirst(DialogEntity(newTag, WeakReference(dialog)))
                }
                if (dismissDelay > 0) {
                    handler.sendMessageDelayed(
                        Message.obtain(handler, MSG_DISMISS_DIALOG, newTag),
                        dismissDelay
                    )
                }
            },
            100
        )
    }

    private fun dismiss(tag: String) {
        synchronized(dialogs) {
            dialogs.removeEntity(tag)?.dialog?.get()?.cancel()
        }
    }

    private fun Deque<DialogEntity>.getEntity(tag: String): DialogEntity? {
        for (entity in this) {
            if (entity.tag == tag) {
                return entity
            }
        }
        return null
    }

    private fun Deque<DialogEntity>.removeEntity(tag: String): DialogEntity? {
        val entity = getEntity(tag)
        if (entity != null) {
            remove(entity)
        }
        return entity
    }

    fun onEventReceived(cancel: Boolean): Boolean {
        synchronized(dialogs) {
            var top = dialogs.pollFirst()
            while (top != null) {
                val dialog = top.dialog.get()
                if (dialog == null || !dialog.isShowing) {
                    top = dialogs.pollFirst()
                    continue
                }
                return if (cancel) {
                    dialog.onCancel()
                } else {
                    dialog.onConfirm()
                }
            }
        }
        return false
    }
}
