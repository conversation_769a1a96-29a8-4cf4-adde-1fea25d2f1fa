package com.ainirobot.common.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.widget.ImageView
import com.github.penfeizhou.animation.webp.WebPDrawable

@SuppressLint("AppCompatCustomView")
class AnimImageView : ImageView {

    private var animRes: Array<WebPDrawable>? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int)
            : super(context, attrs, defStyleAttr)

    fun setAnim(animRes: Array<WebPDrawable>) {
        this.animRes = animRes
    }

    override fun setImageDrawable(image: Drawable?) {
        val old = drawable
        if (old is WebPDrawable && old.isRunning) {
            old.setVisible(false, false)
        }
        super.setImageDrawable(image)
    }

    fun hide() {
        val old = drawable
        if (old is WebPDrawable && old.isRunning) {
            old.setVisible(false, false)
        }
        this.visibility = GONE
    }

    fun show(animIndex: Int = 0) {
        this.visibility = VISIBLE
        val anims = animRes ?: return
        if (animIndex < 0 || animIndex >= anims.size) {
            return
        }
        val newImage = anims[animIndex]
        val oldImage = drawable
        if (oldImage is WebPDrawable && oldImage.isRunning) {
            if (oldImage == newImage) {
                return
            }
            oldImage.setVisible(false, false)
        }
        setImageDrawable(newImage)
        newImage.setVisible(true, true)
    }
}