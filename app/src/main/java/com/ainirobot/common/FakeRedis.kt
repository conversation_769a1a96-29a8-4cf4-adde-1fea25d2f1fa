package com.ainirobot.common

data class RedisEntity(
    val key: String,
    val value: Any,
    val expiryTime: Long
) {
    fun isExpired(): Boolean {
        return expiryTime > 0 && expiryTime < System.currentTimeMillis()
    }
}

class FakeRedis private constructor(val name: String) {

    companion object {

        private val instances = mutableMapOf<String, FakeRedis>()

        fun create(name: String): FakeRedis {
            synchronized(instances) {
                return instances.getOrPut(name) { FakeRedis(name) }
            }
        }
    }

    private val entitiesPool = mutableMapOf<String, RedisEntity>()

    @Suppress("UNCHECKED_CAST")
    fun <T> getValue(key: String, default: T? = null): T? {
        return get(key)?.let {
            it.value as T
        } ?: default
    }

    operator fun get(
        key: String
    ): RedisEntity? {
        synchronized(entitiesPool) {
            val entity = entitiesPool[key] ?: return null
            if (entity.isExpired()) {
                entitiesPool.remove(key)
            }
            return entity
        }
    }

    fun put(key: String, value: Any, expiryMillis: Long = 0L) {
        synchronized(entitiesPool) {
            entitiesPool[key] = RedisEntity(key, value, if (expiryMillis > 0) System.currentTimeMillis() + expiryMillis else 0)
        }
    }

    fun remove(key: String) {
        synchronized(entitiesPool) {
            entitiesPool.remove(key)
        }
    }

    fun clear() {
        synchronized(entitiesPool) {
            entitiesPool.clear()
        }
    }
}
