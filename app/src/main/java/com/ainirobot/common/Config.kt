package com.ainirobot.common

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.ainirobot.agent.BuildConfig
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.KLog.e
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

object Config {

    private lateinit var contextRef: WeakReference<Context>

    var debugMode: Boolean = BuildConfig.DEBUG_MODE_ENABLE

    val releaseMode: Boolean get() = !debugMode

    val actionVersion: String get() {
        return if (BuildConfig.DEBUG_MODE_ENABLE) {
            if (BuildConfig.REGION_OVERSEA) {
                "oversea_draft"
            } else {
                "draft"
            }
        } else {
            if (BuildConfig.REGION_OVERSEA) {
                BuildConfig.AGENT_ACTION_VERSION_OVERSEA
            } else {
                BuildConfig.AGENT_ACTION_VERSION_DOMESTIC
            }
        }
    }

    val context: Context get() = contextRef.get()!!

    val gson = Gson()

    val prettyGson = GsonBuilder().setPrettyPrinting().create()!!

    private val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        exception.e("Config")
    }

    val mainScope = CoroutineScope(Dispatchers.Main + exceptionHandler + SupervisorJob())

    val ioScope = CoroutineScope(Dispatchers.IO + exceptionHandler + SupervisorJob())

    val defaultScope = CoroutineScope(Dispatchers.Default + exceptionHandler + SupervisorJob())

    /**
     * 是否为多任务规划版本
     */
    const val isMultiActionVersion: Boolean = false

    /**
     * TTS播放的速度，单位：秒/字
     */
    private const val TTS_SPEED = 0.45f

    /**
     * 计算TTS播放时长，单位：毫秒；
     */
    fun calculateTtsDuration(text: String): Long {
        return (text.length * TTS_SPEED * 1000).toLong()
    }

    /**
     * 小豹APP
     */
    const val MODULE_APP = "com.ainirobot.moduleapp"

    /**
     * Launcher的AgentId
     */
    const val AGENT_ID_LAUNCHER = "agent_B9SLEgAM2c2AJNxw"

    /**
     * 唤醒id
     */
    var wakeupId: String = ""

    private const val BR_ACTION_WAKEUP_ID = "action_wakeup_id"

    private val receiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context?, intent: Intent?) {
            if (context == null || intent == null) {
                return
            }
            if (intent.action == BR_ACTION_WAKEUP_ID) {
                <EMAIL> = intent.getStringExtra("data_wakeup_id") ?: ""
                KLog.d("wakeupId: $wakeupId", "Config")
            }
        }
    }

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    fun initialize(context: Context) {
        this.contextRef = WeakReference(context)
        ConnectivityAssist.initialize(context)
        context.registerReceiver(
            receiver,
            IntentFilter(BR_ACTION_WAKEUP_ID),
            "com.ainirobot.permission.WAKEUP_ID_RECEIVER",
            null
        )
    }
}

fun <T> T.runOnMain(block: suspend (T) -> Unit) {
    Config.mainScope.launch {
        block(this@runOnMain)
    }
}

fun <T> T.runOnDefault(block: suspend (T) -> Unit) {
    Config.defaultScope.launch {
        block(this@runOnDefault)
    }
}