package com.ainirobot.agent.action

import com.ainirobot.agent.core.PlanMessage
import com.ainirobot.agent.core.RunningStatus
import com.ainirobot.bi.RTPlanElapse
import com.ainirobot.bi.RTPlanTrace
import com.ainirobot.common.DataStore
import com.ainirobot.common.utils.DateUtil.dateFormat
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * Plan执行过程追踪记录
 */
class ActionMonitor(message: PlanMessage) {

    companion object {

        private val appNameMapping = mapOf(
            "system_bf6d2b672273c7369fa557efd61d7a07" to "小豹App",
            "system_640ce92cb1553953254fc90ae92ea9bd" to "用户信息注册",
            "system_17bf9cfc230d17c94a19a0dc4faa6569" to "日历",
            "system_a8c9098ad7a91a66287b25d6befef6ec" to "天气",
            "system_00760f1d425189480a4f957fdeefe77a" to "导览",
            "system_f1383df09054ea2ae5ce28a977b54a5d" to "网页",
            "system_fa1c059ee428a9d21adf393a1d8fa011" to "导航引领",
            "system_d5a64441247aa63b70dd8d02e3f753f0" to "配置欢迎语",
            "system_01917709e2d711f26014d0f1e78295fb" to "拍照",
            "system_2184a8c5d2989bc1b8e803cf3dead414" to "跳舞",
            "system_c1bd1ef2fa34da4b577687b7f3c73263" to "OverSea_Advert",
            "system_a4cb5674016737343873a1008f107b60" to "OverSea_Chat",
            "system_0c6c62e582b89f1752a712cb562694ec" to "OverSea_Cruise",
            "system_ccfda69b10253ec17722dfd329d3bf01" to "OverSea_Dance",
            "system_33e51e3e560ef8dab9f29cb1aaa7058c" to "OverSea_Guide",
            "system_51ccbfd5276f094369f90484dbd488fd" to "OverSea_Leading",
            "system_f769799caf1f88c5c87e0e80eb12fa6b" to "OverSea_Navigation",
            "system_4f307c6d4cb0f187a3edb3dcc6f43749" to "OverSea_Portal",
            "system_95b3de1d8f68b33892e5ac2b42662517" to "OverSea_Web",
            "system_024280e32e73c00ad621710870d4cb18" to "OverSea_Weather",
            "system_f9c8000e22cc81eb14348790428b21a0" to "OverSea_Calendar"
        )

        private val lock = Mutex()
        private val runningActions = mutableMapOf<String, ActionNode>()

        suspend fun isRunningAction(actionName: String): Boolean {
            lock.withLock { return runningActions.containsKey(actionName) }
        }

        /**
         * 获取正在执行的本地Action，包含Robot和Both两种
         */
        suspend fun getRunningLocalAction(filter: (node: ActionNode) -> Boolean): ActionNode? {
            lock.withLock {
                for (node in runningActions.values) {
                    if ((node.executionSide == ExecutionSide.ROBOT
                                || node.executionSide == ExecutionSide.BOTH) && filter(node)) {
                        return node
                    }
                }
            }
            return null
        }
    }

    private val report: RTPlanTrace = RTPlanTrace()
        .setVersion(message.version)
        .setMsgId(message.id)
        .setQueryId(message.queryId)
        .setUserQuery(message.query)
        .setPlanId(message.content.id)
        .setRunId(message.content.runId)
        .setPlanType(message.content.type)
        .setReceivedTime(System.currentTimeMillis().dateFormat())

    private val elapseReport: RTPlanElapse = RTPlanElapse()
        .setQueryId(message.queryId)
        .setUserQuery(message.query)
        .setRunId(message.content.runId)
        .setReceivedTime(System.currentTimeMillis().dateFormat())

    private val executionDetail = StringBuilder()

    init {
        message.elapseInfo?.forEach { (key, value) ->
            if (value is Double) {
                val time = (value * 1000).toLong()
                if (time > 86400000) {
                    elapseReport.setField(key, time.dateFormat(format = "yyyy-MM-dd HH:mm:ss.SSS"))
                } else {
                    elapseReport.setField(key, time.toInt())
                }
            } else if (value is String)  {
                elapseReport.setField(key, value.toString())
            }
        }
    }

    fun onStart(node: PlanNode) {
        report.setPlanContent(node.toString())
            .setStartTime(System.currentTimeMillis().dateFormat())
            .setPackageName(DataStore.currentPackageName)
            .setAppName(appNameMapping[DataStore.currentAppId] ?: "")
            .setAppId(DataStore.currentAppId)
            .setPageId(DataStore.currentPageId)
        elapseReport.setStartTime(System.currentTimeMillis().dateFormat(format = "yyyy-MM-dd HH:mm:ss.SSS"))
    }

    suspend fun onActionStart(node: ActionNode) {
        lock.withLock { runningActions[node.name] = node }
        executionDetail.append(System.currentTimeMillis().dateFormat())
            .append(" Action: ")
            .append(node.displayName)
            .append("(${node.name}) start execute\n")
    }

    suspend fun onActionEnd(
        node: ActionNode,
        completion: ActionCompletion,
    ) {
        lock.withLock { runningActions.remove(node.name) }
        executionDetail.append(System.currentTimeMillis().dateFormat())
            .append(" Action: ")
            .append(node.displayName)
            .append("(${node.name}) end execute, params: ")
        node.parameters.forEach {
            if (it.key != "_CONTEXT") {
                executionDetail.append(it.key).append("=").append(it.value).append(", ")
            }
        }
        executionDetail.append("success: ")
            .append(completion.isSuccessful)
            .append(", message: ")
            .append(completion.message)
        completion.result?.let {
            executionDetail.append(", result: ").append(it)
        }
        executionDetail.append("\n")
    }

    fun onEnd(status: RunningStatus, elapseTime: Int) {
        report.setExecutionDetail(executionDetail.toString())
            .setStatus(status)
            .setExecutionElapseTime(elapseTime)
            .report()
        elapseReport.setExecutionElapseTime(elapseTime)
           .report()
    }
}