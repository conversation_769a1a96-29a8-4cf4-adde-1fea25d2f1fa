package com.ainirobot.agent.action

import com.ainirobot.agent.core.RunningStatus
import com.ainirobot.common.Config
import java.lang.ref.WeakReference

data class ActionCompletion(
    val status: String = SUCCESS,
    val result: Map<String, *>? = null,
    val message: String = ""
) {

    companion object {
        const val UNSUPPORTED = "unsupported"
        const val SUCCESS = "succeeded"
        const val FAILED = "failed"
        const val TIMEOUT = "timeout"
        const val INTERRUPTED = "interrupted"
        const val RECALLED = "recalled"
    }

    val isSuccessful: Boolean get() = status == SUCCESS

    val isTimeout: Boolean get() = status == TIMEOUT

    val isCompleted: Boolean get() = status == SUCCESS || status == RECALLED || status == INTERRUPTED

    fun toStatus(): RunningStatus {
        return when (status) {
            SUCCESS -> {
                RunningStatus.SUCCEEDED
            }
            INTERRUPTED -> {
                RunningStatus.INTERRUPTED
            }
            RECALLED -> {
                RunningStatus.RECALLED
            }
            else -> {
                RunningStatus.FAILED
            }
        }
    }
}

open class CommonEvent(
    open val name: String,
    open val params: Map<String, *>
)

open class AgentEvent(
    name: String,
    params: Map<String, *>,
    open val oneway: Boolean = true,
    open val timeout: Long = 0
) : CommonEvent(name, params) {

    open suspend fun notifyExecuteStatus(status: ActionCompletion) { }
}

class ActionEvent(
    private val schedulerRef: WeakReference<ActionBus.Scheduler>? = null,
    name: String,
    val planId: String,
    val runId: String,
    var appId: String, // 规划此action时当前app的appId
    val pageId: String, // 规划此action时当前页面的pageId
    val displayName: String = "",
    val actionId: String = "",
    timeout: Long = Long.MAX_VALUE,
    val side: ExecutionSide = ExecutionSide.ROBOT,
    /**
     * 是否单向action，true则不需要回调执行结果
     */
    oneway: Boolean = false,
    val userQuery: String = "",
    /**
     * action执行所需参数
     */
    params: Map<String, *>,
) : AgentEvent(name, params, oneway, timeout) {

    /**
     * 服务端执行的Action预执行结果
     */
    val preemptiveResult: Any? get() {
        return if (side == ExecutionSide.SERVER) {
            val entity = schedulerRef?.get()?.redis?.get("${runId}_$actionId")
            if (entity != null && !entity.isExpired()) {
                entity.value
            } else null
        } else null
    }

    /**
     * 通知ActionEvent执行状态，0不支持，1成功，2失败，3超时
     */
    override suspend fun notifyExecuteStatus(status: ActionCompletion) {
        if (oneway) {
            return
        }
        val scheduler = schedulerRef?.get()
        if (scheduler == null) {
            // 超时时有多种超时机制，超时时长应该是层层递减，如果统一则可能会出现以下异常
            // 目前已经要根节点处理，但安全起见，此处亦添加判断
            if (!status.isTimeout && Config.debugMode) {
                throw IllegalStateException(
                    "Scheduler not found, planId: $planId, runId: $runId, actionId: $actionId, actionName: $name"
                )
            }
        }
        scheduler?.next(actionId, status)
    }
}
