package com.ainirobot.agent.action

import android.content.Context
import com.ainirobot.common.Config.gson
import com.google.gson.annotations.SerializedName
import com.google.gson.stream.JsonReader
import java.io.File
import java.io.FileReader
import java.io.InputStream
import java.io.InputStreamReader

data class ActionProfileInfo(
    @SerializedName("version")
    val version: String,
    @SerializedName("actions")
    val actions: List<ActionSchema>
)

data class ActionSchema(
    @SerializedName("namespace")
    val namespace: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("level")
    val level: String,
    @SerializedName("display_name")
    val displayName: String,
    @SerializedName("execute_side")
    val executionSide: ExecutionSide,
    @SerializedName("parameters")
    val parameters: List<Parameter>,
) {

    /**
     * 是否是应用内的Action。
     */
    val isInApp: <PERSON>olean
        get() = level == "launcher" || level == "opk"
}

data class Parameter(
    @SerializedName("name")
    val name: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("is_required")
    val required: Boolean
)

class ActionProfile(private val context: Context) {

    companion object {

        private const val FILE_NAME = "actions_config.json"

        private const val FIELD_VERSION = "version"

        private fun InputStream.readVersion(): String? {
            return JsonReader(InputStreamReader(this)).use { reader ->
                reader.beginObject()
                var version: String? = null
                while (reader.hasNext()) {
                    if (reader.nextName() == FIELD_VERSION) {
                        version = reader.nextString()
                    } else {
                        reader.skipValue()  // Skip other keys
                    }
                }
                reader.endObject()
                version
            }
        }

        /**
         * 比较版本号
         * @param version1 第一个版本号字符串（例如："1.0.1"）。
         * @param version2 第二个版本号字符串（例如："1.0.0"）。
         * @return 如果 version1 比 version2 大（更新），则返回正数；
         *         如果 version1 比 version2 小（更旧），则返回负数；
         *         如果两个版本号相同，则返回零。
         */
        private fun compareVersion(version1: String?, version2: String?): Int {
            if (version1 == null && version2 == null) return 0
            if (version1 == null) return -1
            if (version2 == null) return 1

            val parts1 = version1.split(".")
            val parts2 = version2.split(".")
            val maxLength = maxOf(parts1.size, parts2.size)

            for (i in 0 until maxLength) {
                val part1 = parts1.getOrNull(i)?.toIntOrNull() ?: 0
                val part2 = parts2.getOrNull(i)?.toIntOrNull() ?: 0
                if (part1 != part2) {
                    return part1.compareTo(part2)
                }
            }
            return 0
        }
    }

    private val profileFile = File(context.filesDir, FILE_NAME)
    private val actionSchemas = mutableMapOf<String, ActionSchema>()

    fun initialize() {
        if (checkUpdateFromAssets()) {
            context.assets.open(FILE_NAME).use { input ->
                profileFile.outputStream().use { output ->
                    input.copyTo(output)
                }
            }
        }
        parseActionProfile()?.also { profile ->
            actionSchemas.clear()
            profile.actions.forEach { action ->
                actionSchemas[action.name] = action
            }
        }
    }

    operator fun get(name: String): ActionSchema? {
        return actionSchemas[name]
    }

    /**
     * 检查是否可以从assets更新本地配置；
     */
    private fun checkUpdateFromAssets(): Boolean {
        if (!profileFile.exists()) {
            return true
        }

        return compareVersion(
            context.assets.open(FILE_NAME).readVersion(),
            profileFile.inputStream().readVersion()
        ).let { it > 0 }
    }

    private fun parseActionProfile(): ActionProfileInfo? {
        return try {
            FileReader(profileFile).use { reader ->
                gson.fromJson(reader, ActionProfileInfo::class.java)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}