package com.ainirobot.agent.action

import com.ainirobot.agent.action.ActionParser.Companion.TAG_FALLBACK
import com.ainirobot.agent.action.ActionParser.Companion.TAG_PLAN
import com.ainirobot.agent.action.ActionParser.Companion.TAG_SEQUENCE
import com.ainirobot.agent.core.RunningStatus

sealed class CompositeNode: Node() {

    private val _children = mutableListOf<Node>()
    val children: List<Node> = _children

    override fun setScheduler(scheduler: ActionBus.Scheduler) {
        super.setScheduler(scheduler)
        for (child in children) {
            child.setScheduler(scheduler)
        }
    }

    fun addChild(node: Node) {
        _children.add(node)
    }

    override fun toString(): String {
        val builder = StringBuilder()
        builder.append("<${tag} id=$id name=$name>")
        for (child in children) {
            builder.append(child.toString())
        }
        builder.append("</$tag>")
        return builder.toString()
    }
}

class SequenceNode(
    override val id: String,
    override val name: String
): CompositeNode() {

    override val tag: String
        get() = TAG_SEQUENCE

    override suspend fun execute(): ActionCompletion {
        sendStatus(actionStatus = RunningStatus.RUNNING)
        for (child in children) {
            val completion = child.execute()
            if (!completion.isSuccessful) {
                sendStatus(actionStatus = completion.toStatus())
                return completion
            }
        }
        sendStatus(actionStatus = RunningStatus.SUCCEEDED)
        return ActionCompletion()
    }
}

class FallbackNode(
    override val id: String,
    override val name: String
): CompositeNode() {

    override val tag: String
        get() = TAG_FALLBACK

    override suspend fun execute(): ActionCompletion {
        sendStatus(actionStatus = RunningStatus.RUNNING)
        for (child in children) {
            val completion = child.execute()
            if (completion.isCompleted) {
                sendStatus(actionStatus = completion.toStatus())
                return completion
            }
        }
        sendStatus(actionStatus = RunningStatus.FAILED)
        return ActionCompletion(ActionCompletion.FAILED)
    }
}

class PlanNode(override val id: String): CompositeNode() {

    override val name: String
        get() = "plan"

    override val tag: String
        get() = TAG_PLAN

    override suspend fun execute(): ActionCompletion {
        sendStatus(planStatus = RunningStatus.RUNNING)
        for (child in children) {
            val completion = child.execute()
            if (!completion.isSuccessful) {
                sendStatus(planStatus = completion.toStatus())
                return completion
            }
        }
        sendStatus(planStatus = RunningStatus.SUCCEEDED)
        return ActionCompletion()
    }
}
