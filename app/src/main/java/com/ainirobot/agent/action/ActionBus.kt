package com.ainirobot.agent.action

import com.ainirobot.agent.core.Plan
import com.ainirobot.agent.core.PlanMessage
import com.ainirobot.agent.core.ResultMessage
import com.ainirobot.agent.core.RunningStatus
import com.ainirobot.bi.RTPlanStatus
import com.ainirobot.bi.RTQueryTrace
import com.ainirobot.common.FakeRedis
import com.ainirobot.agent.base.utils.DigitalUtil
import com.ainirobot.common.utils.KLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlin.coroutines.CoroutineContext

class ActionBus(
    coroutineContext: CoroutineContext
) {

    class Scheduler(
        val planId: String,
        val runId: String,
        val appId: String, // 规划此action时当前app的appId
        val pageId: String, // 规划此action时当前页面的pageId
        val planNode: PlanNode,
        val monitor: ActionMonitor,
        val redis: FakeRedis,
        val emitter: MutableSharedFlow<AgentEvent>
    ) {

        constructor(
            plan: Plan,
            monitor: ActionMonitor,
            redis: FakeRedis,
            emitter: MutableSharedFlow<AgentEvent>
        ): this(
            plan.id,
            plan.runId,
            plan.appId ?: "",
            plan.pageId ?: "",
            ActionParser(
                plan.xml
            ).parse(),
            monitor,
            redis,
            emitter
        )

        init {
            planNode.setScheduler(this)
        }

        val id: String get() = "$planId-$runId"

        /**
         * 客户端Action执行完成状态传输通道
         */
        val completionChannel = Channel<Pair<String, ActionCompletion>>()

        /**
         * 开始执行Plan
         */
        suspend fun start(): ActionCompletion {
            monitor.onStart(planNode)
            return planNode.execute()
        }

        /**
         * 发送客户端Action执行完成状态，触发执行下一步
         */
        suspend fun next(actionId: String, status: ActionCompletion) {
            completionChannel.send(actionId to status)
        }
    }

    private val coroutineScope = CoroutineScope(coroutineContext + Dispatchers.IO + SupervisorJob())
    private val lock = Mutex()
    private val schedulers = mutableMapOf<String, Scheduler>()
    private val redis = FakeRedis.create("action_preload_result_pool")

    /**
     * 是否有正在执行的Action
     */
    val hasRunningAction: Boolean
        get() = schedulers.isNotEmpty()

    val agentEventEmitter = MutableSharedFlow<AgentEvent>(extraBufferCapacity = Int.MAX_VALUE)
    val commonEventEmitter = MutableSharedFlow<CommonEvent>(extraBufferCapacity = Int.MAX_VALUE)

    suspend fun onPlanMessageReceived(message: PlanMessage) {
        val plan = message.content
        executeCommonEvent(
            CommonEvent(
                name = "plan.received",
                params = mapOf(
                    "query_id" to message.queryId,
                    "plan_xml" to plan.xml
                ) + (message.elapseInfo ?: emptyMap()),
            )
        )
        KLog.d("plan message id: ${message.id}, plan.status: ${plan.status}", "AgentBus")
        if (plan.status != "succeeded") {
            reportPlanStatus(message, 0)
            return
        }
        RTQueryTrace.reportPlanReceived(message.queryId)
        if (plan.executeImmediately) {
            schedule(
                Scheduler(
                    plan,
                    ActionMonitor(message),
                    redis,
                    agentEventEmitter
                ),
                message
            )
        } else if (plan.waitingReason == "waiting_confirm") {
            reportPlanStatus(message, 3)
            executeCommonEvent(
                CommonEvent(
                    name = "plan.show",
                    params =  mapOf("plan_xml" to plan.xml)
                )
            )
        }
    }

    suspend fun executeAction(
        actionName: String,
        executionSide: ExecutionSide,
        timeout: Int,
        params: Map<String, *>,
        appId: String = "",
        pageId: String = ""
    ) {
        val plan = Plan(
            id = DigitalUtil.uuid(),
            runId = DigitalUtil.uuid(),
            appId = appId ?: "",
            pageId = pageId,
            type = "single_action",
            status = "succeeded",
            message = "",
            xml = "",
            executeImmediately = true
        )
        val message = PlanMessage(
            version = "client_execution",
            queryId = DigitalUtil.shortUUID(),
            query = actionName,
            content = plan
        )
        val planNode = PlanNode(DigitalUtil.uuid())
        planNode.addChild(
            SequenceNode(
                "seq_1",
                "root_sequence"
            ).also {
                it.addChild(
                    ActionNode(
                        id = "act_0",
                        name = actionName,
                        displayName = "",
                        executionSide = executionSide,
                        timeout = timeout,
                        parameters = params
                    )
                )
            }
        )
        this.schedule(
            Scheduler(
                planId = plan.id,
                runId = plan.runId,
                appId = plan.appId ?: "",
                pageId = plan.pageId,
                planNode = planNode,
                monitor = ActionMonitor(message),
                redis = redis,
                emitter = agentEventEmitter
            ),
            message
        )
    }

    private suspend fun schedule(scheduler: Scheduler, message: PlanMessage) {
        reportPlanStatus(message, 1)
        val t1 = System.currentTimeMillis()
        // 相同类型的Plan的id相同，每次下发的BT的runId是不同的
        val schedulerId = scheduler.id
        lock.withLock {
            schedulers[schedulerId] = scheduler
        }
        coroutineScope.launch {
            KLog.d("========Plan Execute Start=============", "AgentBus")
            val status = scheduler.start().toStatus()
            lock.withLock {
                schedulers.remove(schedulerId)
            }
            val elapseTime = (System.currentTimeMillis() - t1).toInt()
            KLog.d("========Plan Execute End: $status, ElapseTime: $elapseTime=============", "AgentBus")
            reportPlanStatus(message, 2, elapseTime)
            scheduler.monitor.onEnd(status, elapseTime)
            executeCommonEvent(
                CommonEvent(
                    name = "plan.finish",
                    params =  mapOf(
                        "plan_id" to scheduler.planId,
                        "run_id" to scheduler.runId,
                        "status" to status,
                        "elapse_time" to elapseTime
                    )
                )
            )
        }
    }

    private fun reportPlanStatus(
        message: PlanMessage,
        option: Byte,
        elapseTime: Int = 0
    ) {
        val plan = message.content
        val report = RTPlanStatus()
            .setVersion(message.version)
            .setMsgId(message.id)
            .setQueryId(message.queryId)
            .setUserQuery(message.query)
            .setPlanId(plan.id)
            .setRunId(plan.runId)
            .setPlanType(plan.type)
            .setOption(option)
            .setStatus(
                if (plan.status == ActionCompletion.SUCCESS)
                    RunningStatus.SUCCEEDED
                else
                    RunningStatus.FAILED
            )
            .setExecutionElapseTime(elapseTime)
        message.elapseInfo?.let {
            val uest = it["user_end_speech_timestamp"]
            if (uest != null && uest is Double) {
                report.setUserEndSpeechTimestamp((uest * 1000).toLong())
            }
            val finalAsr = it["final_asr"]
            if (finalAsr != null && finalAsr is Double) {
                report.setFinalAsr((finalAsr * 1000).toInt())
            }
            val planTotal = it["plan_total"]
            if (planTotal != null && planTotal is Double) {
                report.setPlanTotal((planTotal * 1000).toInt())
            }
            val selectFewShot = it["select_few_shot"]
            if (selectFewShot != null && selectFewShot is Double) {
                report.setSelectFewShot((selectFewShot * 1000).toInt())
            }
        }
        report.report()
    }

    /**
     * 执行通用事件
     */
    fun executeCommonEvent(event: CommonEvent) {
        coroutineScope.launch {
            commonEventEmitter.tryEmit(event)
        }
    }

    /**
     * 服务端执行Action结果数据通知
     * 服务端异步返回的本地执行Action所需要的参数结果，如：Action为查天气，此处会接受到天气信息
     */
    fun onResultMessageReceived(message: ResultMessage) {
        val actionResult = message.content
        // 同一个BT执行的所有ResultMessage的runId都是相同的
        val runId = actionResult.runId
        val actionId = actionResult.actionId
        if (actionResult.status) {
            message.content.result?.let {
                redis.put("${runId}_$actionId", it)
            }
        }
    }
}
