package com.ainirobot.agent.action

import org.xmlpull.v1.XmlPullParser
import org.xmlpull.v1.XmlPullParserFactory
import java.io.StringReader
import java.util.LinkedList

class ActionParser(
    private val xml: String,
    private val interceptor: ((node: Node) -> Unit)? = null
) {

    companion object {
        const val TAG_PLAN = "Plan"
        const val TAG_SEQUENCE = "Sequence"
        const val TAG_FALLBACK = "Fallback"
        const val TAG_ACTION = "Action"

        private val requiredAttrs = setOf("ID", "name", "display_name", "execute_side", "execute_timeout_limit")

        private fun createCompositeNode(parser: XmlPullParser): CompositeNode {
            when(parser.name) {
                TAG_PLAN -> {
                    return PlanNode(parser.getAttributeValue(null, "ID"))
                }
                TAG_SEQUENCE -> {
                    return SequenceNode(
                        parser.getAttributeValue(null, "ID"),
                        parser.getAttributeValue(null, "name")
                    )
                }
                TAG_FALLBACK -> {
                    return FallbackNode(
                        parser.getAttributeValue(null, "ID"),
                        parser.getAttributeValue(null, "name")
                    )
                }
                else -> {
                    throw IllegalArgumentException("Unknown composite node type: ${parser.name}")
                }
            }
        }

        private fun createActionNode(parser: XmlPullParser): Node {
            val parameters = mutableMapOf<String, Any>()
            val attrCount = parser.attributeCount
            for (i in 0 until attrCount) {
                val key = parser.getAttributeName(i)
                if (key in requiredAttrs) {
                    continue
                }
                parameters[key] = parseValue(parser.getAttributeValue(i))
            }
            val executeSide = parser.getAttributeValue(null, "execute_side")
            return ActionNode(
                parser.getAttributeValue(null, "ID"),
                parser.getAttributeValue(null, "name"),
                parser.getAttributeValue(null, "display_name"),
                ExecutionSide.valueOf(executeSide.uppercase()),
                parser.getAttributeValue(null, "execute_timeout_limit").toInt(),
                parser.getAttributeValue(null, "_USER_QUERY"),
                parameters
            )
        }

        private fun parseValue(value: String): Any {
            return when {
                value.startsWith("[") && value.endsWith("]") -> {
                    // 处理数组情况
                    val elements = value.drop(1).dropLast(1).split(",").map { it.trim() }
                    elements.map { parseElement(it) }
                }
                else -> {
                    // 处理单个元素情况
                    parseElement(value)
                }
            }
        }

        private fun parseElement(element: String): Any {
            return when {
                element.matches(Regex("-?\\d+")) -> {
                    // 处理整数，选择 Int 或 Long
                    val longValue = element.toLong()
                    if (longValue in Int.MIN_VALUE..Int.MAX_VALUE) {
                        longValue.toInt() // 返回 Int
                    } else {
                        longValue // 返回 Long
                    }
                }
                element.matches(Regex("-?\\d+\\.\\d+")) -> {
                    // 处理浮点数，选择 Float 或 Double
                    val doubleValue = element.toDouble()
                    if (doubleValue in Float.MIN_VALUE..Float.MAX_VALUE) {
                        doubleValue.toFloat() // 返回 Float
                    } else {
                        doubleValue // 返回 Double
                    }
                }
                element.startsWith("\"") && element.endsWith("\"") || element.startsWith("'") && element.endsWith("'") -> {
                    // 处理字符串
                    element.drop(1).dropLast(1)
                }
                else -> {
                    element
                }
            }
        }
    }

    fun parse(): PlanNode {
        val factory = XmlPullParserFactory.newInstance()
        val parser = factory.newPullParser()
        parser.setInput(StringReader(xml))

        val compositeStack = LinkedList<CompositeNode>()
        var eventType = parser.eventType
        while (eventType != XmlPullParser.END_DOCUMENT) {
            when (eventType) {
                XmlPullParser.START_TAG -> {
                    when (parser.name) {
                        TAG_PLAN, TAG_SEQUENCE, TAG_FALLBACK  -> {
                            val composite = createCompositeNode(parser)
                            compositeStack.peek()?.addChild(composite)
                            compositeStack.push(composite)
                            interceptor?.invoke(composite)
                        }
                        TAG_ACTION -> {
                            val actionNode = createActionNode(parser)
                            compositeStack.peek()!!.addChild(actionNode)
                            interceptor?.invoke(actionNode)
                        }
                    }
                }
                XmlPullParser.END_TAG -> {
                    when (parser.name) {
                        TAG_PLAN, TAG_SEQUENCE, TAG_FALLBACK  -> {
                            val compositeNode = compositeStack.pop()
                            if (compositeNode == null || compositeNode.tag != parser.name) {
                                throw IllegalArgumentException("Invalid composite node: ${parser.name}")
                            }
                            if (compositeNode is PlanNode) {
                                if (compositeStack.isNotEmpty()) {
                                    throw IllegalArgumentException("Invalid XML format")
                                }
                                return compositeNode
                            }
                        }
                    }
                }
            }
            eventType = parser.next()
        }
        throw IllegalArgumentException("Invalid XML format")
    }
}