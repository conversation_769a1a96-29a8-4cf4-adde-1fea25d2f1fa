package com.ainirobot.agent.action

import com.ainirobot.agent.action.ActionParser.Companion.TAG_ACTION
import com.ainirobot.agent.core.ActionStatus
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.agent.core.RunningStatus
import com.ainirobot.bi.RTActionStatus
import com.google.gson.annotations.SerializedName
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.withTimeoutOrNull
import java.lang.ref.WeakReference

enum class ExecutionSide {
    @SerializedName("robot")
    ROBOT,
    @SerializedName("server")
    SERVER,
    @SerializedName("both")
    BOTH
}

sealed class Node {

    private lateinit var _scheduler: ActionBus.Scheduler
    val scheduler: ActionBus.Scheduler get() = _scheduler

    /**
     * 规划此action时当前app的appId
     */
    val appId: String
        get() = _scheduler.appId

    /**
     * 规划此action时当前页面的pageId
     */
    val pageId: String
        get() = _scheduler.pageId

    val planId: String
        get() = _scheduler.planId
    val runId: String
        get() = _scheduler.runId
    val executor: MutableSharedFlow<AgentEvent>
        get() = _scheduler.emitter
    val completion: Channel<Pair<String, ActionCompletion>>
        get() = _scheduler.completionChannel

    abstract val id: String

    abstract val name: String

    abstract val tag: String

    abstract suspend fun execute(): ActionCompletion

    open fun setScheduler(scheduler: ActionBus.Scheduler) {
        this._scheduler = scheduler
    }

    /**
     * 上报Plan&Action的执行状态
     */
    fun sendStatus(
        actionStatus: RunningStatus? = null,
        actionResult: Map<String, *>? = null,
        actionException: String = "",
        planStatus: RunningStatus = RunningStatus.RUNNING
    ) {
        val nodes = if (actionStatus != null) {
            listOf(
                ActionStatus(
                    actionId = id,
                    status = actionStatus,
                    result = actionResult,
                    exception = actionException
                )
            )
        } else null
        MessageSender.sendStatus(
            planId,
            runId,
            status = planStatus,
            nodes = nodes
        )
    }
}

class ActionNode(
    override val id: String,
    override val name: String,
    val displayName: String,
    val executionSide: ExecutionSide,
    val timeout: Int = 0,
    val userQuery: String = "",
    val parameters: Map<String, *>
): Node() {

    override val tag: String
        get() = TAG_ACTION

    private val oneway: Boolean
        get() = timeout == 0

    override suspend fun execute(): ActionCompletion {
        sendStatus(actionStatus = RunningStatus.RUNNING)
        RTActionStatus()
            .setPlanId(planId)
            .setRunId(runId)
            .setActionId(id)
            .setActionName(name)
            .setDisplayName(displayName)
            .setOption(1)
            .setStatus(RunningStatus.RUNNING)
            .setElapsedTime(0)
            .setMessage("")
            .report()
        scheduler.monitor.onActionStart(this)
        val t1 = System.currentTimeMillis()
        val completion = internalExecute()
        val elapsedTime = (System.currentTimeMillis() - t1).toInt()
        RTActionStatus()
            .setPlanId(planId)
            .setRunId(runId)
            .setActionId(id)
            .setActionName(name)
            .setDisplayName(displayName)
            .setOption(2)
            .setStatus(completion.toStatus())
            .setElapsedTime(elapsedTime)
            .setMessage(completion.message)
            .report()
        scheduler.monitor.onActionEnd(this, completion)
        sendStatus(
            actionStatus = completion.toStatus(),
            actionResult = completion.result
        )
        scheduler.redis.remove("${runId}_$id")
        return completion
    }

    private suspend fun internalExecute(): ActionCompletion {
        executor.emit(
            ActionEvent(
                schedulerRef = WeakReference(scheduler),
                name = name,
                planId = planId,
                runId = runId,
                appId = appId,
                pageId = pageId,
                displayName = displayName,
                actionId = id,
                timeout = timeout * 1000L,
                side = executionSide,
                oneway = oneway,
                userQuery = userQuery,
                params = parameters
            )
        )

        if (oneway) {
            return ActionCompletion(message = "Oneway action")
        }

        // 比原超时时长+2s，避免比子协程更早结束
        val status = withTimeoutOrNull((timeout + 2) * 1000L) {
            var actionStatus: Pair<String, ActionCompletion>?
            do {
                actionStatus = completion.receive()
            } while (actionStatus!!.first != id)
            actionStatus.second
        }
        return status ?: ActionCompletion(status = ActionCompletion.TIMEOUT)
    }

    override fun toString(): String {
        val builder = StringBuilder()
        builder.append("<${tag} id=$id name=$name displayName=$displayName side=$executionSide timeout=$timeout")
        parameters.forEach {
            builder.append(" ${it.key}=${it.value}")
        }
        builder.append("/>")
        return builder.toString()
    }
}
