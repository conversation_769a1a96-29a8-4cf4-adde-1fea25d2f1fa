package com.ainirobot.agent.service

import android.os.Handler
import android.os.Looper
import com.ainirobot.common.DataStore
import com.ainirobot.common.utils.KLog
import com.ainirobot.coreservice.client.listener.Person
import com.ainirobot.coreservice.client.person.PersonApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import kotlin.coroutines.CoroutineContext

interface FaceEventListener {

    fun onFaceAppear()  // 人脸出现时调用

    fun onFaceDisappear()  // 人脸消失时调用

}

object FaceMonitorService {

    private const val STATUS_INVALID = -1
    private const val STATUS_FACE_DISAPPEAR = 0
    private const val STATUS_FACE_APPEAR = 1

    private val appIdWhiteList = listOf(
        "system_640ce92cb1553953254fc90ae92ea9bd" //面试或人脸采集，避免camera抢占；
    )

    private var job: Job? = null

    // 当前的人脸状态，默认为人脸消失
    @Volatile
    private var faceStatus = STATUS_INVALID
    @Volatile
    private var faceStatus2 = STATUS_INVALID

    val isFaceAppear: Boolean
        get() = faceStatus2 == STATUS_FACE_APPEAR

    private val listeners = mutableListOf<WeakReference<FaceEventListener>>()

    private val handler = Handler(Looper.getMainLooper())

    private val disappearRunnable = Runnable {
        onFaceDisappear()
    }

    fun onStartMonitor(coroutineContext: CoroutineContext) {
        job = CoroutineScope(coroutineContext + Dispatchers.IO + SupervisorJob()).launch {
            while (isActive) {
                // 获取人脸信息的 JSON 字符串
                val person = PersonApi.getInstance().focusPerson
                if (!checkAppWhiteList()) {
                    if (person != null) {
                        if (person.distance < 1.6) {
                            // 通知人脸出现
                            notifyFaceAppear()
                        } else {
                            // 通知人脸消失
                            notifyFaceDisappear(person)
                        }
                    } else {
                        // 没有人脸信息时，通知人脸消失
                        notifyFaceDisappear(null)
                    }
                }
                // 延迟 200 毫秒再进行下一次检测
                delay(300)
            }
        }
    }

    /**
     * 是否存在于白名单，true 表示不需要检测人脸
     */
    private fun checkAppWhiteList(): Boolean {
        val currentAppId = DataStore.currentAppId
        return appIdWhiteList.contains(currentAppId)
    }

    fun onStopMonitor() {
        job?.cancel()
    }

    fun registerListener(listener: FaceEventListener) {
        synchronized(listeners) {
            if (findListener(listener) == null) {
                listeners.add(WeakReference(listener))
                if (faceStatus2 == STATUS_FACE_APPEAR) {
                    listener.onFaceAppear()
                } else if (faceStatus2 == STATUS_FACE_DISAPPEAR) {
                    listener.onFaceDisappear()
                }
            }
        }
    }

    private fun findListener(listener: FaceEventListener): WeakReference<FaceEventListener>? {
        return listeners.find { it.get() == listener }
    }

    fun unregisterListener(listener: FaceEventListener) {
        synchronized(listeners) {
            findListener(listener)?.let {
                listeners.remove(it)
            }
        }
    }

    private fun notifyFaceAppear() {
        if (faceStatus != STATUS_FACE_APPEAR) {
            faceStatus = STATUS_FACE_APPEAR
            KLog.d("notifyFaceAppear", "FaceMonitorService")
            onFaceAppear()
        }
    }

    private fun notifyFaceDisappear(person: Person?) {
        if (faceStatus != STATUS_FACE_DISAPPEAR) {
            faceStatus = STATUS_FACE_DISAPPEAR
            KLog.d("notifyFaceDisappear: person: ${person}, distance: ${person?.distance}", "FaceMonitorService")
            handler.postDelayed(disappearRunnable, 5000)
        }
    }

    private fun onFaceAppear() {
        handler.removeCallbacks(disappearRunnable)
        synchronized(listeners) {
            if (faceStatus2 != STATUS_FACE_APPEAR) {
                faceStatus2 = STATUS_FACE_APPEAR
                KLog.d("onFaceAppear", "FaceMonitorService")
                listeners.forEach { it.get()?.onFaceAppear() }
            }
        }
    }

    private fun onFaceDisappear() {
        synchronized(listeners) {
            if (faceStatus2 != STATUS_FACE_DISAPPEAR) {
                faceStatus2 = STATUS_FACE_DISAPPEAR
                KLog.d("onFaceDisappear", "FaceMonitorService")
                listeners.forEach { it.get()?.onFaceDisappear() }
            }
        }
    }
}