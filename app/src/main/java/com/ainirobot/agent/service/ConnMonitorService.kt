package com.ainirobot.agent.service

import android.app.AlertDialog
import android.graphics.Color
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.WindowManager
import android.net.ConnectivityManager
import android.net.Network
import android.widget.Toast
import com.ainirobot.agent.R
import com.ainirobot.agent.core.ConnectState
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.common.Config
import com.ainirobot.common.Config.ioScope
import com.ainirobot.common.ConnectivityAssist
import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.common.network.WebHookApi
import com.ainirobot.common.utils.KLog
import com.ainirobot.speechasrservice.data.spokesman.SpokermanServer
import kotlinx.coroutines.launch

class ConnMonitorService : ServiceDelegate() {

    companion object {

        private const val MSG_HEARTBEAT_EVENT = 2000

        private const val MSG_UNSUBSCRIBED_WARNING = 2001

        private const val HEARTBEAT_DELAY_TIME = 90000L

    }

    private val handler = object : Handler(Looper.getMainLooper()) {

        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_HEARTBEAT_EVENT -> {
                    if (isRunning) {
                        ioScope.launch {
                            // 心跳消息超时时间为30s
                            val heartbeat = MessageSender.sendHeartbeatSync(30000)
                            if (heartbeat?.server?.isDisconnected != false) {
                                val networkConnected = ConnectivityAssist.isConnected
                                KLog.d("heartbeat disconnected: $heartbeat, isNetworkConnected: $networkConnected", "ConnMonitorService")
                                RobotConsole.print("heartbeat disconnected: Reconnecting ${heartbeat?.timestamp}, isNetworkConnected: $networkConnected", Color.RED)
                                if (networkConnected) {
                                    heartbeatCount++
                                    if (heartbeatCount > 10) {
                                        // 暂兜底，心跳失败超过10次则自动重启服务
                                        android.os.Process.killProcess(android.os.Process.myPid())
                                        return@launch
                                    }
                                    WebHookApi.sendAsync("heartbeat disconnected, heartbeatCount: $heartbeatCount Reconnecting ${heartbeat?.timestamp}")
                                    agent.reconnect()
                                }
                            }
                            // 90s发送一次心跳
                            sendEmptyMessageDelayed(MSG_HEARTBEAT_EVENT, HEARTBEAT_DELAY_TIME)
                        }
                    }
                }
                MSG_UNSUBSCRIBED_WARNING -> {
                    WebHookApi.sendAsync("AgentService livekit unsubscribed > 30s")
                }
            }
        }
    }

    private var currentDialog: AlertDialog? = null
    private var tryConnectCount = 0
    private var heartbeatCount = 0
    @Volatile
    private var isRunning = false

    private val networkCallback = object : ConnectivityManager.NetworkCallback() {

        override fun onAvailable(network: Network) {
            KLog.d("Network is available, start reconnect, current State: ${agent.connectState}", "ConnMonitorService")
            if (agent.connectState != ConnectState.IDLE) {
                agent.enableAutoReconnect = true
                if (agent.connectState == ConnectState.DISCONNECTED
                    || agent.connectState == ConnectState.FAILED) {
                    agent.reconnect()
                }
            }
            SpokermanServer.getInstance().networkConnected()
        }

        override fun onLost(network: Network) {
            KLog.d("Network is lost, disable autoReconnect", "ConnMonitorService")
            // 网络断开时
            agent.enableAutoReconnect = false
        }
    }

    override fun onCreate() {
        handler.sendEmptyMessageDelayed(MSG_HEARTBEAT_EVENT, HEARTBEAT_DELAY_TIME)
        agent.enableAutoReconnect = ConnectivityAssist.isConnected
        ConnectivityAssist.register(networkCallback)
    }

    override fun onConnecting() {
        if (tryConnectCount == 6) {
            WebHookApi.sendAsync("AgentService livekit connection retry times > 5")
        }
        tryConnectCount++
    }

    override fun onConnected(success: Boolean) {
        if (success) {
            tryConnectCount = 0
            heartbeatCount = 0
        } else {
            isRunning = false
            showDialog(context.getString(R.string.dialog_connect_fail_tips))
        }
    }

    override fun onSubscribed(success: Boolean) {
        isRunning = success
        if (success) {
            heartbeatCount = 0
            handler.removeMessages(MSG_UNSUBSCRIBED_WARNING)
            currentDialog?.dismiss()
        } else {
            handler.sendEmptyMessageDelayed(MSG_UNSUBSCRIBED_WARNING, 30000)
        }
    }

    override fun onDisconnected() {
        isRunning = false
        showDialog(context.getString(R.string.dialog_disconnected_tips))
    }

    override fun onDestroy() {
        currentDialog?.dismiss()
        currentDialog = null
        ConnectivityAssist.unregister(networkCallback)
    }

    private fun showDialog(message: String) {
        if (Config.releaseMode) {
            Toast.makeText(context, message, Toast.LENGTH_LONG).show()
            return
        }
        val dialog = currentDialog
        if (dialog != null) {
            dialog.setMessage(message)
            return
        }

        val builder = AlertDialog.Builder(context)
        builder.setTitle(R.string.connection_dialog_title)
        builder.setMessage(message)
        builder.setPositiveButton(R.string.restart_service) { _, _ ->
            android.os.Process.killProcess(android.os.Process.myPid())
        }.setNegativeButton(R.string.cancel) { it, _ ->
            it.dismiss()
        }.setOnDismissListener { currentDialog = null }

        val newDialog = builder.create()

        // 设置 Window 类型为系统级别
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            newDialog.window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY)
        } else {
            newDialog.window?.setType(WindowManager.LayoutParams.TYPE_PHONE)
        }
        newDialog.show()
        currentDialog = newDialog
    }
}
