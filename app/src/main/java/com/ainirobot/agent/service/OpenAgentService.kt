package com.ainirobot.agent.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.ainirobot.agent.bus.APKSupervisor
import com.ainirobot.agent.bus.AgentBus
import com.ainirobot.agent.bus.OPKSupervisor
import com.ainirobot.common.Config.ioScope
import kotlinx.coroutines.launch

class OpenAgentService : Service() {

    companion object {

        private const val NOTIFICATION_ID = 1001

        private const val CHANNEL_ID = "open_agent_service"

        private const val CHANNEL_NAME = "OpenAgentService"

        private fun buildNotification(context: Context): Notification {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channel = NotificationChannel(
                    CHANNEL_ID,
                    CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_MIN
                ).apply {
                    description = "OpenAgentService"
                    setShowBadge(false)
                    lockscreenVisibility = Notification.VISIBILITY_SECRET
                }

                (context.getSystemService(Context.NOTIFICATION_SERVICE) as? NotificationManager)
                    ?.createNotificationChannel(channel)
            }

            return NotificationCompat.Builder(context, CHANNEL_ID)
                .setContentTitle("OpenAgentService")
                .setContentText("OpenAgentService Running")
                .setSmallIcon(android.R.drawable.stat_notify_sync)
                .setPriority(NotificationCompat.PRIORITY_MIN)
                .setVisibility(NotificationCompat.VISIBILITY_SECRET)
                .build()
        }
    }

    override fun onCreate() {
        super.onCreate()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForeground(NOTIFICATION_ID, buildNotification(this))
        }

        // 添加不同平台的包管理器
        AgentBus.addSupervisor(APKSupervisor(this))
        AgentBus.addSupervisor(OPKSupervisor())

        ioScope.launch {
            AgentBus.onCreate()
        }
    }

    override fun onBind(intent: Intent?): IBinder {
        return AgentBus
    }

    override fun onDestroy() {
        AgentBus.onDestroy()
        super.onDestroy()
    }
}