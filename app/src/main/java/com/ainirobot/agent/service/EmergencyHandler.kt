package com.ainirobot.agent.service

import android.graphics.Color
import com.ainirobot.agent.core.CommonMessage
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.common.Config
import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.common.utils.KLog
import com.ainirobot.coreservice.client.StatusListener
import com.ainirobot.coreservice.client.Definition
import com.ainirobot.coreservice.client.SystemApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlin.coroutines.CoroutineContext

/**
 * 处理机器人急停状态的类
 */
class EmergencyHandler {

    // 直接使用全局IO协程作用域，避免每次创建新的作用域
    private val ioScope = Config.ioScope

    /**
     * 急停状态监听器
     */
    private val emergencyStatusListener = object : StatusListener() {
        override fun onStatusUpdate(type: String?, data: String?) {
            if (data.isNullOrEmpty()) {
                return
            }
            
            KLog.d("Emergency status update: type=$type, data=$data", "EmergencyHandler")
            
            // 急停状态：1表示进入急停，0表示退出急停
            val isEmergencyStop = data == "1"
            
            if (isEmergencyStop) {
                // 进入急停状态
                KLog.d("机器人进入急停状态", "EmergencyHandler")
                RobotConsole.print("机器人进入急停状态!", Color.RED)
                
                // 使用全局IO协程作用域执行清除历史记录
                ioScope.launch {
                    try {
                        // 发送清除历史记录命令
                        MessageSender.sendMessage(
                            CommonMessage(
                                name = "cmd",
                                content = mapOf(
                                    "command" to "clear_history"
                                )
                            ),
                            false
                        )
                    } catch (e: Exception) {
                        KLog.e("Failed to send clear_history command: ${e.message}", "EmergencyHandler")
                    }
                }
            } else {
                // 退出急停状态，只打印日志
                KLog.d("机器人退出急停状态", "EmergencyHandler")
                RobotConsole.print("机器人退出急停状态", Color.GREEN)
            }
        }
    }
    
    /**
     * 注册急停状态监听器
     */
    fun register() {
        SystemApi.getInstance().registerStatusListener(Definition.STATUS_EMERGENCY, emergencyStatusListener)
        KLog.d("Registered emergency status listener", "EmergencyHandler")
    }
    
    /**
     * 解注册急停状态监听器
     */
    fun unregister() {
        SystemApi.getInstance().unregisterStatusListener(emergencyStatusListener)
        KLog.d("Unregistered emergency status listener", "EmergencyHandler")
    }
} 