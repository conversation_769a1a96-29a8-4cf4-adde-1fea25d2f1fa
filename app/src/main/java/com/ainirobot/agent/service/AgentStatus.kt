package com.ainirobot.agent.service

import android.content.Intent
import com.ainirobot.agent.IAgent
import com.ainirobot.common.ConnectivityAssist

object AgentStatus : IAgent.Stub() {

    private const val ACTION = "com.ainirobot.agent.action.AGENT"

    /**
     * 最新接收服务器消息时间
     */
    var messageTime: Long = System.currentTimeMillis()

    override fun getStatus(): MutableMap<String, String> {
        return mutableMapOf(
            "messageTime" to messageTime.toString(),
            "network" to ConnectivityAssist.isConnected.toString()
        )
    }

    fun match(intent: Intent?): <PERSON><PERSON><PERSON> {
        return intent?.action == ACTION
    }

}