package com.ainirobot.agent.service

import android.annotation.SuppressLint
import android.app.AlertDialog
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.view.WindowManager
import com.ainirobot.agent.R
import com.ainirobot.agent.action.ExecutionSide
import com.ainirobot.agent.base.Transcription
import com.ainirobot.agent.bus.AgentBus
import com.ainirobot.agent.core.AgentClient
import com.ainirobot.agent.core.CommonMessage
import com.ainirobot.agent.core.ConnectState
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.agent.core.SpeakState
import com.ainirobot.agent.core.SpeechPlayer
import com.ainirobot.agent.core.SubscribeState
import com.ainirobot.bi.RTConnectStatus
import com.ainirobot.bi.RTExceptionalEvent
import com.ainirobot.bridge.executor.ActionEvents
import com.ainirobot.bridge.executor.CommonEventDispatcher
import com.ainirobot.bridge.executor.EventDispatcher
import com.ainirobot.bridge.skill.AgentSkillService
import com.ainirobot.bridge.skill.MediaPageStatus
import com.ainirobot.bridge.skill.SkillServiceImpl
import com.ainirobot.bridge.skill.SpeechClient
import com.ainirobot.common.Config
import com.ainirobot.common.DataStore
import com.ainirobot.common.debug.CaptionWindow
import com.ainirobot.common.debug.Env
import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.common.network.WebHookApi
import com.ainirobot.common.runOnMain
import com.ainirobot.common.utils.DeviceOS
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.RobotConfig
import com.ainirobot.coreservice.client.ApiListener
import com.ainirobot.coreservice.client.RobotOS
import com.ainirobot.coreservice.client.StatusListener
import com.ainirobot.coreservice.client.SystemApi
import com.ainirobot.coreservice.client.hardware.RobotCore
import com.ainirobot.speechasrservice.collection.VoiceDataCollection
import com.ainirobot.speechasrservice.data.spokesman.SpokermanServer
import com.ainirobot.speechasrservice.skill.SkillManager
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import kotlin.coroutines.CoroutineContext


abstract class ServiceDelegate {

    lateinit var agent: VirtualAgent
        private set

    val context: Context get() { return agent.context }

    fun onCreateService(agent: VirtualAgent) {
        this.agent = agent
        onCreate()
    }

    open fun onConnecting() {}

    abstract fun onConnected(success: Boolean)

    abstract fun onSubscribed(success: Boolean)

    abstract fun onDisconnected()

    abstract fun onCreate()

    abstract fun onDestroy()

}

class AgentService: Service(), VirtualAgent {

    private val captionObserver = object : CaptionWindow.CaptionObserver {

        override fun onCaptionChanged(caption: Transcription) {
            if (caption.isUserSpeaking) {
                handler.removeMessages(MSG_USER_BECOME_SILENT)
            }
        }
    }

    private class SystemApiListener(
        private val coroutineContext: CoroutineContext
    ) : ApiListener {

        private val emergencyHandler = EmergencyHandler()

        override fun handleApiDisabled() {}

        override fun handleApiConnected() {
            FaceMonitorService.onStartMonitor(coroutineContext)

            // 注册急停状态监听器
            emergencyHandler.register()
        }

        override fun handleApiDisconnected() {
            FaceMonitorService.onStopMonitor()

            // 解注册急停状态监听器
            emergencyHandler.unregister()
        }
    }

    companion object {

        private const val MSG_USER_BECOME_SILENT = 10001

        private const val DELAY_USER_BECOME_SILENT = 4000L
    }

    private val handler = object : Handler(Looper.getMainLooper()) {

        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_USER_BECOME_SILENT -> {
                    val delayTime = msg.obj as Long // 获取传递的延迟时间
                    sendUserSilent(delayTime)
                }
            }
        }
    }

    private val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        exception.printStackTrace()
        RTExceptionalEvent()
            .setTag("agent_exception")
            .setLevel(2)
            .setType(1)
            .setMessage(exception.message ?: "Unknown error")
            .setException(exception)
            .report()
        exception.runOnMain {
            if (Config.debugMode) {
                showErrorDialog(it.message ?: "Unknown error")
            }
            RobotConsole.print(exception.message ?: "Unknown error", Color.RED)
            WebHookApi.sendAsync("AgentService Crash: ${it.message}")
        }
    }

    private val mainScope = CoroutineScope(Dispatchers.Main + exceptionHandler)
    private val ioScope = CoroutineScope(mainScope.coroutineContext + Dispatchers.IO + SupervisorJob())
    private val agentClient: AgentClient by lazy { AgentClient(this, mainScope.coroutineContext) }
    private val dispatcher: EventDispatcher by lazy { EventDispatcher(this, ioScope) }
    private val commonEventDispatcher: CommonEventDispatcher by lazy { CommonEventDispatcher(this, ioScope) }
    private var speechClient: SpeechClient? = null

    private val serviceDelegates = listOf(
        RobotStatusService(mainScope.coroutineContext),
        ConnMonitorService()
    )

    private var mediaPageStatus: MediaPageStatus = MediaPageStatus.EXIT

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    override fun onCreate() {
        super.onCreate()
        DeviceOS.initialize()
        RobotConsole.initialize(this)
        CaptionWindow.show(applicationContext)
        CaptionWindow.captionObserver = captionObserver

        RobotCore.init(
            this
        ) {
            KLog.d("RobotCore init completed", "AgentService")
            VoiceDataCollection.getInstance().onCoreConnected()
//            // 暂时把静止模式写死开启
//            if (!DeviceOS.staticModeEnable) {
//                DeviceOS.staticModeEnable = true
//            }
        }

        // 提前初始化 SpokermanServer，确保在 HWService 注册前就准备好
        // 这样可以避免 setLanguage 等方法在 SpokermanServer 初始化前被调用
        initializeSpokermanServer()

        SpeechHardwareImpl.onConfigInitialized = {
            if (Env.isInitialized) {
                initialize()
            } else {
                KLog.d("SpeechConfig is not initialized", "AgentService")
            }
        }
        RobotCore.registerHWService(RobotOS.AGENT_SERVICE, SpeechHardwareImpl)
        SystemApi.getInstance().connect(applicationContext, SystemApiListener(mainScope.coroutineContext))

        KLog.d( "onCreate", "AgentService")
        EventBus.getDefault().register(this)
    }

    /**
     * 提前初始化 SpokermanServer，确保在 HWService 可能被调用前就准备好
     * 这样可以避免 setLanguage 等方法在 SpokermanServer 初始化前被调用导致的 NPE
     */
    private fun initializeSpokermanServer() {
        try {
            KLog.d("Early initialization of SpokermanServer", "AgentService")
            SpokermanServer.getInstance().init(this)
        } catch (e: Exception) {
            KLog.e("Failed to initialize SpokermanServer early: ${e.message}", "AgentService")
        }
    }

    private fun initialize() {
        KLog.d( "onCreate initialize, agentEnv: ${Env.agentEnvName}", "AgentService")
        SkillManager.getInstance().init(this)
        agentClient.initialize()
        RobotConsole.show()

        serviceDelegates.forEach { delegate ->
            delegate.onCreateService(this)
        }

        mainScope.launch {
            ioScope.launch {
                agentClient.agentEvents.collect {
                    dispatcher.dispatcher(it)
                }
            }
            ioScope.launch {
                agentClient.commonEvents.collect {
                    commonEventDispatcher.dispatcher(it)
                }
            }
            launch {
                agentClient.connectObserver
                    .filter { it != ConnectState.IDLE }
                    .collect { state ->
                        var color = Color.WHITE
                        when (state) {
                            ConnectState.CONNECTING -> {
                                serviceDelegates.forEach { delegate ->
                                    delegate.onConnecting()
                                }
                            }
                            ConnectState.FAILED -> {
                                serviceDelegates.forEach { delegate ->
                                    delegate.onConnected(false)
                                }
                                color = Color.RED
                                onConnectStatusChanged("failed")
                                RobotConsole.enableReconnectBtn(true)
                            }
                            ConnectState.CONNECTED -> {
                                serviceDelegates.forEach { delegate ->
                                    delegate.onConnected(true)
                                }
                                color = Color.GREEN
                                onConnectStatusChanged("connected")
                                RobotConsole.enableReconnectBtn(true)
                            }
                            ConnectState.DISCONNECTED -> {
                                serviceDelegates.forEach { delegate ->
                                    delegate.onDisconnected()
                                }
                                color = Color.RED
                                onConnectStatusChanged("disconnected")
                                RobotConsole.enableReconnectBtn(true)
                            }
                            else -> {
                                RobotConsole.enableReconnectBtn(false)
                            }
                        }
                        RobotConsole.print(state.name, color)
                        KLog.d("Room Connect State: $state", "AgentService")
                    }
            }
            launch {
                agentClient.subscribeObserver
                    .filter { it != SubscribeState.IDLE }
                    .collect { state ->
                        if (state == SubscribeState.SUBSCRIBED) {
                            serviceDelegates.forEach { delegate ->
                                delegate.onSubscribed(true)
                            }
                            RobotConsole.print(state.name, Color.GREEN)

                            // 连接成功重新上报AgentApp状态信息
                            AgentBus.reportAgentInfos()

                            onConnectStatusChanged("subscribed")
                        } else if (state == SubscribeState.UNSUBSCRIBED) {
                            serviceDelegates.forEach { delegate ->
                                delegate.onSubscribed(false)
                            }
                            RobotConsole.print(state.name, Color.RED)
                            onConnectStatusChanged("unsubscribed")
                        }
                        KLog.d("Room Subscribe State: $state", "AgentService")
                    }
            }
            launch {
                agentClient.transcription
                    .filter { it.text.isNotEmpty() || it.error.isNotEmpty() }
                    .collect { transcription ->
                        CaptionWindow.setCaption(transcription)
                        if (!transcription.isUserSpeaking) {
                            handler.removeMessages(MSG_USER_BECOME_SILENT)
                            if (transcription.final) {
                                KLog.d("sendUserSilent, promoteModeActive: ${RobotConfig.promoteModeActive}, promoteModeEnableAutoChat: ${RobotConfig.promoteModeEnableAutoChat}, promoteModeAutoChatInterval: ${RobotConfig.promoteModeAutoChatInterval}")
                                if (RobotConfig.promoteModeActive) {
                                    if (RobotConfig.promoteModeEnableAutoChat && RobotConfig.promoteModeAutoChatInterval > 0) {
                                        val message = handler.obtainMessage(MSG_USER_BECOME_SILENT).apply {
                                            obj = RobotConfig.promoteModeAutoChatInterval * 1000L // 传递延迟时间
                                        }
                                        handler.sendMessageDelayed(message, RobotConfig.promoteModeAutoChatInterval * 1000L)
                                    }
                                } else {
                                    val message = handler.obtainMessage(MSG_USER_BECOME_SILENT).apply {
                                        obj = DELAY_USER_BECOME_SILENT // 传递延迟时间
                                    }
                                    handler.sendMessageDelayed(message, DELAY_USER_BECOME_SILENT)
                                }
                            }
                        }
                        try {
                            speechClient?.skillCallback?.onSpeechParResult(transcription.text)
                        } catch (e: Exception) {
                            KLog.d("onSpeechParResult exception: ${e.message}", "AgentService")
                        }
                    }
            }
            launch {
                SpeechPlayer.speakState
                    .collect { state ->
                        if (mediaPageStatus != MediaPageStatus.ENTER) {
                            return@collect
                        }
                        when (state) {
                            SpeakState.SPEAK_START -> {
                                RobotConsole.print(">>>>>>AI开始说话")
                                agentClient.executeAction(
                                    actionName = ActionEvents.COMMON_PAUSE,
                                    executionSide = ExecutionSide.ROBOT,
                                    timeout = 0
                                )
                            }
                            SpeakState.SPEAK_END -> {
                                RobotConsole.print(">>>>>>AI停止说话")
                            }
                            SpeakState.IDLE -> { }
                        }
                    }
            }
            agentClient.connect()
        }

        RobotCore.registerStatusListener("update_voice", object : StatusListener() {

            override fun onStatusUpdate(type: String?, data: String?) {
                KLog.d("update_voice: $type, $data", "AgentService")
                ioScope.launch {
                    delay(2000)
                    SpokermanServer.getInstance().getSpokesmenList()
                }
            }
        })
        val intent = Intent(this, OpenAgentService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(intent)
        } else {
            startService(intent)
        }
    }

    private fun onConnectStatusChanged(status: String) {
        RobotConsole.changeState(status)
        RTConnectStatus().setStatus(status).report()
    }

    override fun onBind(intent: Intent?): IBinder {
        KLog.d("onBind", "AgentService")
        RobotConsole.print("AgentService onBind")
        if (AgentStatus.match(intent)) {
            return AgentStatus
        }

        return SkillServiceImpl(
            SpeechClient(packageName, AgentSkillService(agentClient)).also {
                speechClient = it
            }
        )
    }

    override fun onUnbind(intent: Intent?): Boolean {
        RobotConsole.print("AgentService onUnbind")
        KLog.d("onUnbind: ${intent?.action}", "AgentService")
        speechClient = null
        return super.onUnbind(intent)
    }

    override fun onDestroy() {
        KLog.d("onDestroy", "AgentService")
        serviceDelegates.forEach { delegate ->
            delegate.onDestroy()
        }
        agentClient.disconnect()
        EventBus.getDefault().unregister(this)
        DeviceOS.destroy(this)
        super.onDestroy()
    }

    private fun showErrorDialog(message: String) {
        val builder = AlertDialog.Builder(applicationContext)
        builder.setTitle(R.string.error_dialog_title)
        builder.setMessage(message)
        builder.setPositiveButton(R.string.ok) { dialog, _ ->
            dialog.dismiss()
        }
        builder.setNegativeButton(R.string.restart_service) { dialog, _ ->
            dialog.dismiss()
            android.os.Process.killProcess(android.os.Process.myPid())
        }

        val dialog = builder.create()

        // 设置 Window 类型为系统级别
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            dialog.window?.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY)
        } else {
            dialog.window?.setType(WindowManager.LayoutParams.TYPE_PHONE)
        }

        dialog.show()
    }

    override val context: Context
        get() = applicationContext

    override var enableAutoReconnect: Boolean
        get() = agentClient.isAutoReconnect
        set(value) {
            agentClient.isAutoReconnect = value
        }

    override val connectState: ConnectState
        get() = agentClient.connectState

    override fun reconnect() {
        agentClient.reconnect()
    }

    private fun sendUserSilent(interval: Long) {
        KLog.d("sendUserSilent, interval: ${interval}, isFaceAppear: ${FaceMonitorService.isFaceAppear}," +
                " hasRunningAction: ${agentClient.hasRunningAction}, currentPackageName${DataStore.currentPackageName}" +
        " mediaPageStatus:" + mediaPageStatus);
        if (FaceMonitorService.isFaceAppear
            && !agentClient.hasRunningAction
            && DataStore.currentPackageName == Config.MODULE_APP
            && mediaPageStatus != MediaPageStatus.ENTER) {
            RobotConsole.print("User becoming silent")
            MessageSender.sendMessage(
                CommonMessage(
                    name = "user.event",
                    content = mapOf("event" to "用户不说话${interval/1000}s")
                ),
                false
            )
        }
    }

    @Subscribe
    fun onMediaPageStatusChanged(status: MediaPageStatus) {
        mediaPageStatus = status
        KLog.d("onMediaPageStatusChanged: $status", "AgentService")
    }
}