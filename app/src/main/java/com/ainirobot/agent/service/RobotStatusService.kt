package com.ainirobot.agent.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.database.ContentObserver
import android.media.AudioManager
import android.os.BatteryManager
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import com.ainirobot.agent.BuildConfig
import com.ainirobot.agent.core.CommonMessage
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.app.MainApp
import com.ainirobot.common.Config
import com.ainirobot.common.Config.gson
import com.ainirobot.common.DataStore
import com.ainirobot.common.debug.CaptionWindow
import com.ainirobot.common.utils.DeviceOS
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.location.Location
import com.ainirobot.common.utils.location.LocationManager
import com.ainirobot.coreservice.client.RobotSettings
import com.ainirobot.coreservice.client.SettingsUtil
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener
import com.ainirobot.speechasrservice.data.spokesman.SpokermanServer
import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig
import com.google.gson.Gson
import com.google.gson.JsonParser
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import kotlin.coroutines.CoroutineContext
import kotlin.math.abs


object RobotStatus {

    const val KEY_VOLUME = "volume"
    const val KEY_BRIGHTNESS = "brightness"
    const val KEY_CURRENT_SPEED = "current_speed"
    const val KEY_TARGET_SPEED = "target_speed"
    const val KEY_LOCATION = "geo_location"
    const val KEY_BATTERY_LEVEL = "battery_level"
    const val KEY_SPEECH_RATE = "speech_rate"
    const val KEY_SPOKESMAN = "spokesman"
    const val KEY_AGENT_ID = "agent_id"
    const val KEY_TIMEZONE = "timezone"
    const val KEY_LANGUAGE = "language"
    const val KEY_LANGUAGE_CODE = "language_code"
    const val KEY_REGION_VERSION = "region_version"
    const val KEY_PRODUCT_ID = "product_id"
    const val KEY_PRODUCT_MODEL = "product_model"
    const val KEY_ACTION_VERSION = "action_version"
    const val KEY_ENTERPRISE_ID = "enterprise_id"
    const val KEY_DEVICE_ID = "device_id"
    const val KEY_GROUP_ID = "group_id"
    const val KEY_CLIENT_ID = "client_id"
    const val KEY_ROBOT_NAME = "robot_name"
    const val KEY_AGENT_MODE = "agent_mode"
    const val KEY_MULTILINGUAL = "multilingual"
    const val KEY_ROBOT_MAP_ID = "map_id"
    const val KEY_ROBOT_MAP_NAME = "map_name"
    const val KEY_TURN_ON_CLARIFY = "turn_on_clarify"
    const val KEY_TURN_ON_CONFIRM = "turn_on_confirm"

    var onStatusChangedListener: ((String) -> Unit)? = null
        private set

    fun initialize(context: Context, listener: (String) -> Unit) {
        onStatusChangedListener = listener
        SettingsUtil.registerSettingsListener(
            context,
            listOf(
                RobotSettings.SETTINGS_GLOBAL_VOICE_CORP_ID,
                RobotSettings.SETTINGS_GLOBAL_VOICE_GROUP_ID
            ),
            settingsListener
        )
        RobotSettingApi.getInstance().registerRobotSettingListener(
            robotSettingsListener,
            SettingsUtil.ROBOT_SETTING_SPEECH_SPEED_INT,
            SettingsUtil.ROBOT_SETTING_VOICE_SUPPORT_MULTILINGUAL,
            RobotSettings.SETTINGS_GLOBAL_ROBOT_COORDINATE,
            DeviceOS.ROBOT_SETTING_SPEAKER_ROLE
        )
    }

    fun destroy(context: Context) {
        onStatusChangedListener = null
        SettingsUtil.unregisterSettingsListener(context)
        RobotSettingApi.getInstance().unRegisterRobotSettingListener(robotSettingsListener)
    }

    private val robotSettingsListener = object : RobotSettingListener() {

        override fun onRobotSettingChanged(key: String) {
            KLog.d("onRobotSettingChanged key: $key", "RobotStatusService")
            onStatusChangedListener?.invoke(key)
            when (key) {
                SettingsUtil.ROBOT_SETTING_SPEECH_SPEED_INT -> {
                    report(mapOf(KEY_SPEECH_RATE to DeviceOS.speechRate))
                }
                SettingsUtil.ROBOT_SETTING_VOICE_SUPPORT_MULTILINGUAL -> {
                    report(mapOf(KEY_MULTILINGUAL to DeviceOS.multiLingual))
                    val multiLingual = DeviceOS.multiLingual
                    if (multiLingual <= 0) {
                        SpokermanServer.getInstance().notifyMultiLingualClosed(EnvironmentConfig.TtsConfig.getTtsLanguage())
                    }
                }
                DeviceOS.ROBOT_SETTING_SPEAKER_ROLE -> {
                    val spokesman = DeviceOS.spokesman
                    if (spokesman != DataStore.currentSpokesman) {
                        KLog.d("onRobotSettingChanged spokesman: $spokesman", "RobotStatusService")
                        report(mapOf(KEY_SPOKESMAN to spokesman))
                        SpokermanServer.getInstance().notifySpokesmanChanged(spokesman)
                        DataStore.currentSpokesman = spokesman
                    }
                }
            }
        }
    }

    private val settingsListener = SettingsUtil.SettingsListener { _, name ->
        KLog.d("onSettingsChanged: $name", "RobotStatusService")
        when (name) {
            RobotSettings.SETTINGS_GLOBAL_VOICE_CORP_ID -> {
                report(mapOf(KEY_ENTERPRISE_ID to DeviceOS.enterpriseId))
                // 企业变化时重新接到发言人列表
                SpokermanServer.getInstance().getSpokesmenList()
            }
            RobotSettings.SETTINGS_GLOBAL_VOICE_GROUP_ID -> {
                report(mapOf(KEY_GROUP_ID to DeviceOS.groupId))
            }
        }
    }

    fun report(status: Map<String, Any>) {
        KLog.d("RobotStatus.report called with keys: ${status.keys.joinToString()}", "RobotStatusService")
        KLog.d("RobotStatus.report called with values: ${status.values.joinToString()}", "RobotStatusService")
        if (status.containsKey(KEY_LOCATION)) {
            KLog.d("Location data found in status", "RobotStatusService")
        }
        MessageSender.sendMessage(
            CommonMessage(
                name = "state.robot_state",
                content = status + mapOf("updated_at" to System.currentTimeMillis())
            )
        )
        KLog.d("RobotStatus.report message sent", "RobotStatusService")
    }
}


/**
 * 机器人状态监听服务
 */
class RobotStatusService(
    coroutineContext: CoroutineContext
) : ServiceDelegate(), LocationManager.OnLocatedListener {

    companion object {

        private const val VOLUME_CHANGED_ACTION = "android.media.VOLUME_CHANGED_ACTION"

        private fun String.encode(): String {
            return this.replace(" ", "%20")
        }

        /**
         * 计算机器人速度上报时间间隔
         */
        private fun calculateInterval(currentSpeed: Float, previousSpeed: Float): Long {
            val acceleration = abs(currentSpeed - previousSpeed)
            return when {
                acceleration > 0.3f || currentSpeed > 0.8f -> {
                    500L // 高速变化时
                }
                acceleration > 0.2f -> {
                    1500L // 适中变化时
                }
                currentSpeed == 0.0f -> {
                    5000L // 停止时
                }
                else -> {
                    3000L // 速度稳定时
                }
            }
        }
    }

    private val brightnessChangeObserver = object : ContentObserver(Handler(Looper.getMainLooper())) {

        override fun onChange(selfChange: Boolean) {
            super.onChange(selfChange)
            RobotStatus.report(mapOf(RobotStatus.KEY_BRIGHTNESS to getScreenBrightness()))
        }
    }

    private val statusChangeObserver = object : BroadcastReceiver() {

        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                Intent.ACTION_BATTERY_CHANGED -> {
                    val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0)
                    val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, 0)
                    if (scale > 0) {
                        val batteryPct = (level * 100.0f / scale).toInt()
                        if (batteryPct != batteryLevel) {
                            RobotStatus.report(mapOf(RobotStatus.KEY_BATTERY_LEVEL to batteryPct))
                            batteryLevel = batteryPct
                        }
                    }
                }
                VOLUME_CHANGED_ACTION -> {
                    val streamType = intent.getIntExtra("android.media.EXTRA_VOLUME_STREAM_TYPE", -1)
//                    val newVolume = intent.getIntExtra("android.media.EXTRA_VOLUME_STREAM_VALUE", -1)
//                    val oldVolume = intent.getIntExtra("android.media.EXTRA_PREV_VOLUME_STREAM_VALUE", -1)
                    if (streamType == AudioManager.STREAM_MUSIC) {
                        RobotStatus.report(mapOf(RobotStatus.KEY_VOLUME to getVolume()))
                    }
                }
                Intent.ACTION_TIMEZONE_CHANGED -> {
                    RobotStatus.report(mapOf(RobotStatus.KEY_TIMEZONE to DeviceOS.getTimezone()))
                }
                Intent.ACTION_LOCALE_CHANGED -> {
                    val language = DeviceOS.language

                    CaptionWindow.enable = false
                    CaptionWindow.enable = true
                    CaptionWindow.show(context)
                }
            }
        }
    }

    private val robotSettingListener = object : RobotSettingListener() {

        override fun onRobotSettingChanged(key: String) {
            when (key) {
                SettingsUtil.ROBOT_SETTING_SPEECH_SPEED_INT -> {
                    RobotStatus.report(mapOf(RobotStatus.KEY_SPEECH_RATE to DeviceOS.speechRate))
                }
                DeviceOS.ROBOT_SETTING_SPEAKER_ROLE -> {
                    val spokesman = DeviceOS.spokesman
                    if (spokesman != DataStore.currentSpokesman) {
                        KLog.d("onRobotSettingChanged spokesman: $spokesman", "RobotStatusService")
                        RobotStatus.report(mapOf(RobotStatus.KEY_SPOKESMAN to spokesman))
                        SpokermanServer.getInstance().notifySpokesmanChanged(spokesman)
                        DataStore.currentSpokesman = spokesman
                    }
                }
                RobotSettings.SETTINGS_GLOBAL_ROBOT_COORDINATE -> {
                    setCoordinate()
                }
            }
        }

    }

    private fun setCoordinate() {
        val coordinate = SettingsUtil.getString(
            MainApp.getInstance(),
            RobotSettings.SETTINGS_GLOBAL_ROBOT_COORDINATE
        )
        val posInfo = SettingsUtil.getString(
            MainApp.getInstance(),
            RobotSettings.SETTINGS_GLOBAL_ROBOT_POS_INFO
        )

        KLog.d("setCoordinate called with coordinate: $coordinate  posInfo: $posInfo", "RobotStatusService")

        if (!TextUtils.isEmpty(coordinate)) {
            try {
                val split = coordinate.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()

                var longitude = ""
                var latitude = ""
                // 检查数组长度，避免越界
                if (split.size >= 2) {
                    longitude = split[0]
                    latitude = split[1]
                    KLog.d("setCoordinate location lng: ${longitude}, lat: ${latitude}", "RobotStatusService")

                    var posWay = ""
                    if(split.size >= 3) {
                        posWay = split[2]
                        KLog.d("setCoordinate pos_way: $posWay", "RobotStatusService")
                    }

                    if("ip" == posWay || longitude.isEmpty() || latitude.isEmpty()) {
                        KLog.d("Starting Baidu location due to posWay='ip' or empty coordinates", "RobotStatusService")
                        startBaiduLocation()
                        return
                    }

                    // 创建与LocationManager中相同结构的Location对象
                    var locationJson: Any;
                    try {
                        val jsonElement = JsonParser.parseString(posInfo)
                        if (jsonElement.isJsonNull || !jsonElement.isJsonObject) {
                            locationJson = mapOf(
                                "longitude" to longitude.toDouble(),
                                "latitude" to latitude.toDouble(),
                                "location_type" to "settings" // Location type from settings
                            )
                        } else {
                            val gson = Gson()
                            locationJson = gson.fromJson(posInfo, Location::class.java)
                        }
                    } catch (e: Exception) {
                        // 处理解析错误
                        KLog.d("locationJson JSON parse failed: ${e.message}", "RobotStatusService")
                        locationJson = mapOf(
                            "longitude" to longitude.toDouble(),
                            "latitude" to latitude.toDouble(),
                            "location_type" to "settings" // Location type from settings
                        )
                    }

                    // 使用Gson转换为JSON字符串并打印
                    val finalLocation = gson.toJson(locationJson)
                    KLog.d("finalLocation from settings: $finalLocation", "RobotStatusService")

                    // 调用setGeoLocation方法
                    DataStore.geoLocation = finalLocation
                    KLog.d("Calling onLocationSuccess directly for setting-based location", "RobotStatusService")
                    onLocationSuccess(finalLocation)
                } else {
                    KLog.e("Invalid coordinate format: $coordinate (split size: ${split.size})", "RobotStatusService")
                    startBaiduLocation()
                }
            } catch (e: Exception) {
                KLog.e("Error processing coordinate: ${e.message}", "RobotStatusService")
                e.printStackTrace()
                startBaiduLocation()
            }
        } else {
            KLog.d("No coordinate in settings, starting Baidu location", "RobotStatusService")
            startBaiduLocation()
        }
    }

    private fun startBaiduLocation() {
        KLog.d("startBaiduLocation called", "RobotStatusService")
        LocationManager.getInstance().init(context)
        LocationManager.getInstance().startLocation()
        KLog.d("Baidu location started", "RobotStatusService")
    }

    private val scope = CoroutineScope(coroutineContext + Dispatchers.IO)

    /**
     * 当前电池电量
     */
    private var batteryLevel = 0

    override fun onConnected(success: Boolean) { }

    override fun onSubscribed(success: Boolean) {
        if (success) {
            reportAllStatus()
            canReportSpeed = true
        } else {
            canReportSpeed = false
        }
    }

    override fun onDisconnected() {
        canReportSpeed = false
    }

    override fun onCreate() {
        batteryLevel = DataStore.batteryLevel

        registerObservers()
        KLog.d("Setting location listener in onCreate", "RobotStatusService")
        LocationManager.getInstance().setOnLocatedListener(this)
        startReportSpeed()

        RobotStatus.initialize(context) { key ->
            when (key) {
                RobotSettings.SETTINGS_GLOBAL_ROBOT_COORDINATE -> {
                    setCoordinate()
                }
            }
        }

        setCoordinate()
        RobotStatus.report(mapOf(RobotStatus.KEY_SPEECH_RATE to DeviceOS.speechRate))
    }

    override fun onDestroy() {
        unregisterObservers()
        KLog.d("Removing location listener in onDestroy", "RobotStatusService")
        LocationManager.getInstance().setOnLocatedListener(null)
    }

    private fun registerObservers() {
        context.contentResolver.registerContentObserver(
            Settings.System.getUriFor(Settings.System.SCREEN_BRIGHTNESS),
            true,
            brightnessChangeObserver
        )
        context.registerReceiver(
            statusChangeObserver,
            IntentFilter(VOLUME_CHANGED_ACTION)
        )
        context.registerReceiver(
            statusChangeObserver,
            IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        )
        context.registerReceiver(
            statusChangeObserver,
            IntentFilter(Intent.ACTION_TIMEZONE_CHANGED)
        )
        context.registerReceiver(
            statusChangeObserver,
            IntentFilter(Intent.ACTION_LOCALE_CHANGED)
        )
    }

    private fun unregisterObservers() {
        context.contentResolver.unregisterContentObserver(brightnessChangeObserver)
        context.unregisterReceiver(statusChangeObserver)
        RobotStatus.destroy(context)
    }

    private fun reportAllStatus() {
        val language = DeviceOS.language
        val content = mutableMapOf(
            RobotStatus.KEY_ENTERPRISE_ID to DeviceOS.enterpriseId.encode(),
            RobotStatus.KEY_DEVICE_ID to DeviceOS.deviceId.encode(),
            RobotStatus.KEY_GROUP_ID to DeviceOS.groupId.encode(),
            RobotStatus.KEY_CLIENT_ID to DeviceOS.clientId.encode(),
            RobotStatus.KEY_SPEECH_RATE to DeviceOS.speechRate,
            RobotStatus.KEY_SPOKESMAN to DeviceOS.spokesman,
            RobotStatus.KEY_MULTILINGUAL to DeviceOS.multiLingual,
            RobotStatus.KEY_VOLUME to getVolume(),
            RobotStatus.KEY_BRIGHTNESS to getScreenBrightness(),
            RobotStatus.KEY_BATTERY_LEVEL to batteryLevel,
            RobotStatus.KEY_AGENT_ID to DataStore.currentAgentId,
            RobotStatus.KEY_PRODUCT_ID to DataStore.productId,
            RobotStatus.KEY_PRODUCT_MODEL to DeviceOS.productMode,
            RobotStatus.KEY_ACTION_VERSION to Config.actionVersion,
            "interface_state" to mapOf(
                "package_name" to DataStore.currentPackageName,
                "app_id" to DataStore.currentAppId,
                "page_id" to DataStore.currentPageId
            ),
            RobotStatus.KEY_TIMEZONE to DeviceOS.getTimezone(),
            RobotStatus.KEY_LANGUAGE to language.codeName,
            RobotStatus.KEY_LANGUAGE_CODE to language.codeValue,
            RobotStatus.KEY_REGION_VERSION to BuildConfig.REGION_VERSION,
            RobotStatus.KEY_AGENT_MODE to DataStore.agentThoughtMode,
            RobotStatus.KEY_TURN_ON_CLARIFY to DataStore.turnOnClarify,
            RobotStatus.KEY_TURN_ON_CONFIRM to DataStore.turnOnConfirm
        )
        if (DataStore.mapInfo.isNotEmpty()) {
            val jsonObject = JSONObject(DataStore.mapInfo)
            content[RobotStatus.KEY_ROBOT_MAP_ID] = jsonObject.optString("map_id", "")
            content[RobotStatus.KEY_ROBOT_MAP_NAME] = jsonObject.optString("map_name", "")
        }
        if (DataStore.geoLocation.isNotEmpty()) {
            content[RobotStatus.KEY_LOCATION] = DataStore.geoLocation
        }
        if (DataStore.agentProfile.isNotEmpty()) {
            content["character_state"] = gson.fromJson<Any?>(
                DataStore.agentProfile,
                object : TypeToken<Map<String, String>>() {}.type
            ).also {
                KLog.d("character_state-profile: $it", "RobotStatusService")
            }
        }
        RobotStatus.report(content)
    }

    private fun getVolume(): Int {
        val volume = DeviceOS.getVolumeLevel(context)
        val maxVolume = DeviceOS.maxVolumeLevel
        return (volume * 100.0f / maxVolume).toInt()
    }

    private fun getScreenBrightness(): Int {
        val brightness = DeviceOS.getScreenBrightness(context)
        if (brightness < 0) {
            return -1
        }
        return (brightness * 100.0f / DeviceOS.MAX_SCREEN_BRIGHTNESS).toInt()
    }

    override fun onLocationSuccess(location: String?) {
        KLog.d("onLocationSuccess called with: ${location?.substring(0, Math.min(100, location?.length ?: 0))}", "RobotStatusService")
        if (!location.isNullOrEmpty()) {
            KLog.d("DataStore.geoLocation: ${DataStore.geoLocation.substring(0, Math.min(100, DataStore.geoLocation.length))}", "RobotStatusService")
            KLog.d("Reporting location via RobotStatus.report", "RobotStatusService")
            RobotStatus.report(mapOf(RobotStatus.KEY_LOCATION to DataStore.geoLocation))
            KLog.d("Location reported", "RobotStatusService")
        } else {
            KLog.e("Location is null or empty", "RobotStatusService")
        }
    }

    @Volatile
    private var previousSpeed = 0.0f
    @Volatile
    private var canReportSpeed = false
    private fun startReportSpeed() {
        scope.launch {
            while (true) {
                if (!canReportSpeed) {
                    delay(3000)
                    continue
                }
                val speed = DeviceOS.getSpeed()
                if (speed == null
                    || speed.first == previousSpeed
                    || speed.first < 0.1) {
                    delay(5000)
                    continue
                }
                RobotStatus.report(
                    mapOf(
                        RobotStatus.KEY_CURRENT_SPEED to speed.first,
                        RobotStatus.KEY_TARGET_SPEED to speed.second
                    )
                )
                delay(calculateInterval(speed.first, previousSpeed))
            }
        }
    }
}