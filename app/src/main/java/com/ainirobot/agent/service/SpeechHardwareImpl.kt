package com.ainirobot.agent.service

import com.ainirobot.agent.BuildConfig
import com.ainirobot.agent.core.CommonMessage
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.common.Config
import com.ainirobot.common.Config.gson
import com.ainirobot.common.DataStore
import com.ainirobot.common.debug.CaptionWindow
import com.ainirobot.common.debug.Env
import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.common.utils.KLog
import com.ainirobot.coreservice.IInspectCallBack
import com.ainirobot.coreservice.bean.Command
import com.ainirobot.coreservice.client.exception.BinderExceptionHandler
import com.ainirobot.coreservice.client.hardware.HWService
import com.ainirobot.coreservice.client.speech.entity.LangParamsEnum
import com.ainirobot.coreservice.config.ServiceConfig
import com.ainirobot.features.web.WebFeature
import com.ainirobot.speechasrservice.data.spokesman.SpokermanServer
import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig
import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig.TtsConfig
import com.ainirobot.speechasrservice.utils.PublicConfig
import com.ainirobot.speechasrservice.utils.SpeechConfig
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

object SpeechHardwareImpl : HWService() {

    private const val TAG = "SpeechHardwareImpl"

    private val voiceMonitorWhiteList = arrayOf(
        Config.MODULE_APP,
        "com.ainirobot.maptool"
    )

    var onConfigInitialized: (() -> Unit)? = null
    
    override fun getBoardName(): String {
        KLog.d("getBoardName", TAG)
        return ""
    }

    override fun getVersion(subsystem: String?): String {
        KLog.d("getVersion subsystem: $subsystem", TAG)
        return ""
    }

    override fun isNeedUpgrade(subsystem: String?, version: String?): Boolean {
        KLog.d("isNeedUpgrade subsystem: $subsystem, version: $version", TAG)
        return false
    }

    override fun onMount(serviceName: String?, config: ServiceConfig?): MutableList<Command> {
        KLog.d("onMount serviceName: $serviceName, config: $config", TAG)
        if (config != null && config.isEnable) {
            val gson = Gson()
            val publicConfig = gson.fromJson(config.publicConfig, PublicConfig::class.java)
            val speechConfig = gson.fromJson(config.config, SpeechConfig::class.java)
            if (BuildConfig.REGION_OVERSEA) {
                speechConfig.processReleaseServerZoneData()
            }
            if (publicConfig != null && !publicConfig.clientId.isNullOrEmpty()) {
                speechConfig.clientID = publicConfig.clientId
            }
            Env.initialize(speechConfig)
            EnvironmentConfig.init(speechConfig)
            onConfigInitialized?.invoke()
        }
        return mutableListOf()
    }

    override fun onInspectStart(callBack: IInspectCallBack?) {
        KLog.d("onInspectStart", TAG)
    }

    override fun onUpgrade(subsystem: String?, params: String?): Boolean {
        KLog.d("onUpgrade subsystem: $subsystem, params: $params", TAG)
        return false
    }

    override fun onReset() {
        KLog.d("onReset", TAG)
    }

    override fun onAsyncCommand(type: String?, params: String?, language: String?) {
        KLog.d("onAsyncCommand type: $type, params: $params, language: $language", TAG)
    }

    override fun onSyncCommand(type: String?, params: String?, language: String?): String {
        KLog.d("onSyncCommand type: $type, params: $params, language: $language", TAG)
        return ""
    }

    private var lastPckName = ""
    override fun switchAppControl(packageName: String?, lastPackageName: String?) {
        WebFeature.closeWeb( false)
        RobotConsole.print("onApkChanged packageName: $packageName, lastPackageName: $lastPackageName")
        KLog.d("onApkChanged packageName: $packageName, lastPackageName: $lastPackageName", TAG)
        if (packageName in voiceMonitorWhiteList) {
            CaptionWindow.enable = true
            DataStore.microphoneMuted = false
            reportSystemAgentInfo()
        } else {
            CaptionWindow.enable = false
            DataStore.microphoneMuted = true
        }
        DataStore.currentPackageName = packageName ?: ""
        DataStore.currentAppId = ""
        DataStore.currentPageId = ""
        this.lastPckName = packageName ?: ""
        if (!packageName.isNullOrEmpty()) {
            MessageSender.sendMessage(
                CommonMessage(
                    name = "state.robot_state",
                    content = mapOf(
                        "interface_state" to mapOf(
                            "package_name" to packageName,
                            "app_id" to "",
                            "page_id" to ""
                        )
                    )
                )
            )
        }
    }

    /**
     * 进入小豹app后再次上报基本信息，以覆盖二开APP中上报的信息
     * 临时方案，待OPK适配完成后此方法可删除
     */
    private fun reportSystemAgentInfo() {
        // 上报人设信息
        if (DataStore.agentProfile.isNotEmpty()) {
            MessageSender.sendMessage(
                msgType = "state.robot_state",
                content = mapOf(
                    "character_state" to gson.fromJson(
                        DataStore.agentProfile,
                        object : TypeToken<Map<String, String>>() {}.type
                    )
                ),
                isSafety = true
            )
        }
    }

    override fun setLanguage(language: String?) {
        val identity = clearCallingIdentity()
        try {
            val langParams = LangParamsEnum.getLangEnumByCodeName(language)
            TtsConfig.settTtsLanguage(langParams)
            if (!langParams.codeName.isNullOrEmpty()) {
                KLog.d("setLanguage language: $language", TAG)
                SpokermanServer.getInstance().notifyTtsLanguageChanged(langParams.codeName)
                RobotStatus.report(
                    mapOf(
                        RobotStatus.KEY_LANGUAGE to langParams.codeName,
                        RobotStatus.KEY_LANGUAGE_CODE to langParams.codeValue
                    )
                )
            }
        } catch (e: Exception) {
            BinderExceptionHandler.handle(e)
        } finally {
            restoreCallingIdentity(identity)
        }
    }
}