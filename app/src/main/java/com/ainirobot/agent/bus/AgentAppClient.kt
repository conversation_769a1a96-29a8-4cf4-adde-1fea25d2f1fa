package com.ainirobot.agent.bus

import android.os.Build
import android.os.Bundle
import android.os.RemoteException
import com.ainirobot.agent.action.ActionCompletion
import com.ainirobot.agent.action.ActionEvent
import com.ainirobot.agent.base.ActionParams
import com.ainirobot.agent.base.ActionResult
import com.ainirobot.agent.base.AppInfo
import com.ainirobot.agent.base.IAgentApp
import com.ainirobot.agent.base.Message
import com.ainirobot.agent.base.PageInfo
import com.ainirobot.agent.base.Transcription
import com.ainirobot.agent.base.llm.LLMConfig
import com.ainirobot.agent.base.llm.LLMMessage
import com.ainirobot.agent.base.utils.DigitalUtil
import com.ainirobot.agent.base.utils.toBundle
import com.ainirobot.agent.core.AppInfoMessage
import com.ainirobot.agent.core.AudioInfo
import com.ainirobot.agent.core.CommonMessage
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.agent.core.PageInfoMessage
import com.ainirobot.agent.core.SpeechPlayer
import com.ainirobot.bridge.executor.ActionEvents
import com.ainirobot.common.Config.gson
import com.ainirobot.common.Config.ioScope
import com.ainirobot.common.DataStore
import com.ainirobot.common.debug.CaptionWindow
import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.common.utils.KLog
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull

class AgentAppClient(
    private val impl: IAgentApp,
) {

    private val actionCallbacks = mutableMapOf<String, (ActionResult?) -> Unit>()

    lateinit var appInfo: AppInfo
        private set
    private var pageInfo: PageInfo? = null

    var isMicrophoneMuted = false
        private set

    var isEnableWakeFree = true
        private set

    var isEnableVoiceBar = true
        private set

    var isForeground = false
        private set

    init {
        obtainAppInfo()
    }

    private fun obtainAppInfo() {
        this.appInfo = this.impl.info
        this.isMicrophoneMuted = this.appInfo.isMicrophoneMuted
        try {
            this.isEnableWakeFree = this.appInfo.isEnableWakeFree
        } catch (e: Exception) {
            e.printStackTrace()
        }
        try {
            this.isEnableVoiceBar = this.appInfo.isEnableVoiceBar
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun reportAppInfo() {
        MessageSender.sendMessage(
            AppInfoMessage(content = appInfo),
            isSafety = false
        )
    }

    fun reportPageInfo() {
        pageInfo?.let {
            MessageSender.sendMessage(
                PageInfoMessage(content = it),
                isSafety = false
            )
        }
    }

    fun clearAppInfo() {
        MessageSender.sendMessage(
            AppInfoMessage(
                content = AppInfo(
                    version = "",
                    appId = "",
                    platform = "",
                    persona = "",
                    style = "",
                    objective = "",
                    actions = emptyList(),
                    exportedActions = emptyList(),
                    isMicrophoneMuted = true,
                    isEnableWakeFree = true,
                    isDisablePlan = false,
                    isEnableVoiceBar = false
                )
            ),
            isSafety = false
        )
    }

    fun clearPageInfo() {
        MessageSender.sendMessage(
            PageInfoMessage(
                content = PageInfo(
                    appId = "",
                    pageId = "",
                    persona = "",
                    style = "",
                    objective = "",
                    actions = emptyList(),
                    blockActions = emptyList(),
                    blockLevel = "specific"
                )
            ),
            isSafety = false
        )
    }

    fun onForeground() {
        this.isForeground = true
        obtainAppInfo()
    }

    fun onBackground() {
        this.isForeground = false
    }

    fun onDestroy() {

    }

    private fun isCurrentPage(pageId: String): Boolean {
        val currentPageId = this.pageInfo?.pageId
        KLog.d("isCurrentPage, pageId: $pageId, currentPageId: $currentPageId", "AgentAppClient")
        return if (currentPageId == null) true else currentPageId == pageId
    }

    fun onPageBegan(info: PageInfo) {
        this.pageInfo = info
        reportPageInfo()
    }

    fun onPageEnd(pageId: String) {
        if (isCurrentPage(pageId)) {
            clearPageInfo()
        }
    }

    /**
     * 执行动作，返回空表示目标app未处理，或需要后续处理
     */
    suspend fun executeActionEvent(event: ActionEvent): ActionCompletion? {
        val params = ActionParams(
            sid = DigitalUtil.uuid(),
            appId = event.appId,
            pageId = event.pageId,
            actionName = event.name,
            content = event.params.toBundle().apply { putString("user_query", event.userQuery) },
        )
        KLog.d(
            "executeActionEvent, action: ${params.actionName}, timeoutMillis: ${event.timeout}",
            "AgentAppClient"
        )
        val timeoutMillis = if (event.timeout < 10000) 10000 else event.timeout - 50
        val completion = withTimeoutOrNull(timeoutMillis) {
            suspendCancellableCoroutine { continuation ->
                callAction(params) { result ->
                    continuation.resume(
                        if (result != null) ActionCompletion(result.status.toString()) else null
                    ) { e ->
                        e.printStackTrace()
                    }
                }
            }
        }
        if (completion == null) {
            synchronized(actionCallbacks) {
                actionCallbacks.remove(params.sid)
            }
        }
        return completion
    }

    private fun callAction(params: ActionParams, callback: (ActionResult?) -> Unit) {
        KLog.d("callAction, name: ${params.actionName}", "AgentAppClient")
        try {
            val result = impl.callAction(params)
            KLog.d("callAction, name: ${params.actionName}, result: $result", "AgentAppClient")
            if (result) {
                synchronized(actionCallbacks) {
                    actionCallbacks[params.sid] = callback
                }
            } else {
                callback(null)
            }
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    fun respondAction(result: ActionResult) {
        KLog.d("respondAction: $result", "AgentAppClient")
        val callback = synchronized(actionCallbacks) {
            actionCallbacks.remove(result.sid)
        } ?: return
        callback.invoke(result)
    }

    fun onAgentStatusChanged(status: String, message: String): Boolean {
        KLog.d(
            "onAgentStatusChanged, status: $status, isForeground: $isForeground",
            "AgentAppClient"
        )
        return if (isForeground) {
            this.executeMethod(
                "onAgentStatusChanged",
                Bundle().apply {
                    putString("status", status)
                    putString("message", message)
                }
            )?.toBoolean() ?: false
        } else false
    }

    fun onActionStatusChanged(actionName: String, status: String, message: String, result: Map<String, *>? = null) {
        KLog.d(
            "onActionStatusChanged, actionName: $actionName, status: $status, message: $message, result: $result",
            "AgentAppClient"
        )
        this.executeMethod(
            "onActionStatusChanged",
            Bundle().apply {
                putString("actionName", actionName)
                putString("status", status)
                putString("message", message)
                result?.let { 
                    putString("result", gson.toJson(it))
                }
            }
        )
    }

    fun onTranscribed(transcription: Transcription): Boolean {
        KLog.d(
            "onTranscribed, transcription: $transcription, isForeground: $isForeground",
            "AgentAppClient"
        )
        return if (!isForeground) false else try {
            impl.onTranscribed(transcription)
        } catch (e: RemoteException) {
            KLog.e("onTranscribed failed", "AgentAppClient")
            e.printStackTrace()
            false
        }
    }

    /**
     * 同步扩展方法，可通过此通道执行客户端的方法，等待结果
     *
     * @param method 方法名，只是作为一个任务标识，并不能调用真正的方法
     * @param data 参数
     */
    private fun executeMethod(method: String, data: Bundle): String? {
        data.classLoader = AgentAppClient::class.java.classLoader
        return try {
            impl.executeMethod(method, data)?.ifEmpty { null }
        } catch (e: Exception) {
            KLog.e("executeMethod failed", "AgentAppClient")
            e.printStackTrace()
            null
        }
    }

    /**
     * 异步扩展方法，可通过此通道给客户端发消息，不等待结果
     */
    private fun send(message: Message) {
        try {
            impl.send(message)
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    fun dispatch(message: Message) {
        message.content.classLoader = AgentAppClient::class.java.classLoader
        KLog.d(
            "Agent_app send message, appId: ${message.appId}, type: ${message.type}, content: ${message.content.getString("command")}",
            "AgentAppClient"
        )
        when (message.type) {
            "system_command" -> dispatchSystemCommand(message)
        }
    }

    private fun dispatchSystemCommand(message: Message) {
        val command = message.content.getString("command") ?: return
        when (command) {
            "query_by_text" -> {
                val text = message.content.getString("text")
                KLog.d("QueryByText: $text", "AgentAppClient")
                if (!text.isNullOrEmpty()) {
                    CaptionWindow.clearAiCaption()
                    CaptionWindow.clearUserCaption()
                    MessageSender.sendMessage(
                        msgType = "plan.plan",
                        content = mapOf("query" to text),
                        isSafety = false
                    )
                }
            }

            "follow_up" -> {
                val prompt = message.content.getString("prompt", "")
                KLog.d("FollowUp: $prompt", "AgentAppClient")
                RobotConsole.print("Agent App execute follow_up")
                MessageSender.sendMessage(
                    CommonMessage(
                        name = "cmd",
                        content = mapOf(
                            "command" to "generate_follow-up",
                            "prompt" to prompt
                        )
                    ),
                    false
                )
            }

            "play_tts" -> {
                val text = message.content.getString("text")
                KLog.d("PlayTTS: $text", "AgentAppClient")
                if (!text.isNullOrEmpty()) {
                    ioScope.launch {
                        val result = SpeechPlayer.playSync(
                            AudioInfo(
                                isStream = false,
                                text = text,
                                timeoutMillis = message.content.getLong("timeout_millis")
                            )
                        )
                        // 1: success, 2: failed，注：不能使用0
                        val status = if (result != null) {
                            if (result.status) 1 else 2
                        } else 2
                        <EMAIL>(
                            Message(
                                id = message.id,
                                type = "command_callback",
                                content = Bundle().also {
                                    it.putString("command", command)
                                    it.putInt("status", status)
                                },
                                appId = appInfo.appId
                            )
                        )
                    }
                }
            }

            "stop_tts" -> {
                SpeechPlayer.stop()
            }

            "call_llm_stream", "call_llm" -> {
                val messages = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    message.content.getParcelableArrayList("messages", LLMMessage::class.java)
                } else {
                    message.content.getParcelableArrayList("messages")
                }
                val llmConfig = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    message.content.getParcelable("llm_config", LLMConfig::class.java)
                } else {
                    message.content.getParcelable("llm_config")
                }
                val timeoutMillis = message.content.getLong("timeout_millis")
                KLog.d(
                    "$command, messages: $messages, llmConfig: $llmConfig, timeoutMillis: $timeoutMillis",
                    "AgentAppClient"
                )
                if (messages != null && llmConfig != null) {
                    ioScope.launch {
                        val result = if (command == "call_llm_stream") {
                            SpeechPlayer.playSync(
                                AudioInfo(
                                    isStream = true,
                                    streamConfig = mapOf(
                                        "messages" to messages,
                                        "llm_config" to llmConfig
                                    ),
                                    timeoutMillis = timeoutMillis
                                )
                            )

                        } else {
                            MessageSender.executeActionSync(
                                action = ActionEvents.Server.CALL_LLM,
                                params = mapOf(
                                    "messages" to messages,
                                    "llm_config" to llmConfig
                                ),
                                planId = "",
                                actionId = "",
                                runId = DigitalUtil.uuid(),
                                timeoutMillis = timeoutMillis
                            )
                        }
                        // 1: success, 2: failed，注：不能使用0
                        val status = if (result != null) {
                            if (result.status) 1 else 2
                        } else 2
                        KLog.d("$command status: $status", "AgentAppClient")
                        <EMAIL>(
                            Message(
                                id = message.id,
                                type = "command_callback",
                                content = Bundle().also {
                                    it.putString("command", command)
                                    it.putInt("status", status)
                                    result?.result?.let { r ->
                                        it.putString(
                                            "result",
                                            gson.toJson(
                                                r,
                                                object : TypeToken<Map<String, Any>>() {}.type
                                            )
                                        )
                                    }
                                },
                                appId = appInfo.appId
                            )
                        )
                    }
                }
            }

            "microphone_muted" -> {
                this.isMicrophoneMuted = message.content.getBoolean("muted", false)
                KLog.d("setMicrophoneMuted: ${this.isMicrophoneMuted}", "AgentAppClient")
                DataStore.microphoneMuted = this.isMicrophoneMuted
            }

            "enable_wake_free" -> {
                this.isEnableWakeFree = message.content.getBoolean("value", true)
                KLog.d("isEnableWakeFree: ${this.isEnableWakeFree}", "AgentAppClient")
                DataStore.isEnableWakeFree = this.isEnableWakeFree
            }

            "enable_voice_bar" -> {
                this.isEnableVoiceBar = message.content.getBoolean("value", true)
                KLog.d("isEnableVoiceBar: ${this.isEnableVoiceBar}", "AgentAppClient")
                CaptionWindow.enable = this.isEnableVoiceBar
            }

            "disable_plan" -> {
                val isDisablePlan = message.content.getBoolean("value", false)
                KLog.d("isDisablePlan: $isDisablePlan", "AgentAppClient")
                MessageSender.sendMessage(
                    CommonMessage(
                        name = "state.robot_state",
                        content = mapOf(
                            "disable_plan" to isDisablePlan
                        )
                    ),
                    isSafety = false
                )
            }

            "upload_interface_info" -> {
                val interfaceInfo = message.content.getString("interface_info")
                if (interfaceInfo != null) {
                    MessageSender.sendMessage(
                        CommonMessage(
                            name = "state.robot_state",
                            content = mapOf(
                                "interface_state" to mapOf(
                                    "interface_info" to interfaceInfo
                                )
                            )
                        ),
                        isSafety = false
                    )
                }
            }

            "clear_context" -> {
                MessageSender.sendMessage(
                    CommonMessage(
                        name = "cmd",
                        content = mapOf("command" to "clear_history")
                    ),
                    false
                )
            }
        }
    }
}