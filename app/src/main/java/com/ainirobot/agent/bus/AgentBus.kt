package com.ainirobot.agent.bus

import android.os.Bundle
import android.os.RemoteException
import com.ainirobot.agent.action.ActionCompletion
import com.ainirobot.agent.action.ActionEvent
import com.ainirobot.agent.base.ActionEntity
import com.ainirobot.agent.base.ActionRegistry
import com.ainirobot.agent.base.ActionResult
import com.ainirobot.agent.base.IAgentApp
import com.ainirobot.agent.base.IAgentService
import com.ainirobot.agent.base.Message
import com.ainirobot.agent.base.PageInfo
import com.ainirobot.agent.base.Transcription
import com.ainirobot.common.DataStore
import com.ainirobot.common.debug.CaptionWindow
import com.ainirobot.common.utils.KLog

object AgentBus : IAgentService.Stub() {

    private val apps = mutableMapOf<String, AgentAppClient>()

    private val packageSupervisors = mutableListOf<PackageSupervisor>()

    private var currentApp: AgentAppClient? = null

    fun addSupervisor(supervisor: PackageSupervisor) {
        packageSupervisors.add(supervisor)
    }

    fun onCreate() {
        packageSupervisors.forEach { it.onCreate() }
    }

    fun onDestroy() {
        packageSupervisors.forEach { it.onDestroy() }
    }

    private fun getAction(actionName: String): ActionEntity? {
        for (supervisor in packageSupervisors) {
            val action = supervisor.getAction(actionName)
            if (action != null) {
                KLog.d("findAction, actionName: $actionName, action: $action", "AgentBus")
                return action
            }
        }
        KLog.d("findAction failed, actionName: $actionName", "AgentBus")
        return null
    }

    /**
     * 通过actionName获取app的注册表，前提是不同app的namespace必须保持唯一
     */
    private fun getActionRegistry(actionName: String): ActionRegistry? {
        for (supervisor in packageSupervisors) {
            val actionRegistry = supervisor.getActionRegistry(actionName)
            if (actionRegistry != null) {
                return actionRegistry
            }
        }
        return null
    }

    /**
     * 检测action的注册信息是否完整，如果不完整则进行补充
     * 注：仅限于在注册表中注册的action
     */
    private fun checkActions(appId: String, actions: List<ActionEntity>) {
        actions.forEach { action ->
            KLog.d("checkAction, appId: $appId, action: $action", "AgentBus")
            val parametersIsNull = action.parameters.isNullOrEmpty()
            val descIsNull = action.desc.isEmpty()
            if (parametersIsNull || descIsNull) {
                getAction(action.name)?.let {
                    if (parametersIsNull) {
                        action.parameters = it.parameters
                    }
                    if (descIsNull) {
                        action.desc = it.desc
                    }
                    action.displayName = it.displayName
                    action.executeTimeout = it.executeTimeout
                    action.extra = it.extra
                }
            }
        }
    }

    fun reportAgentInfos() {
        currentApp?.let {
            if (it.isForeground) {
                it.reportAppInfo()
                it.reportPageInfo()
            }
        }
    }

    /*****************************以下是Agent客户端调用的接口***********************/

    private fun isCurrentApp(appId: String): Boolean {
        val currentAppId = currentApp?.appInfo?.appId
        KLog.d("isCurrentApp, appId: $appId, currentAppId: $currentAppId", "AgentBus")
        return if (currentAppId == null) true else currentAppId == appId
    }

    override fun onAppCreate(app: IAgentApp) {
        KLog.d("onAppCreate", "AgentBus")
        // 此处注册在注册表内配置的action
        val client = AgentAppClient(app)
        val appId = client.appInfo.appId
        synchronized(apps) {
            apps[appId] = client
        }
        try {
            app.asBinder().linkToDeath(
                { // 如果远程进程死亡，则删除所有注册，并删除app
                    KLog.d("Agent Client linkToDeath, appId: $appId", "AgentBus")
                    if (isCurrentApp(appId)) {
                        onAppBackground(appId)
                        onAppDestroy(appId)
                    }
                },
                0
            )
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    override fun onAppDestroy(appId: String) {
        KLog.d("onAppDestroy", "AgentBus")
        synchronized(apps) {
            apps.remove(appId)?.onDestroy()
        }
    }

    override fun onAppForeground(appId: String) {
        KLog.d("onAppForeground, appId: $appId", "AgentBus")
        val app = getApp(appId) ?: return
        KLog.d("onAppForeground, appId: $appId, isMicrophoneMuted: ${app.isMicrophoneMuted}, isEnableWakeFree: ${app.isEnableWakeFree}, isEnableVoiceBar: ${app.isEnableVoiceBar} ", "AgentBus")
        currentApp = app
        app.onForeground()
        DataStore.microphoneMuted = app.isMicrophoneMuted
        DataStore.isEnableWakeFree = app.isEnableWakeFree
        CaptionWindow.enable = app.isEnableVoiceBar
        val info = app.appInfo
        checkActions(info.appId, info.actions)
        getApp(appId)?.reportAppInfo()
    }

    override fun onAppBackground(appId: String) {
        val app = getApp(appId)?.apply { onBackground() }
        val isCurrentApp = isCurrentApp(appId)
        KLog.d("onAppBackground, appId: $appId, isCurrentApp: $isCurrentApp", "AgentBus")
        if (isCurrentApp) {
            app?.clearAppInfo()
            // 正常情况下会多报一次页面清除，但避免异常退出或客户端未处理PageAgent生命周期时兜底
            app?.clearPageInfo()
        }
    }

    private fun getApp(appId: String): AgentAppClient? {
        synchronized(apps) {
            return apps[appId]
        }
    }

    override fun onPageBegan(info: PageInfo) {
        KLog.d("onPageBegan, info: $info", "AgentBus")
        checkActions(info.appId, info.actions)
        getApp(info.appId)?.onPageBegan(info)
    }

    override fun onPageEnd(appId: String, pageId: String) {
        KLog.d("onPageEnd, appId: $appId, pageId: $pageId", "AgentBus")
        if (isCurrentApp(appId)) {
            getApp(appId)?.onPageEnd(pageId)
        }
    }

    override fun respondAction(result: ActionResult) {
        getApp(result.appId)?.respondAction(result)
    }

    /**
     * 异步执行的扩展方法，支持AgentClient调用
     */
    override fun send(message: Message) {
        getApp(message.appId)?.dispatch(message)
    }

    /**
     * 同步执行扩展方法，支持AgentClient调用
     */
    override fun executeMethod(method: String, data: Bundle): String? {
        return null
    }

    /*****************************以下是AgentService调用客户端的方法***********************/

    fun onAgentStatusChanged(status: String, message: String = ""): Boolean {
        KLog.d("onAgentStatusChanged, status: $status, message: $message, currentApp: ${currentApp != null}", "AgentBus")
        val current = currentApp
        return current != null && current.onAgentStatusChanged(status, message)
    }

    fun onActionStatusChanged(actionName: String, status: String, message: String = "", result: Map<String, *>? = null) {
        KLog.d("onActionStatusChanged, actionName: $actionName, status: $status, message: $message, result: $result, currentApp: ${currentApp != null}", "AgentBus")
        val current = currentApp
        current?.onActionStatusChanged(actionName, status, message, result)
    }

    fun onTranscribed(transcription: Transcription): Boolean {
        KLog.d("onTranscribed, transcription: $transcription, currentApp: ${currentApp != null}", "AgentBus")
        val current = currentApp
        return current != null && current.onTranscribed(transcription)
    }

    /**
     * 执行动作，返回空表示目标app未处理，或需要后续处理
     */
    suspend fun executeActionEvent(event: ActionEvent): ActionCompletion? {
        KLog.d("executeActionEvent, currentAppId: ${event.appId}, event: $event", "AgentBus")
        // 先获取当前app的appId
        val currentAppId = event.appId
        if (currentAppId.isEmpty()) {
            return null
        }
        // 如果当前app已经退出，则直接结束，如果是系统action则让下一级处理
        val currentApp = getApp(currentAppId)
        KLog.d("executeActionEvent, currentApp: $currentApp", "AgentBus")
        if (currentApp == null) {
            return if (event.name.startsWith("orion.")) {
                null
            } else ActionCompletion(
                status = ActionCompletion.FAILED,
                message = "The current app(${currentAppId}) does not exist or has been exited"
            )
        }

        // 优先分配给当前app去执行action
        val completion = currentApp.executeActionEvent(event)
        KLog.d("executeActionEvent currentApp finish, appId: ${event.appId}, completion: $completion", "AgentBus")
        // 如果当前app已经处理，那么直接返回
        if (completion != null) {
            return completion
        }

        // 如果此action是另一个应用的开放action，那么分配给其所属app去执行
        val actionRegistry = getActionRegistry(event.name)
        return if (actionRegistry != null) {
            event.appId = actionRegistry.appId
            getApp(event.appId)?.executeActionEvent(event)
        } else null
    }
}
