package com.ainirobot.agent.bus

import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.content.pm.ServiceInfo
import android.os.Build
import com.ainirobot.agent.base.ActionRegistry
import com.ainirobot.common.utils.KLog

class APKSupervisor(private val context: Context) : PackageSupervisor() {

    companion object {

        private const val ACTION_CLIENT_SERVICE = "com.ainirobot.agent.action.AGENT_CLIENT_SERVICE"
    }

    private val mapping = mutableMapOf<String, String>()

    private val packageReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context, intent: Intent) {
            val packageName = intent.data?.schemeSpecificPart ?: return
            if (intent.action == Intent.ACTION_PACKAGE_ADDED) {
                val serviceInfo = getServiceInfo(packageName)
                KLog.d("onPackageAdded: $packageName, serviceInfo: $serviceInfo", "APKSupervisor")
                if (serviceInfo != null) {
                    onPackageAdded(packageName, serviceInfo)
                }
            } else if (intent.action == Intent.ACTION_PACKAGE_REMOVED) {
                val appId = synchronized(mapping) {
                    mapping.remove(packageName)
                }?: return
                removeActionRegistry(appId)
            }
        }
    }

    private fun getServiceInfo(packageName: String): ServiceInfo? {
        val intent = Intent(ACTION_CLIENT_SERVICE)
        intent.setPackage(packageName)
        val infos = context.packageManager.queryIntentServices(intent, 0)
        return if (infos.isNotEmpty()) {
            infos.first().serviceInfo
        } else null
    }

    override fun onCreate() {
        val resolveInfos = context.packageManager.queryIntentServices(
            Intent(ACTION_CLIENT_SERVICE),
            0
        )
        context.registerReceiver(
            packageReceiver,
            IntentFilter().also {
                it.addAction(Intent.ACTION_PACKAGE_ADDED)
                it.addAction(Intent.ACTION_PACKAGE_REMOVED)
                it.addDataScheme("package")
            }
        )
        KLog.d("resolveInfos size: ${resolveInfos.size}, resolveInfos: $resolveInfos", "APKSupervisor")
        for (resolveInfo in resolveInfos) {
            onPackageAdded(resolveInfo.serviceInfo.packageName, resolveInfo.serviceInfo)
        }
    }

    private fun onPackageAdded(packageName: String, serviceInfo: ServiceInfo) {
        val pm = context.packageManager
        val appInfo = pm.getApplicationInfo(packageName, PackageManager.GET_META_DATA)
        val sdkVersion = appInfo.metaData.getString("com.ainirobot.agent.SDK_VERSION")
        KLog.d("SDK_VERSION: $sdkVersion, ${appInfo.packageName}, uid: ${appInfo.uid}", "APKSupervisor")
        val assets = pm.getResourcesForApplication(appInfo).assets
        val actionRegistry = ActionRegistry(assets)
        KLog.d("actionRegistry: $actionRegistry", "APKSupervisor")
        synchronized(mapping) {
            mapping[appInfo.packageName] = actionRegistry.appId
        }
        addActionRegistry(actionRegistry)
        startAgentClientService(serviceInfo, appInfo.uid)
    }

    private fun startAgentClientService(serviceInfo: ServiceInfo, uid: Int) {
        val intent = Intent()
        KLog.d("startAgentClientService, packageName: ${serviceInfo.packageName}, className: ${serviceInfo.name}", "APKSupervisor")
        intent.component = ComponentName(
            serviceInfo.packageName,
            serviceInfo.name
        )
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent)
        } else {
            context.startService(intent)
        }
    }

    override fun onDestroy() {
        context.unregisterReceiver(packageReceiver)
    }
}