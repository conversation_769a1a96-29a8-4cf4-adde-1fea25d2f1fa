package com.ainirobot.agent.bus

import com.ainirobot.agent.base.ActionEntity
import com.ainirobot.agent.base.ActionRegistry

abstract class PackageSupervisor {

    private val actionRegistries = mutableMapOf<String, ActionRegistry>()

    fun addActionRegistry(actionRegistry: ActionRegistry) {
        synchronized(actionRegistries) {
            actionRegistries[actionRegistry.appId] = actionRegistry
        }
    }

    fun removeActionRegistry(appId: String) {
        synchronized(actionRegistries) {
            actionRegistries.remove(appId)
        }
    }

    /**
     * 通过actionName获取Action配置，前提是actionName必须保持唯一
     */
    fun getAction(actionName: String): ActionEntity? {
        return getActionRegistry(actionName)?.getAction(actionName)
    }

    /**
     * 通过actionName获取app的注册表，前提是不同app的namespace必须保持唯一
     */
    fun getActionRegistry(actionName: String): ActionRegistry? {
        synchronized(actionRegistries) {
            return actionRegistries.values.firstOrNull { it.getAction(actionName) != null }
        }
    }

    abstract fun onCreate()

    abstract fun onDestroy()

}