package com.ainirobot.agent.core

import android.content.Context
import android.media.MediaRecorder
import android.util.Log
import com.ainirobot.agent.action.ExecutionSide
import com.ainirobot.common.ConnectivityAssist
import com.ainirobot.common.network.UserApi
import com.ainirobot.common.network.WebHookApi
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.KLog.e
import com.twilio.audioswitch.AudioDevice
import io.livekit.android.AudioOptions
import io.livekit.android.LiveKit
import io.livekit.android.LiveKitOverrides
import io.livekit.android.audio.AudioSwitchHandler
import io.livekit.android.events.RoomEvent
import io.livekit.android.events.collect
import io.livekit.android.room.Room
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import java.util.LinkedList
import kotlin.coroutines.CoroutineContext

enum class ConnectState {
    IDLE,
    CONNECTED,
    DISCONNECTING,
    DISCONNECTED,
    CONNECTING,
    FAILED
}

enum class SubscribeState {
    IDLE,
    SUBSCRIBED,
    UNSUBSCRIBED
}

class AgentClient(
    private val context: Context,
    val coroutineContext: CoroutineContext
) {

    private data class Entity(
        val topic: String,
        val data: ByteArray,
        val expiredTime: Long,
        val completion: (success: Boolean) -> Unit
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as Entity

            if (topic != other.topic) return false
            if (!data.contentEquals(other.data)) return false
            if (expiredTime != other.expiredTime) return false

            return true
        }

        override fun hashCode(): Int {
            var result = topic.hashCode()
            result = 31 * result + data.contentHashCode()
            result = 31 * result + expiredTime.hashCode()
            return result
        }
    }

    companion object {

        const val TOPIC_NORMAL = "lk-chat-topic"

        private const val POOL_MAX_SIZE = 100

        private fun createRoom(context: Context): Room {
            KLog.d("createRoom", "AgentClient")
            return LiveKit.create(
                context,
                overrides = LiveKitOverrides(
                    audioOptions = AudioOptions(
                        javaAudioDeviceModuleCustomizer = { builder ->
                            builder.setAudioRecordFactory(CustomAudioRecordFactory(context))
                            builder.setInputSampleRate(16000)
                            builder.setAudioSource(MediaRecorder.AudioSource.MIC)
                            builder.setUseStereoInput(false)
                            builder.setUseHardwareNoiseSuppressor(true)
                            builder.setUseHardwareAcousticEchoCanceler(true)
                        }
                    )
                )
            ).also {
                val audioHandler = it.audioHandler
                if (audioHandler is AudioSwitchHandler) {
                    audioHandler.preferredDeviceList = listOf(
                        AudioDevice.BluetoothHeadset::class.java,
                        AudioDevice.WiredHeadset::class.java,
                        AudioDevice.Speakerphone::class.java,
                        AudioDevice.Earpiece::class.java,
                    )
                }
            }
        }
    }

    private val dispatcher = MessageDispatcher(coroutineContext)
    private val coroutineScope = CoroutineScope(coroutineContext + Dispatchers.Default + SupervisorJob())
    private val connectStateFlow = MutableSharedFlow<ConnectState>(8)
    private val subscribeStateFlow = MutableSharedFlow<SubscribeState>(8)

    val connectObserver = connectStateFlow.asSharedFlow()
    val subscribeObserver = subscribeStateFlow.asSharedFlow()

    val transcription = dispatcher.transcription.asSharedFlow()
    val agentEvents = dispatcher.agentEvents.asSharedFlow()
    val commonEvents = dispatcher.commonEvents.asSharedFlow()

    val hasRunningAction: Boolean
        get() = dispatcher.hasRunningAction

    @Volatile
    var isAutoReconnect = true

    /**
     * 麦克风是否静音，当为true时仅仅是向服务端上传空数据包；
     */
//    var microphoneMuted: Boolean = false
//        set(value) {
//            field = value
//            room.setMicrophoneMute(value)
//        }

    private var room: Room? = null

    @Volatile
    var connectState = ConnectState.IDLE
        private set
    @Volatile
    private var subscribeState = SubscribeState.IDLE

    private val mutex = Mutex()
    private val dataPool = LinkedList<Entity>()

    private var reconnectDelay = 3000L

    fun initialize() {
        MessageSender.initialize(this)
    }

    fun connect() {
//        LiveKit.loggingLevel = LoggingLevel.DEBUG
//        LiveKit.enableWebRTCLogging = true
        KLog.d("connect", "AgentClient")
        coroutineScope.launch {
            connectInternal(this)
        }
    }

    private suspend fun connectInternal(coroutineScope: CoroutineScope) {
        KLog.d("connectInternal, room: $room, connectState: $connectState", "AgentClient")
        if (connectState == ConnectState.CONNECTING
            || connectState == ConnectState.CONNECTED
            || !ConnectivityAssist.isConnected) {
            return
        }
        notifyConnectState(ConnectState.CONNECTING)

        // 如果room不为空，则释放
        room?.release()
        room = createRoom(context)
        coroutineScope.launch {
            room!!.events.collect { event ->
                handleEvent(event)
            }
        }

        withContext(Dispatchers.IO) {
           try {
               UserApi.getRoomInfo()?.also {
                   val t1 = System.currentTimeMillis()
                   room!!.connect(it.url, it.token)
                   val t2 = System.currentTimeMillis()
                   KLog.d("connectInternal cost: ${t2 - t1}ms", "AgentClient")
               }
           } catch (e: Exception) {
               e.e("AgentClient")
               WebHookApi.sendAsync(
                   Log.getStackTraceString(e),
                   "AgentClient",
               )
               null
           }
        } ?: run {
            KLog.d("connectInternal failed", "AgentClient")
            notifyConnectState(ConnectState.FAILED)
            delay(reconnectDelay)
            if (connectState == ConnectState.FAILED) {
                autoReconnect()
            }
            reconnectDelay += 3000
            if (reconnectDelay > 60000) {
                reconnectDelay = 60000
            }
        }
    }

    private suspend fun notifyConnectState(state: ConnectState) {
        connectState = state
        connectStateFlow.emit(state)
    }

    private fun autoReconnect(): Boolean {
        KLog.d("autoReconnect, isAutoReconnect: $isAutoReconnect, connectState: $connectState", "AgentClient")
        if (!isAutoReconnect) {
            return false
        }

        return if (connectState == ConnectState.DISCONNECTED
            || connectState == ConnectState.FAILED
            || connectState == ConnectState.IDLE) {
            coroutineScope.launch {
                connectInternal(this)
            }
            true
        } else false
    }

    private suspend fun notifySubscribeState(state: SubscribeState) {
        subscribeState = state
        subscribeStateFlow.emit(state)
    }

    private suspend fun handleEvent(event: RoomEvent) {
        KLog.d("handleEvent, room: $room, event: $event", "AgentClient")
        val room = this.room ?: return
        when (event) {
            is RoomEvent.Connected -> {
                reconnectDelay = 3000
                val t1 = System.currentTimeMillis()
                val job = coroutineScope.launch {
                    val task = async(Dispatchers.IO) {
                        room.localParticipant.setMicrophoneEnabled(true)
                        KLog.d("setMicrophoneEnabled success, cost: ${System.currentTimeMillis() - t1}ms", "AgentClient")
                    }

                    try {
                        withTimeout(5000) {
                            task.await() // 等待任务完成
                        }
                    } catch (e: TimeoutCancellationException) {
                        KLog.d("setMicrophoneEnabled timeout: ${System.currentTimeMillis() - t1}ms", "AgentClient")
                        // 任务超时，强制结束进程
                        android.os.Process.killProcess(android.os.Process.myPid())
                    }
                }
                job.join()
                KLog.d("setMicrophoneEnabled end, cost: ${System.currentTimeMillis() - t1}ms", "AgentClient")
                notifyConnectState(ConnectState.CONNECTED)
                sendDelayedData()
            }
            is RoomEvent.Disconnected -> {
                room.localParticipant.setMicrophoneEnabled(false)
                room.release()
                this.room = null
                notifyConnectState(ConnectState.DISCONNECTED)
                autoReconnect()
            }
            is RoomEvent.TrackPublished -> {
                KLog.d("TrackPublished participant: ${event.participant.name}, ${event.participant.sid}, ${event.publication.track?.kind}, publication: ${event.publication.track?.sid}, ${event.publication.track?.name}", "AgentClient")
            }
            is RoomEvent.TrackUnpublished -> {
                KLog.d("TrackUnpublished participant: ${event.participant.name}, ${event.participant.sid}, ${event.publication.track?.kind}, publication: ${event.publication.track?.sid}, ${event.publication.track?.name}", "AgentClient")
            }
            is RoomEvent.Reconnecting -> {
                notifyConnectState(ConnectState.CONNECTING)
            }
            is RoomEvent.Reconnected -> {
                notifyConnectState(ConnectState.CONNECTED)
                sendDelayedData()
            }
            is RoomEvent.FailedToConnect -> {
                notifyConnectState(ConnectState.FAILED)
                autoReconnect()
            }
            is RoomEvent.TrackSubscribed -> {
                notifySubscribeState(SubscribeState.SUBSCRIBED)
            }
            is RoomEvent.TrackUnsubscribed -> {
                notifySubscribeState(SubscribeState.UNSUBSCRIBED)
            }
            else -> {
                dispatcher.onRoomEventReceived(event)
            }
        }
    }

    fun updateMetadata(metadata: Map<String, Any>) {
        if (connectState == ConnectState.CONNECTED
            && subscribeState == SubscribeState.SUBSCRIBED) {
            coroutineScope.launch {
                if (connectState == ConnectState.CONNECTED
                    && subscribeState == SubscribeState.SUBSCRIBED) {
                    room?.localParticipant?.updateMetadata(
                        MessageJson.toJson(metadata).also {
                            KLog.json(it, "AgentClient")
                        }
                    )
                }
            }
        }
    }

    suspend fun sendData(
        data: ByteArray,
        topic: String = TOPIC_NORMAL,
        isSafety: Boolean,
        expiredTime: Int,
        completion: (success: Boolean) -> Unit
    ) {
        KLog.d("sendData, room: $room, topic: $topic, isSafety: $isSafety, expiredTime: $expiredTime", "AgentClient")
        if (connectState == ConnectState.CONNECTED
            && subscribeState == SubscribeState.SUBSCRIBED) {
            try {
                room?.localParticipant?.publishData(data, topic=topic)
                completion(true)
            } catch (e: Exception) {
                e.printStackTrace()
                KLog.d("sendData failed: ${String(data)}", "AgentClient")
                completion(false)
            }
        } else if (isSafety) {
            mutex.withLock {
                if (dataPool.size >= POOL_MAX_SIZE) {
                    dataPool.poll()?.completion?.invoke(false)
                }
                dataPool.offer(Entity(topic, data, System.currentTimeMillis() + expiredTime, completion))
            }
        } else {
            completion(false)
        }
    }

    private suspend fun sendDelayedData() {
        KLog.d("sendDelayedData", "AgentClient")
        mutex.withLock {
            var entity = dataPool.poll()
            while (entity != null) {
                if (connectState == ConnectState.CONNECTED && subscribeState == SubscribeState.SUBSCRIBED) {
                    if (entity.expiredTime > System.currentTimeMillis()) {
                        try {
                            room?.localParticipant?.publishData(entity.data, topic=entity.topic)
                            entity.completion(true)
                        } catch (e: Exception) {
                            entity.completion(false)
                            e.printStackTrace()
                            e.e("AgentClient")
                        }
                    } else {
                        entity.completion(false)
                    }
                    entity = dataPool.poll()
                } else {
                    dataPool.offer(entity)
                    break
                }
            }
        }
    }

    fun reconnect() {
        KLog.d("reconnect", "AgentClient")
        isAutoReconnect = true
        if (!autoReconnect()) {
            disconnect(true)
        }
    }

    fun disconnect(tryReconnect: Boolean = false) {
        isAutoReconnect = tryReconnect
        KLog.d("disconnect, room: $room, connectState: $connectState, isAutoReconnect: $isAutoReconnect", "AgentClient")
        if (connectState == ConnectState.CONNECTED) {
            coroutineScope.launch {
                if (connectState == ConnectState.CONNECTED) {
                    val t1 = System.currentTimeMillis()
                    room?.disconnect()
                    val t2 = System.currentTimeMillis()
                    KLog.d("disconnect cost: ${t2 - t1}ms", "AgentClient")
                    notifyConnectState(ConnectState.DISCONNECTING)
                }
            }
        }
    }

    suspend fun executeAction(
        actionName: String,
        executionSide: ExecutionSide,
        timeout: Int,
        params: Map<String, *> = emptyMap<String, String>()
    ) {
        dispatcher.executeAction(
            actionName, executionSide, timeout, params
        )
    }
}