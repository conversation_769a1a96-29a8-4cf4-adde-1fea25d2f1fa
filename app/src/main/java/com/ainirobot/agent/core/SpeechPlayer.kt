package com.ainirobot.agent.core

import com.ainirobot.bridge.executor.ActionEvents
import com.ainirobot.agent.base.utils.DigitalUtil
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.KLog.e
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeoutOrNull
import java.util.concurrent.ConcurrentLinkedQueue

data class AudioInfo(
    val isStream: Boolean,
    val text: String? = null,
    val streamConfig: Map<String, *>? = null,
    val timeoutMillis: Long = 60000
)

enum class SpeakState {
    IDLE,
    SPEAK_START,
    SPEAK_END
}

object SpeechPlayer {

    private enum class PlayState {
        IDLE,
        PLAYING,
        STOPPED
    }

    private class PlayTask(
        val audio: AudioInfo,
        var callback: ((ActionResult?) -> Unit)? = null
    ) {

        private val messageId = DigitalUtil.uuid()
        @Volatile
        var state = PlayState.IDLE

        fun start() {
            if (state != PlayState.IDLE) {
                KLog.d("PlayTTS Task already processed: $state", TAG)
                currentTask = null
                executeTask()
                return
            }
            KLog.d("PlayTTS start: $audio", TAG)
            state = PlayState.PLAYING
            beginSpeaking()
            val action: String
            val params: Map<String, *>
            if (audio.isStream) {
                action = ActionEvents.Server.REALTIME_SAY
                params = audio.streamConfig!!
                KLog.d("PlayTTS start streamConfig: ${audio.streamConfig}", "MessageSender")
            } else {
                action = ActionEvents.Server.TTS_PLAY
                params = mapOf("text" to audio.text!!)
                KLog.d("PlayTTS start text: ${audio.text}", "MessageSender")
            }

            MessageSender.executeAction(
                action = action,
                params = params,
                planId = "",
                actionId = "",
                runId = DigitalUtil.uuid(),
                messageId = messageId,
                isSafety = false
            ) { result ->
                KLog.d("PlayTTS result: $result", "MessageSender")
                onCompletion(result as? ActionResult, true)
            }
        }

        @Synchronized
        private fun onCompletion(result: ActionResult?, executeNext: Boolean) {
            KLog.d("PlayTTS onCompletion: $result, state: $state", TAG)
            if (state != PlayState.PLAYING) {
                return
            }
            callback?.invoke(result)
            callback = null
            currentTask = null
            endSpeaking()
            state = PlayState.STOPPED
            if (executeNext) {
                executeTask()
            }
        }

        private fun sendAbortMessage() {
            MessageSender.sendMessage(
                SpeechAbortMessage(
                    SpeechAbort(
                        message = "Client interrupt speech"
                    )
                )
            )
        }

        fun stop(isNotifyServer: Boolean) {
            KLog.d("PlayTTS stop: $state, callback: $callback", TAG)
            when (state) {
                PlayState.IDLE -> {
                    callback?.invoke(newFailResult("force stopped, not waiting"))
                    callback = null
                    state = PlayState.STOPPED
                }
                PlayState.PLAYING -> {
                    onCompletion(
                        newFailResult("force stopped"),
                        false
                    )
                    coroutineScope.launch {
                        MessageSender.removeCallback(messageId)
                        if (isNotifyServer) {
                            sendAbortMessage()
                        }
                    }
                }
                PlayState.STOPPED -> {
                    KLog.d("PlayTTS Task already stopped", TAG)
                }
            }
        }
    }

    private const val TAG = "SpeechPlayer"
    private val taskQueue = ConcurrentLinkedQueue<PlayTask>()
    private val coroutineScope = CoroutineScope(Dispatchers.IO + Job())
    @Volatile
    private var currentTask: PlayTask? = null

    private val speakStateFlow = MutableStateFlow(SpeakState.IDLE)
    val speakState = this.speakStateFlow.asStateFlow()

    /**
     * 设置语音状态为开始说话
     */
    fun beginSpeaking() {
        KLog.d("Setting speak state to SPEAK_START", TAG)
        speakStateFlow.value = SpeakState.SPEAK_START
    }

    /**
     * 设置语音状态为结束说话
     */
    fun endSpeaking() {
        KLog.d("Setting speak state to SPEAK_END", TAG)
        speakStateFlow.value = SpeakState.SPEAK_END
    }

    /**
     * 设置语音状态为空闲
     */
    fun resetSpeechState() {
        KLog.d("Setting speak state to IDLE", TAG)
        speakStateFlow.value = SpeakState.IDLE
    }

    private fun newFailResult(
        message: String = ""
    ): ActionResult {
        return ActionResult(
            resultType = ResultType.CLIENT_REQUESTED,
            runId = "",
            planId = "",
            actionId = "",
            actionName = "",
            status = false,
            message = message,
            resultId = "",
            pushAudio = false
        )
    }

    suspend fun playSync(text: String, timeoutMillis: Long = 10000): ActionResult? {
        return this.playSync(
            AudioInfo(
                false,
                text,
                timeoutMillis = timeoutMillis
            )
        )
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun playSync(audio: AudioInfo): ActionResult? {
        KLog.d("PlayTTS playSync: text=${audio.text}, isStream=${audio.isStream}, timeoutMillis: ${audio.timeoutMillis}", TAG)
        var task: PlayTask? = null
        val result = withTimeoutOrNull(audio.timeoutMillis) {
            suspendCancellableCoroutine { continuation ->
                task = play(
                    audio = audio
                ) { result: ActionResult? ->
                    try {
                        continuation.resume(result) { e ->
                            e.e(TAG)
                        }
                    } catch (e: IllegalStateException) {
                        KLog.d("PlayTTS IllegalStateException ${e.message}", TAG)
                    }
                }
            }
        }
        if (result == null) {
            KLog.d("PlayTTS timeout", TAG)
            task?.stop(true)
        }
        return result
    }

    private fun play(audio: AudioInfo, callback: ((ActionResult?) -> Unit)? = null): PlayTask {
        KLog.d("PlayTTS play: text=${audio.text}, isStream=${audio.isStream}", TAG)
        val task = PlayTask(audio, callback)
        taskQueue.offer(task)
        executeTask()
        return task
    }

    private fun executeTask() {
        KLog.d("PlayTTS executeTask: currentTask=$currentTask, taskQueue=${taskQueue.size}", TAG)
        if (currentTask != null) {
            return
        }
        currentTask = taskQueue.poll() ?: return
        currentTask?.start()
    }

    fun stop(isNotifyServer: Boolean = true) {
        KLog.d("StopTTS currentTask: $currentTask", TAG)
        currentTask?.stop(isNotifyServer)
        while (taskQueue.isNotEmpty()) {
            taskQueue.poll()?.stop(isNotifyServer)
        }
    }
}