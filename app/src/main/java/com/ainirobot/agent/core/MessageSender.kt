package com.ainirobot.agent.core

import com.ainirobot.agent.action.ActionEvent
import com.ainirobot.common.utils.DeviceOS
import com.ainirobot.agent.base.utils.DigitalUtil
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.KLog.e
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withTimeoutOrNull
import java.lang.ref.WeakReference

object MessageSender {

    /**
     * 一个数据包最大的字节数
     */
    private const val DATA_PACKET_MAX_SIZE = 14 * 1024

    /**
     * 分片数据包最大字符数
     */
    private const val DATA_PACKET_SECTION_LENGTH = 3000

    private enum class MessageType {
        ACTION,
        HEARTBEAT,
        TEST
    }

    private data class ActionHolder(
        val type: MessageType,
        val runId: String,
        val planId: String,
        val actionId: String,
        val actionName: String,
        val callback: ((CallbackResult) -> Unit)
    )

    @Volatile
    private var client: WeakReference<AgentClient>? = null

    private val internalClient: AgentClient
        get() = client?.get() ?: throw IllegalStateException("AgentClient is null")

    private val mutex = Mutex()
    private val callbackResults = mutableMapOf<String, ActionHolder>()
    private val coroutineScope: CoroutineScope by lazy {
        CoroutineScope(internalClient.coroutineContext + Dispatchers.Default + SupervisorJob())
    }

    private var isInitialized = false

    fun initialize(client: AgentClient) {
        this.client = WeakReference(client)
        this.isInitialized = true
    }

    /**
     * 向Agent发送数据消息
     */
    private suspend fun sendData(id: String, data: ByteArray, isSafety: Boolean) {
        if (!isInitialized) {
            return
        }
        internalClient.sendData(
            data,
            isSafety = isSafety,
            expiredTime = 30000 // 当isSafety为true时有效，消息在30s内未发送成功则认为发送失败，且不再重试
        ) { success ->
            if (!success) {
                callbackResults.remove(id)?.let {
                    when (it.type) {
                        MessageType.ACTION -> {
                            it.callback(
                                ActionResult(
                                    ResultType.CLIENT_REQUESTED,
                                    it.runId,
                                    it.planId,
                                    it.actionId,
                                    it.actionName,
                                    false,
                                    "Send message failed",
                                    "",
                                    false
                                )
                            )
                        }
                        MessageType.HEARTBEAT -> {
                            it.callback(Heartbeat())
                        }
                        MessageType.TEST -> {
                            it.callback(Connectivity(0, 0))
                        }
                    }
                }
            }
        }
    }

    /**
     * 向Agent发送数据消息
     */
    fun sendMessage(
        message: IMessage<*>,
        isSafety: Boolean = true,
        delayTime: Long = 0
    ) {
        if (!isInitialized) {
            return
        }
        coroutineScope.launch {
            if (delayTime > 0) {
                delay(delayTime)
            }
            val json = MessageJson.toJson(
                MessageWrapper(
                    id = message.id,
                    message = message.toJson(),
                    timestamp = System.currentTimeMillis()
                )
            )
            val data = json.toByteArray(Charsets.UTF_8)
            KLog.d("sendMessage byte size = ${data.size}", "MessageSender")
            if (data.size < DATA_PACKET_MAX_SIZE) {
                KLog.json("MessageSender") { json }
                sendData(message.id, data, isSafety)
                return@launch
            }

            val content = when (message) {
                is Message -> {
                    MessageJson.toJson(message.content)
                }
                is MessageExpose -> {
                    MessageJsonExpose.toJson(message.content)
                }
            }

            var total = content.length / DATA_PACKET_SECTION_LENGTH
            if (content.length % DATA_PACKET_SECTION_LENGTH > 0) {
                total++
            }
            KLog.d("SectionMessage total = $total", "MessageSender")
            for (i in 0 until total) {
                val start = i * DATA_PACKET_SECTION_LENGTH
                var end = (i + 1) * DATA_PACKET_SECTION_LENGTH
                if (end > content.length) {
                    end = content.length
                }
                val section = content.substring(start, end)
                val sectionJson = MessageJson.toJson(
                    MessageWrapper(
                        id = message.id,
                        message = SectionMessage(
                            id = message.id,
                            type = message.type,
                            content = section,
                            total = total,
                            index = i + 1
                        ).toJson(),
                        timestamp = System.currentTimeMillis()
                    )
                )
                sendData(message.id, sectionJson.toByteArray(Charsets.UTF_8), isSafety)
            }
        }
    }

    /**
     * 向Agent发送数据消息
     */
    fun sendMessage(msgType: String, content: Map<String, Any>, isSafety: Boolean = true) {
        if (!isInitialized) {
            return
        }
        sendMessage(CommonMessage(name = msgType, content = content), isSafety)
    }

    /**
     * 执行服务端Action命令
     */
    fun executeAction(
        action: String,
        params: Map<String, Any?>,
        planId: String = "",
        actionId: String = "",
        runId: String = DigitalUtil.uuid(),
        messageId: String = DigitalUtil.uuid(),
        isSafety: Boolean = true,
        callback: ((CallbackResult?) -> Unit)? = null
    ) {
        if (!isInitialized) {
            callback?.invoke(null)
            return
        }
        val commandMessage = ActionMessage(
            content = Action(
                runId = runId,
                planId = planId,
                action = action,
                actionId = actionId,
                params = params
            )
        )
        commandMessage.id = messageId
        commandMessage.deviceId = DeviceOS.deviceId

        coroutineScope.launch {
            callback?.let {
                mutex.withLock {
                    callbackResults[messageId] = ActionHolder(
                        type = MessageType.ACTION,
                        runId = runId,
                        planId = planId,
                        actionId = actionId,
                        actionName = action,
                        callback = it
                    )
                }
            }
            sendMessage(commandMessage, isSafety)
        }
    }

    suspend fun executeActionSync(
        event: ActionEvent,
        params: Map<String, Any?>? = null,
        isSafety: Boolean = true,
        timeoutMillis: Long = 0
    ): ActionResult? {
        if (!isInitialized) {
            return null
        }
        val timeout =  if (timeoutMillis <= 0) {
            when (event.timeout) {
                in 0..3000 -> {
                    2000
                }
                in 3001..5000 -> {
                    3000
                }
                else -> event.timeout - 5000
            }
        } else timeoutMillis
        return executeActionSync(
            action = event.name,
            params = params ?: event.params,
            planId = event.planId,
            actionId = event.actionId,
            runId = event.runId,
            timeoutMillis = timeout,
            isSafety = isSafety
        )
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun executeActionSync(
        action: String,
        params: Map<String, Any?>,
        planId: String = "",
        actionId: String = "",
        runId: String = DigitalUtil.uuid(),
        timeoutMillis: Long = Long.MAX_VALUE,
        isSafety: Boolean = true
    ): ActionResult? {
        if (!isInitialized) {
            return null
        }
        val messageId = DigitalUtil.uuid()
        val result = withTimeoutOrNull(timeoutMillis) {
            suspendCancellableCoroutine { continuation ->
                executeAction(
                    action = action,
                    params = params,
                    planId = planId,
                    actionId = actionId,
                    runId = runId,
                    messageId = messageId,
                    isSafety = isSafety
                ) { result: CallbackResult? ->
                    continuation.resume(result) { e ->
                        e.e("MessageSender")
                    }
                }
            }
        }
        if (result == null) {
            removeCallback(messageId)
        }
        return result as? ActionResult
    }

    fun onResultReceived(result: CallbackResult) {
        if (!isInitialized) {
            return
        }
        val requestId =result.requestId
        if (requestId.isEmpty()) {
            return
        }
        coroutineScope.launch {
            mutex.withLock {
                callbackResults.remove(requestId)?.callback?.invoke(result)
            }
        }
    }

    fun onAbortReceived(result: SpeechAbort) {
        if (!isInitialized) {
            return
        }
        val requestId = result.requestId
        if (requestId.isEmpty()) {
            return
        }

        coroutineScope.launch {
            SpeechPlayer.stop(false)
            mutex.withLock {
                callbackResults.remove(requestId)?.let {
                    it.callback(
                        ActionResult(
                            ResultType.CLIENT_REQUESTED,
                            it.runId,
                            it.planId,
                            it.actionId,
                            it.actionName,
                            false,
                            result.message,
                            "",
                            false,
                            result = mapOf("message" to result.message)
                        )
                    )
                }
            }
        }
    }

    /**
     * 上报Plan&Action的执行状态
     */
    fun sendStatus(
        planId: String,
        runId: String,
        status: RunningStatus,
        nodes: List<ActionStatus>? = null
    ) {
        sendMessage(
            PlanStatusMessage(
                content = PlanStatus(
                    planId = planId,
                    runId = runId,
                    status = status,
                    nodes = nodes
                )
            )
        )
    }

    suspend fun removeCallback(requestId: String) {
        mutex.withLock {
            callbackResults.remove(requestId)
        }
    }

    /**
     * 发送心跳命令
     */
    private fun sendHeartbeat(
        messageId: String,
        callback: ((CallbackResult) -> Unit)
    ) {
        if (!isInitialized) {
            return
        }
        val message = HeartbeatMessage(
            content = Heartbeat()
        )
        message.id = messageId
        message.deviceId = DeviceOS.deviceId

        coroutineScope.launch {
            mutex.withLock {
                callbackResults[messageId] = ActionHolder(
                    type = MessageType.HEARTBEAT,
                    runId = "",
                    planId = "",
                    actionId = "",
                    actionName = "",
                    callback = callback
                )
            }
            sendMessage(message, false)
        }
    }

    suspend fun sendHeartbeatSync(
        timeoutMillis: Long = Long.MAX_VALUE
    ): Heartbeat? {
        if (!isInitialized) {
            return null
        }
        val messageId = DigitalUtil.uuid()
        val result = withTimeoutOrNull(timeoutMillis) {
            suspendCancellableCoroutine { continuation ->
                sendHeartbeat(
                    messageId = messageId
                ) { result: CallbackResult? ->
                    continuation.resume(result) { e ->
                        e.e("MessageSender")
                    }
                }
            }
        }
        if (result == null) {
            removeCallback(messageId)
        }
        return result as? Heartbeat
    }

    /**
     * 发送连测试消息
     */
    private fun sendTestMessage(
        messageId: String,
        content: Connectivity?,
        callback: ((CallbackResult) -> Unit)
    ) {
        if (!isInitialized) {
            return
        }
        val message = ConnectivityTestMessage(
            content = content ?: Connectivity(
                serverTimestamp = 0,
                clientTimestamp = System.currentTimeMillis()
            )
        )
        message.id = messageId
        message.deviceId = DeviceOS.deviceId

        coroutineScope.launch {
            mutex.withLock {
                callbackResults[messageId] = ActionHolder(
                    type = MessageType.TEST,
                    runId = "",
                    planId = "",
                    actionId = "",
                    actionName = "",
                    callback = callback
                )
            }
            sendMessage(message, false)
        }
    }

    suspend fun sendTestMessageSync(
        messageId: String = DigitalUtil.uuid(),
        content: Connectivity? = null,
        timeoutMillis: Long = Long.MAX_VALUE
    ): Connectivity? {
        if (!isInitialized) {
            return null
        }
        val result = withTimeoutOrNull(timeoutMillis) {
            suspendCancellableCoroutine { continuation ->
                sendTestMessage(
                    messageId = messageId,
                    content = content
                ) { result: CallbackResult? ->
                    continuation.resume(result) { e ->
                        e.e("MessageSender")
                    }
                }
            }
        }
        if (result == null) {
            removeCallback(messageId)
        }
        return result as? Connectivity
    }
}