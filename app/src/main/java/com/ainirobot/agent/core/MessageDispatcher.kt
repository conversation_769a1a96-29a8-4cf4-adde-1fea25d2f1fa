package com.ainirobot.agent.core

import com.ainirobot.agent.action.ActionBus
import com.ainirobot.agent.action.AgentEvent
import com.ainirobot.agent.action.CommonEvent
import com.ainirobot.agent.action.ExecutionSide
import com.ainirobot.agent.base.Transcription
import com.ainirobot.common.Config.defaultScope
import com.ainirobot.common.utils.DateUtil.dateFormat
import com.ainirobot.common.utils.KLog
import io.livekit.android.annotations.Beta
import io.livekit.android.events.RoomEvent
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import org.greenrobot.eventbus.EventBus
import kotlin.coroutines.CoroutineContext
import com.ainirobot.common.debug.CaptionWindow
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.atomic.AtomicInteger

private class AssembledMessage(
    val total: Int,
    private val callback: suspend () -> Unit
) {
    private val sections = arrayOfNulls<SectionMessage>(total)
    private val count = AtomicInteger(0)

    val isCompleted: Boolean
        get() = count.get() == total

    var expiredJob: Job? = null

    init {
        expiredJob = defaultScope.launch {
            delay(60000)
            callback()
            expiredJob = null
        }
    }

    fun addSection(section: SectionMessage) {
        if (sections[section.index - 1] != null) {
            throw IllegalStateException("Duplicate section index: ${section.index}")
        }
        sections[section.index - 1] = section
        count.incrementAndGet()
    }

    fun toMessage(): IMessage<*> {
        expiredJob?.cancel()
        expiredJob = null
        val content = StringBuilder()
        val section = sections[0]!!
        sections.forEach { content.append(it!!.content) }
        val entity = MessageEntity(
            id = section.id,
            type = section.type,
            queryId = section.queryId ?: "",
            query = section.query ?: "",
            version = section.version ?: "",
            elapseInfo = section.elapseInfo,
            content = MessageJson.fromJson(
                content.toString(),
                object : TypeToken<Map<String, Any>>() {}.type
            )
        ).toJson()
        return MessageJson.fromJson(
            entity,
            Message::class.java
        )
    }
}

class MessageDispatcher(
    coroutineContext: CoroutineContext
) {
    private val actionBus = ActionBus(coroutineContext)
    val transcription = MutableStateFlow(Transcription("", "", "", false, ""))
    val agentEvents: MutableSharedFlow<AgentEvent> get() = actionBus.agentEventEmitter
    val commonEvents: MutableSharedFlow<CommonEvent> = actionBus.commonEventEmitter
    val hasRunningAction: Boolean get() = actionBus.hasRunningAction

    private val messagePools = mutableMapOf<String, AssembledMessage>()
    private val lock = Mutex()

    @OptIn(Beta::class)
    suspend fun onRoomEventReceived(event: RoomEvent) {
        when (event) {
            is RoomEvent.TranscriptionReceived -> {
                KLog.d("==========================TranscriptionReceived==========================", "MessageDispatcher")
//                KLog.d("TranscriptionReceived: ${event.participant?.name}, ${event.participant?.identity?.value}, ${event.participant?.participantInfo}", "MessageDispatcher")
                event.transcriptionSegments.forEach {
                    KLog.d("TranscriptionReceived Segment, sid: ${it.id}, text: ${it.text},Start：${it.startTime}, End: ${it.endTime}, Final: ${it.final}", "MessageDispatcher")
                }
                val seg = event.transcriptionSegments.last()
                val flag = seg.text.indexOf("/[")
                var text = seg.text
                var error = ""
                if (flag >= 0) {
                    text = seg.text.substring(0, flag)
                    error = seg.text.substring(flag + 2, seg.text.length - 1)
                }
                transcription.value = Transcription(
                    seg.id,
                    text,
                    event.participant?.identity?.value ?: "Unknown",
                    seg.final,
                    error
                ).also {
                    if (it.isUserSpeaking && it.final) {
                        EventBus.getDefault().post(it)
                    }
                }
            }
            is RoomEvent.DataReceived -> {
                val topic = event.topic ?: return
                if (topic == AgentClient.TOPIC_NORMAL) {
                    val messageWrapper = MessageJson.fromJson(
                        String(event.data),
                        MessageWrapper::class.java
                    )
                    KLog.json(messageWrapper.message, "MessageDispatcher")
                    KLog.d("DataReceived: MessageId=${messageWrapper.id}, Time: ${messageWrapper.timestamp.dateFormat("yyyy-MM-dd HH:mm:ss.SSS")}", "MessageDispatcher")
                    val message = MessageJson.fromJson(
                        messageWrapper.message,
                        Message::class.java
                    )
                    if (message is EmptyMessage) {
                        return
                    }
                    message.id = messageWrapper.id
                    message.participant = event.participant?.name ?: ""
                    if (message is SectionMessage) {
                        KLog.d("SectionMessage: id=${message.id}, index=${message.index}, total=${message.total}", "MessageDispatcher")
                        lock.withLock {
                            var assembledMessage = messagePools[message.id]
                            if (assembledMessage == null) {
                                assembledMessage = AssembledMessage(message.total) {
                                    lock.withLock {
                                        messagePools.remove(message.id)
                                    }
                                }
                                messagePools[message.id] = assembledMessage
                            }
                            assembledMessage.addSection(message)
                            if (assembledMessage.isCompleted) {
                                dispatchMessage(assembledMessage.toMessage())
                            }
                        }
                        return
                    }
                    dispatchMessage(message)
                }
            }
            else -> {}
        }
    }

    private suspend fun dispatchMessage(message: IMessage<*>) {
        KLog.d("dispatchMessage: ${message.type}", "MessageDispatcher")
        when (message) {
            is PlanMessage -> {
                actionBus.onPlanMessageReceived(message)
            }
            is HeartbeatMessage -> {
                MessageSender.onResultReceived(message.content)
            }
            is ConnectivityTestMessage -> {
                MessageSender.onResultReceived(message.content)
            }
            is SpeechAbortMessage -> {
                MessageSender.onAbortReceived(message.content)
            }
            is ResultMessage -> {
                when (message.content.resultType) {
                    ResultType.CLIENT_REQUESTED -> {
                        MessageSender.onResultReceived(message.content)
                    }
                    ResultType.SERVER_PREPROCESSED -> {
                        actionBus.onResultMessageReceived(message)
                    }
                    ResultType.SERVER_SEND -> {
                        KLog.d("暂不处理服务端主动发送的结果: $message", "MessageDispatcher")
                    }
                }
            }
            is CommonMessage -> {
                actionBus.executeCommonEvent(
                    CommonEvent(
                        name = message.name,
                        params = message.content
                    )
                )
            }
            is RunStepMessage -> {
                val stepDisplayName = message.content["step_display_name"] as? String
                if (!stepDisplayName.isNullOrEmpty()) {
                    CaptionWindow.updateAIExecutionProgress(stepDisplayName, false)
                }
            }
            else -> { }
        }
    }

    suspend fun executeAction(
        actionName: String,
        executionSide: ExecutionSide,
        timeout: Int,
        params: Map<String, *>
    ) {
        actionBus.executeAction(
            actionName, executionSide, timeout, params
        )
    }
}
