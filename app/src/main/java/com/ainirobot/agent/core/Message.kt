package com.ainirobot.agent.core

import com.ainirobot.agent.base.AppInfo
import com.ainirobot.agent.base.PageInfo
import com.ainirobot.agent.base.utils.DigitalUtil
import com.ainirobot.common.DataStore
import com.ainirobot.common.utils.DateUtil.dateFormat
import com.ainirobot.common.utils.DeviceOS
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import java.lang.reflect.Type

class MessageDeserializer : JsonDeserializer<IMessage<*>> {

    override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): IMessage<*> {
        val jsonObject = json.asJsonObject
        val msgType = jsonObject.get("msg_type") ?: return EmptyMessage()
        val total = jsonObject.get("total")
        if (total != null && total.asInt > 1) {
            return context.deserialize(jsonObject, SectionMessage::class.java)
        }
        return when (msgType.asString) {
            "plan.plan" -> context.deserialize(jsonObject, PlanMessage::class.java)
            "run.run_action" -> context.deserialize(jsonObject, ActionMessage::class.java)
            "run.action_result" -> context.deserialize(jsonObject, ResultMessage::class.java)
            "run.interrupt_speech" -> context.deserialize(jsonObject, SpeechAbortMessage::class.java)
            "run.connectivity_test" -> context.deserialize(jsonObject, ConnectivityTestMessage::class.java)
            "monitor.heartbeat" -> context.deserialize(jsonObject, HeartbeatMessage::class.java)
            "run.step" -> context.deserialize(jsonObject, RunStepMessage::class.java)
            else -> {
                context.deserialize<CommonMessage>(jsonObject, CommonMessage::class.java).also {
                    it.name = it.type
                }
            }
        }
    }
}

val MessageJson = GsonBuilder()
    .disableHtmlEscaping()
    .registerTypeAdapter(Message::class.java, MessageDeserializer())
    .create()!!

val MessageJsonExpose = GsonBuilder()
    .disableHtmlEscaping()
    .registerTypeAdapter(Message::class.java, MessageDeserializer())
    .excludeFieldsWithoutExposeAnnotation()
    .create()!!

data class MessageWrapper(
    @SerializedName("id")
    val id: String,
    @SerializedName("message")
    val message: String,
    @SerializedName("timestamp")
    val timestamp: Long
)

sealed interface IMessage<T> {
    val type: String
    val id: String
    val total: Int
    val index: Int
    val content: T

    fun toJson(): String
}

sealed class Message<T>(
    @SerializedName("msg_type")
    override val type: String,
    @SerializedName("content")
    override val content: T,
    @SerializedName("id")
    override var id: String = DigitalUtil.uuid(),
    @SerializedName("total")
    override val total: Int = 1,
    @SerializedName("idx")
    override val index: Int = 1
) : IMessage<T> {
    @Transient var participant: String = ""
    @SerializedName("device_id")
    var deviceId: String = DeviceOS.deviceId
    @SerializedName("agent_id")
    var agentId: String = DataStore.currentAgentId

    override fun toJson(): String {
        return MessageJson.toJson(this)
    }
}

sealed class MessageExpose<T>(
    @Expose
    @SerializedName("msg_type")
    override val type: String,
    @Expose
    @SerializedName("content")
    override val content: T,
    @Expose
    @SerializedName("id")
    override val id: String = DigitalUtil.uuid(),
    @Expose
    @SerializedName("total")
    override val total: Int = 1,
    @Expose
    @SerializedName("idx")
    override val index: Int = 1
) : IMessage<T> {
    @Transient var participant: String = ""
    @Expose
    @SerializedName("device_id")
    var deviceId: String = DeviceOS.deviceId
    @Expose
    @SerializedName("agent_id")
    var agentId: String = DataStore.currentAgentId

    override fun toJson(): String {
        return MessageJsonExpose.toJson(this)
    }
}

class EmptyMessage : Message<String>("empty", "")

sealed class CallbackResult {
    @SerializedName("request_msg_id")
    var requestId: String = ""
}

class HeartbeatMessage(
    content: Heartbeat
): Message<Heartbeat>("monitor.heartbeat", content)

data class Heartbeat(
    @SerializedName("timestamp")
    val timestamp: String = System.currentTimeMillis().dateFormat(),
    @SerializedName("client")
    val client: Map<String, *>? = null,
    @SerializedName("server")
    val server: ServerHeartbeat? = null
) : CallbackResult()

data class ServerHeartbeat(
    @SerializedName("subscribed_tracks")
    val subscribedTracks: List<Map<String, *>>? = null,
    @SerializedName("published_tracks")
    val publishedTracks: List<Map<String, *>>? = null
) {
    val isDisconnected: Boolean
        get() = subscribedTracks.isNullOrEmpty() || publishedTracks.isNullOrEmpty()
}

class SpeechAbortMessage(
    content: SpeechAbort
): Message<SpeechAbort>("run.interrupt_speech", content)

data class SpeechAbort(
    @SerializedName("message")
    val message: String
) : CallbackResult()

class ConnectivityTestMessage(
    content: Connectivity
): Message<Connectivity>("run.connectivity_test", content)

data class Connectivity(
    @SerializedName("server_timestamp")
    val serverTimestamp: Long,
    @SerializedName("client_timestamp")
    val clientTimestamp: Long
) : CallbackResult()

class PlanMessage(
    @SerializedName("action_version")
    val version: String,
    @SerializedName("query_id")
    val queryId: String,
    @SerializedName("user_query")
    val query: String,
    content: Plan,
    @SerializedName("elapse_info")
    var elapseInfo: Map<String, *>? = null
): Message<Plan>("plan.plan", content)

data class Plan(
    @SerializedName("plan_id")
    val id: String,
    @SerializedName("run_id")
    val runId: String,
    @SerializedName("app_id")
    val appId: String = "",
    @SerializedName("page_id")
    val pageId: String = "",
    @SerializedName("plan_type")
    val type: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("message")
    val message: String,
    @SerializedName("plan_xml")
    val xml: String,
    @SerializedName("execute_immediately")
    val executeImmediately: Boolean,
    @SerializedName("waiting_reason")
    val waitingReason: String? = null
)

class ActionMessage(
    content: Action,
): Message<Action>("run.run_action", content)

data class Action(
    @SerializedName("run_id")
    val runId: String,
    @SerializedName("action_name")
    val action: String,
    @SerializedName("parameters")
    val params: Map<String, Any?>,
    @SerializedName("plan_id")
    val planId: String? = null,
    @SerializedName("node_id")
    val actionId: String? = null
)

enum class ResultType {
    @SerializedName("client_requested")
    CLIENT_REQUESTED,
    @SerializedName("server_preprocessed")
    SERVER_PREPROCESSED,
    @SerializedName("server_sent")
    SERVER_SEND
}

class ResultMessage(
    content: ActionResult,
): Message<ActionResult>("run.action_result", content)

data class ActionResult(
    @SerializedName("result_type")
    val resultType: ResultType,
    @SerializedName("run_id")
    val runId: String,
    @SerializedName("plan_id")
    val planId: String = "",
    @SerializedName("node_id")
    val actionId: String = "",
    @SerializedName("action_name")
    val actionName: String,
    @SerializedName("status")
    val status: Boolean,
    @SerializedName("message")
    val message: String,
    @SerializedName("result_id")
    val resultId: String,
    @SerializedName("push_audio")
    val pushAudio: Boolean,
    @SerializedName("result")
    val result: Map<String, *>? = null,
) : CallbackResult()

/**
 * 通用数据消息
 */
class CommonMessage(
    @Transient
    var name: String,
    content: Map<String, Any>,
): Message<Map<String, Any>>(name, content)

/**
 * 通用数据消息Expose
 */
class CommonMessageExpose(
    @Transient
    var name: String,
    content: Map<String, Any>,
): MessageExpose<Map<String, Any>>(name, content)

class AppInfoMessage(
    content: AppInfo,
): MessageExpose<AppInfo>("state.robot_state", content)

class PageInfoMessage(
    content: PageInfo,
): MessageExpose<PageInfo>("state.robot_state", content)

enum class RunningStatus {
    @SerializedName("pending")
    PENDING,
    @SerializedName("running")
    RUNNING,
    @SerializedName("succeeded")
    SUCCEEDED,
    @SerializedName("failed")
    FAILED,
    @SerializedName("waiting")
    WAITING,
    @SerializedName("interrupted")
    INTERRUPTED,
    @SerializedName("recalled")
    RECALLED,
    @SerializedName("timeout")
    TIMEOUT
}

/**
 * Plan运行状态消息
 */
class PlanStatusMessage(
    content: PlanStatus,
): Message<PlanStatus>("run.state", content)

data class PlanStatus(
    @SerializedName("plan_id")
    val planId: String,
    @SerializedName("run_id")
    val runId: String,
    @SerializedName("status")
    val status: RunningStatus,
    @SerializedName("nodes")
    val nodes: List<ActionStatus>?
)

data class ActionStatus(
    @SerializedName("node_id")
    val actionId: String,
    @SerializedName("status")
    val status: RunningStatus,
    @SerializedName("result")
    val result: Map<String, *>? = null,
    @SerializedName("exception")
    val exception: String = "",
    @SerializedName("updated_at")
    val updateTime: Long = System.currentTimeMillis()
)

class RunStepMessage(
    content: Map<String, Any>
) : Message<Map<String, Any>>("run.step", content)

class MessageEntity(
    id:String,
    type: String,
    @SerializedName("query_id")
    val queryId: String,
    @SerializedName("user_query")
    val query: String,
    @SerializedName("action_version")
    val version: String,
    @SerializedName("elapse_info")
    val elapseInfo: Map<String, *>?,
    content: Any
): Message<Any>(
    type = type,
    content = content,
    id = id
)

/**
 * 分片数据消息
 */
class SectionMessage(
    id:String,
    type: String,
    content: String,
    total: Int,
    index: Int,
    @SerializedName("query_id")
    val queryId: String? = null,
    @SerializedName("user_query")
    val query: String? = null,
    @SerializedName("action_version")
    val version: String? = null,
    @SerializedName("elapse_info")
    var elapseInfo: Map<String, *>? = null
): Message<String>(
    type = type,
    content = content,
    id = id,
    total = total,
    index = index
)