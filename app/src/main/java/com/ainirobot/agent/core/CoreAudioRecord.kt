package com.ainirobot.agent.core

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.media.AudioDeviceInfo
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.AudioTimestamp
import android.os.Bundle
import com.ainirobot.agent.base.Transcription
import com.ainirobot.bi.RTQueryTrace
import com.ainirobot.common.DataStore
import com.ainirobot.common.audio.AudioWriter
import com.ainirobot.common.audio.ByteUtils
import com.ainirobot.common.audio.PCMAudioWriter
import com.ainirobot.common.debug.CaptionWindow
import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.common.utils.DataObserver
import com.ainirobot.agent.base.utils.DigitalUtil
import com.ainirobot.common.utils.IntervalPrinter
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.MessageSenderUtil
import com.ainirobot.common.utils.RobotMotionStatus
import com.ainirobot.speechasrservice.collection.VoiceDataCollection
import com.ainirobot.speechasrservice.collection.VoiceDataCollectionProxyCallback
import com.cm.speech.WakeupEngine
import com.cm.speech.asr.req.AudioResult
import com.cm.speech.config.Config
import com.cm.speech.config.ConfigManager
import com.cm.speech.constant.Constant
import com.cm.speech.recognize.TalkInitParams
import com.cm.speech.recognize.TalkJni
import com.cm.speech.util.CFun
import com.cm.speech.warpper.ASRManager
import livekit.org.webrtc.audio.IAudioRecord
import livekit.org.webrtc.audio.IAudioRecordFactory
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import java.nio.ByteBuffer
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.locks.ReentrantLock

class CustomAudioRecordFactory(
    private val context: Context,
): IAudioRecordFactory {

    @SuppressLint("MissingPermission")
    override fun create(
        audioSource: Int,
        sampleRate: Int,
        channelConfig: Int,
        audioFormat: Int,
        bufferSizeInBytes: Int
    ): IAudioRecord {
        KLog.d("CustomAudioRecordFactory create audioSource: $audioSource, sampleRate: $sampleRate, channelConfig: $channelConfig, audioFormat: $audioFormat, bufferSizeInBytes: $bufferSizeInBytes", "CoreAudioRecord")
        val impl = AudioRecord(
            audioSource,
            sampleRate,
            channelConfig,
            audioFormat,
            bufferSizeInBytes
        )
        val customAudioRecord = CoreAudioRecord(context, impl)
        return customAudioRecord
    }
}

class CoreAudioRecord(
    private val context: Context,
    private val audioRecord: AudioRecord
) : IAudioRecord, DataObserver {

    companion object {

        /**
         * 声道数量
         */
        private const val CHANNEL_NUM = 1

        private const val BUFFER_SIZE = CHANNEL_NUM * 320

        /**
         * 1秒的数据量
         * 采样率 x 位宽 x 采样时间 x 通道数
         */
        private const val FEEDBACK_BUFFER_SIZE = CHANNEL_NUM * (16 / 8) * 16000

        private const val AUDIO_SPERASE_LENGTH = 4

        /**
         * talk库返回状态 唤醒
         */
        private const val MSG_WAKE_UP: Int = 1

        /**
         * talk库返回状态 识别开始
         */
        private const val MSG_VAD_BEGIN: Int = 10

        /**
         * talk库返回状态 识别结束
         */
        private const val MSG_VAD_END: Int = 11

        init {
            System.loadLibrary("cmTalkAndroid")
        }

        private fun sendVadBegin(sid: String, frames: Int) {
            RobotConsole.print("发送Vad Begin Event, sid: $sid")
            KLog.d("Send Vad Begin Event, sid: $sid, frames: $frames", "CoreAudioRecord")
            MessageSender.sendMessage(
                CommonMessage(
                    name = "cmd",
                    content = mapOf(
                        "command" to "vad_start",
                        "kwargs" to mapOf(
                            "sid" to sid,
                            "frames" to frames
                        )
                    )
                ),
                false
            )
        }

        private fun sendVadEnd(sid: String) {
            RobotConsole.print("发送Vad End Event, sid: $sid")
            KLog.d("Send Vad End Event, sid: $sid", "CoreAudioRecord")
            MessageSender.sendMessage(
                CommonMessage(
                    name = "cmd",
                    content = mapOf(
                        "command" to "vad_end",
                        "kwargs" to mapOf("sid" to sid)
                    )
                ),
                false
            )
        }

        private fun sendWakeup(sid: String, wakeup: Boolean, delayTime: Long) {
            RobotConsole.print("发送角度识别结果, sid: $sid, valid: $wakeup")
            KLog.d("Send wakeup result, sid: $sid, wakeup: $wakeup", "CoreAudioRecord")
            MessageSender.sendMessage(
                CommonMessage(
                    name = "cmd",
                    content = mapOf(
                        "command" to "wakeup_result",
                        "kwargs" to mapOf(
                            "sid" to sid,
                            "result" to wakeup
                        )
                    )
                ),
                isSafety = false,
                delayTime = delayTime
            )
        }
    }

    @Volatile
    private var isVisionEnable = true

    private val readCFun = CFun(BUFFER_SIZE / 2)

    /**
     * 左路识别
     */
    private val leftChannel = ByteArray(BUFFER_SIZE / 2)

    /**
     * 右路唤醒
     */
    private val rightChannel = ByteArray(BUFFER_SIZE / 2)

    /**
     * 左路识别short长度 512short
     */
    private val leftShortArray = ShortArray(BUFFER_SIZE / 4)

    /**
     * 右路唤醒short长度 512short
     */
    private val rightShortArray = ShortArray(BUFFER_SIZE / 4)

    private val vadStatus = AtomicInteger(MSG_VAD_END)
    @Volatile
    private var sid = ""
    private val isValidVoice = AtomicBoolean(false)
    private val vadStateLock = ReentrantLock()

    private val buffer = ByteArray(BUFFER_SIZE)
    private val feedbackBuffer = ByteArray(FEEDBACK_BUFFER_SIZE)

    private val voiceDataCallback :VoiceDataCollectionProxyCallback   = object : VoiceDataCollectionProxyCallback {
        override fun filterVadData(sid: String?, filter: Boolean, speakId: Int) {
            if (sid != null) {
                onFilterVadData(sid, filter, speakId)
            }
        }
    }

    init {
        ConfigManager.getInstance().configStartUpParams(
            Bundle().apply {
                putString(
                    Constant.EXTRA_VAD_TYPE,
                    ASRManager.VadType.VAD_2.name
                )
                putString(
                    Constant.EXTRA_WAKE_UP_TYPE,
                    ASRManager.WakeupType.NONE.name
                )
                putInt(
                    Constant.EXTRA_VAD_END_INTERVAL,
                    450
                )
                putInt(
                    Constant.EXTRA_ONE_SENTENCE_TIMEOUT,
                    7000
                )
                putInt(
                    Constant.EXTRA_SILENT_TIME_OUT,
                    6000
                )
                putBoolean(
                    Constant.EXTRA_VOLUME_CHANGE,
                    true
                )
                putInt(
                    Constant.EXTRA_AUDI0_CHANNEL,
                    audioRecord.channelConfiguration
                )
            }
        )
        Config.tryToCopyAsrFile(context)
        val params = TalkInitParams(
            ConfigManager.getInstance()
                .getParam(
                    Constant.EXTRA_WAKEUP_LICENSE,
                    Config.LICENSE_RESOURCE_LOCAL_PATH
                ),
            ConfigManager.getInstance().getParam(
                Constant.EXTRA_WAKEUP_RES_FILE,
                Config.WAKEUP_RESOURCE_LOCAL_PATH
            ),
            ConfigManager.getInstance().getParam(
                Constant.EXTRA_WAKEUP_WORDS,
                Constant.DEFAULT_WAKEUP_WORD
            ),
            ConfigManager.getInstance().getParam(
                Constant.EXTRA_VAD_RESOURCE,
                Config.VAD_RESOURCE_LOCAL_PATH
            ),
            ConfigManager.getInstance().getIntParam(
                Constant.EXTRA_VAD_END_INTERVAL,
                Constant.DEFAULT_VAD_INTERVAL
            ),
            Constant.SAMPLE_RATE
        )
        KLog.d("init params: vadInterval=${params.vadInterval}, sampleRate=${params.sampleRate}", "CoreAudioRecord")
        TalkJni.init(context, params) { reason, code ->
            KLog.d("init failed, reason: $reason, code: $code", "CoreAudioRecord")
        }
        TalkJni.TalkEnableVad()

        VoiceDataCollection.getInstance()?.setProxyCallback(voiceDataCallback)

        EventBus.getDefault().register(this)
        MessageSenderUtil.registerObserver(this)
    }

    override fun getRecordingState(): Int {
        return audioRecord.recordingState
    }

    override fun read(audioBuffer: ByteBuffer, sizeInBytes: Int): Int {
        return audioRecord.read(audioBuffer, sizeInBytes)
    }

    override fun read(audioBuffer: ByteBuffer, sizeInBytes: Int, audioTimestamp: AudioTimestamp): Int {
        val readSize = audioRecord.read(buffer, 0, sizeInBytes)
        if (readSize <= 0) {
            return readSize
        }
        if (audioRecord.getTimestamp(
                audioTimestamp,
                AudioTimestamp.TIMEBASE_MONOTONIC
            ) != AudioRecord.SUCCESS
        ) {
            audioTimestamp.nanoTime = 0
        }
        PCMAudioWriter.write(buffer, readSize)
        val status = checkVad(buffer, readSize)
        if (isValidVoice.get()) {
            if (status != MSG_VAD_BEGIN) {
                AudioWriter.write(
                    buffer,
                    readSize,
                    audioRecord.channelConfiguration == AudioFormat.CHANNEL_IN_STEREO
                )
            }
        } else if (status == MSG_VAD_END) {
            AudioWriter.write(
                buffer,
                readSize,
                audioRecord.channelConfiguration == AudioFormat.CHANNEL_IN_STEREO
            )
            AudioWriter.stopRecord()
        }
        audioBuffer.clear()
        audioBuffer.put(buffer, 0, readSize)
        return readSize
    }

    /**
     * 计算VAD BEGIN延迟的帧数
     */
    private fun calculateVadBeginFrames(): Int {
        var byteCount = 0
        while (true) {
            val cn = TalkJni.getFeedbackData(feedbackBuffer, feedbackBuffer.size)
            KLog.d("readFeedback: $cn", "CoreAudioRecord")
            if (cn <= 0) {
                break
            }
            AudioWriter.write(feedbackBuffer, cn, false)
            byteCount += cn
        }
        return (byteCount / BUFFER_SIZE.toFloat() + 0.5f).toInt()
    }

    private fun onFilterVadData(sid: String, filter: Boolean, speakId: Int) {
        KLog.d("onFilterVadData speakId: $speakId, sid: $sid, filter: $filter", "CoreAudioRecord")
        if (filter) {
            RobotConsole.print("环境音或者讲话者($speakId)与其他人说话", Color.YELLOW)
            onSpeakBegin(sid, false)
        } else {
            RobotConsole.print("讲话者($speakId)与机器人说话")
            onSpeakBegin(sid, true)
        }
    }

    private fun onVadBegin() {
        KLog.d("onVad begin: ", "CoreAudioRecord")
        RobotConsole.print("Vad Begin")
        isValidVoice.set(true)
        sid = DigitalUtil.shortUUID()
        AudioWriter.startRecord(
            sid,
            audioRecord.sampleRate
        ).let { KLog.d("startRecord: $it", "CoreAudioRecord") }
        val frameCount = calculateVadBeginFrames()
        sendVadBegin(sid, frameCount)
        RTQueryTrace.reportVadBegin(sid, frameCount * 10)
        if (isVisionEnable && DataStore.isEnableWakeFree) {
            VoiceDataCollection.getInstance()?.onSpeechBegin(sid)
        } else {
            onSpeakBegin(sid, true, 100)
        }
    }

    private fun onVadEnd() {
        KLog.d("onVad end", "CoreAudioRecord")
        RobotConsole.print("Vad End")
        isValidVoice.set(false)
        onSpeakEnd()
        if (isVisionEnable && DataStore.isEnableWakeFree) {
            VoiceDataCollection.getInstance()?.onSpeechEnd(sid)
        }
    }

    private fun onSpeakBegin(
        sid: String,
        valid: Boolean,
        delayTime: Long = 0
    ) {
        sendWakeup(sid, valid, delayTime)
        CaptionWindow.onUserSpeakStart(sid, valid)
        if (valid) {
            RTQueryTrace.reportFaceAngle(sid)
        } else {
            RTQueryTrace.remove(sid)
        }
    }

    private fun onSpeakEnd() {
        sendVadEnd(sid)
        CaptionWindow.onUserSpeakEnd()
        RTQueryTrace.reportVadEnd(sid)
    }

    private val vadPrinter = IntervalPrinter("CoreAudioRecord", 10000)
    /**
     * 检查是否是有VAD事件，如果有才可以上传音频数据；
     */
    private fun checkVad(audioBuffer: ByteArray, readSize: Int): Int {
        if (vadStatus.get() == MSG_VAD_END && (DataStore.microphoneMuted)) {
            return 0
        }
        vadPrinter.d("CoreAudioRecord checkVad, channel config: ${audioRecord.channelConfiguration}")
        val audioResult = talkDecode(audioBuffer, readSize)
        val type: Int = audioResult.type
        if (type == AudioResult.TYPE_WAKEUP) {
            KLog.d("wakeup result", "CoreAudioRecord")
            vadPrinter.d("CoreAudioRecord WakeUp type, readSize: $readSize, vadResult.ret: ${audioResult.wakeupResult.ret}")
        } else if (type == AudioResult.TYPE_VAD) {
            val vadResult = audioResult.vadResult
            val ret = vadResult.ret
            vadPrinter.d("CoreAudioRecord VAD Type, readSize: $readSize, ret: $ret")
            synchronized(vadStateLock) {
                when (ret) {
                    MSG_VAD_BEGIN -> {
                        if (vadStatus.get() == MSG_VAD_END) {
                            vadStatus.set(MSG_VAD_BEGIN)
                            onVadBegin()
                            return MSG_VAD_BEGIN
                        }
                    }
                    MSG_VAD_END -> {
                        if (vadStatus.get() == MSG_VAD_BEGIN) {
                            vadStatus.set(MSG_VAD_END)
                            onVadEnd()
                            return MSG_VAD_END
                        }
                    }
                    else -> { }
                }
            }
        }
        return 0
    }

    private fun talkDecode(buffer: ByteArray, readCount: Int): AudioResult {
        if (audioRecord.channelConfiguration == AudioFormat.CHANNEL_IN_STEREO) {
            var leftCount = 0
            var rightCount = 0
            leftChannel.fill(0)
            rightChannel.fill(0)
            for (i in 0 until readCount step AUDIO_SPERASE_LENGTH) {
                leftChannel[leftCount++] = buffer[i]
                leftChannel[leftCount++] = buffer[i + 1]
                rightChannel[rightCount++] = buffer[i + 2]
                rightChannel[rightCount++] = buffer[i + 3]
            }
            ByteUtils.bytes2ShortsInJava(leftChannel, leftShortArray)
            ByteUtils.bytes2ShortsInJava(rightChannel, rightShortArray)
            return getAudioResult(leftShortArray, rightShortArray, readCount / 4)
        } else {
            val data: ShortArray = readCFun.byteToShortArray(buffer)
            return getAudioResult(data, data, data.size)
        }
    }

    private fun getAudioResult(
        leftShortArray: ShortArray,
        rightShortArray: ShortArray,
        length: Int
    ): AudioResult {
        val ret: Int
        val silenceCount = 0
        val wakeUpPos: Long = 0
        val wakeupSysDetect: Int
        return when (Constant.wakeupType) {
            ASRManager.WakeupType.WAVE_NET -> {
                wakeupSysDetect = WakeupEngine.WakeupSysDetect(
                    TalkJni.inst,
                    rightShortArray,
                    rightShortArray.size,
                    wakeUpPos,
                    0
                )
                if (wakeupSysDetect > 0) {
                    KLog.d("new wakeup model result $wakeupSysDetect, wakeUpPos: $wakeUpPos", "CoreAudioRecord")
                    AudioResult(AudioResult.TYPE_WAKEUP, AudioResult.WakeupResult(wakeupSysDetect))
                } else {
                    ret = TalkJni.TalkDecodeSilenceReport(leftShortArray, rightShortArray, length, 1, silenceCount)
                    AudioResult(AudioResult.TYPE_VAD, AudioResult.VadResult(ret, silenceCount))
                }
            }
            ASRManager.WakeupType.CNN_DNN -> {
                ret = TalkJni.TalkDecodeSilenceReport(leftShortArray, rightShortArray, length, 1, silenceCount)
                if (ret == MSG_WAKE_UP) {
                    AudioResult(AudioResult.TYPE_WAKEUP, AudioResult.WakeupResult(ret))
                } else {
                    AudioResult(AudioResult.TYPE_VAD, AudioResult.VadResult(ret, silenceCount))
                }
            }
            else -> {
                ret = TalkJni.TalkDecodeSilenceReport(leftShortArray, rightShortArray, length, 1, silenceCount)
                AudioResult(AudioResult.TYPE_VAD, AudioResult.VadResult(ret, silenceCount))
            }
        }
    }

    @Subscribe
    fun onAsrFinish(transcription: Transcription) {
        KLog.d("onAsrFinish: ${transcription.final}", "CoreAudioRecord")
        if (transcription.error.isNotEmpty()) {
            VoiceDataCollection.getInstance()?.onSpeechError(sid, 0, transcription.error)
        } else {
            VoiceDataCollection.getInstance()?.onSpeechResult(sid, transcription.text)
        }
    }

    override fun getTimestamp(p0: AudioTimestamp, p1: Int): Int {
        return audioRecord.getTimestamp(p0, p1)
    }

    override fun getAudioFormat(): Int {
        return audioRecord.audioFormat
    }

    override fun getFormat(): AudioFormat {
        return audioRecord.format
    }

    override fun getSampleRate(): Int {
        return audioRecord.sampleRate
    }

    override fun getAudioSource(): Int {
        return audioRecord.audioSource
    }

    override fun getChannelCount(): Int {
        return audioRecord.channelCount
    }

    override fun getState(): Int {
        return audioRecord.state
    }

    override fun getAudioSessionId(): Int {
        return audioRecord.audioSessionId
    }

    override fun getBufferSizeInFrames(): Int {
        return audioRecord.bufferSizeInFrames
    }

    override fun getRoutedDevice(): AudioDeviceInfo {
        return audioRecord.routedDevice
    }

    override fun setPreferredDevice(p0: AudioDeviceInfo?): Boolean {
        return audioRecord.setPreferredDevice(p0)
    }

    override fun startRecording() {
        audioRecord.startRecording()
    }

    override fun stop() {
        audioRecord.stop()
    }

    override fun release() {
        EventBus.getDefault().unregister(this)
        MessageSenderUtil.unregisterObserver(this)
        TalkJni.free()
        audioRecord.release()
        KLog.d("Release audioRecord", "CoreAudioRecord")
    }

    override fun onDataChanged(key: String, data: Any) {
        if (key == "robot_vision_enable") {
            isVisionEnable = data as Boolean
        } else if (key == "robot_motion_status") {
            val motionStatus = data as RobotMotionStatus
            KLog.d("onDataChanged robot_motion_status: motionType=${motionStatus.motionType}, motionStatus=${motionStatus.motionStatus}", "CoreAudioRecord")
            
            if (motionStatus.motionStatus == "start") {
                DataStore.isEnableWakeFree = false
                KLog.d("Disabled angle VAD during robot motion", "CoreAudioRecord")
            } else if (motionStatus.motionStatus == "finish") {
                DataStore.isEnableWakeFree = true
                KLog.d("Enabled angle VAD after robot motion completed", "CoreAudioRecord")
            }
        }
    }
}