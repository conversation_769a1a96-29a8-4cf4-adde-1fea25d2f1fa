package com.ainirobot.bi

import com.ainirobot.agent.core.RunningStatus
import com.ainirobot.common.Config

class RTPlanTrace : BaseReport("gb_agent_os_plan_trace") {

    /**
     * 服务端下发Plan的ActionConfig协议版本号
     */
    fun setVersion(version: String): RTPlanTrace {
        addData("action_version", version)
        return this
    }

    /**
     * 机器人本地的ActionConfig协议版本号
     */
    private fun setRobotActVer(): RTPlanTrace {
        addData("robot_act_ver", Config.actionVersion)
        return this
    }

    /**
     * plan下发时对应的消息id
     */
    fun setMsgId(msgId: String): RTPlanTrace {
        addData("msg_id", msgId)
        return this
    }

    /**
     * 唤醒id
     */
    private fun setWakeupId(wakeupId: String): RTPlanTrace {
        addData("wakeup_id", wakeupId)
        return this
    }

    /**
     * 用以定位音频文件
     */
    fun setQueryId(queryId: String): RTPlanTrace {
        addData("query_id", queryId)
        return this
    }

    /**
     * 用户说的话
     */
    fun setUserQuery(query: String): RTPlanTrace {
        addData("user_query", query)
        return this
    }

    /**
     * plan的id，可重复
     */
    fun setPlanId(planId: String): RTPlanTrace {
        addData("plan_id", planId)
        return this
    }

    /**
     * 一次完整的对话交互的运行id，不重复
     */
    fun setRunId(runId: String): RTPlanTrace {
        addData("run_id", runId)
        return this
    }

    /**
     * plan的类型
     */
    fun setPlanType(planType: String): RTPlanTrace {
        addData("plan_type", planType)
        return this
    }

    /**
     * plan的内容
     */
    fun setPlanContent(content: String): RTPlanTrace {
        addData("plan_xml", content)
        return this
    }

    /**
     * Action执行详情
     */
    fun setExecutionDetail(step: String): RTPlanTrace {
        addData("execution_detail", step)
        return this
    }

    /**
     * plan的生成或者执行状态，1成功，2失败，3超时, 4被同一action打断
     */
    fun setStatus(status: RunningStatus): RTPlanTrace {
        addData(
            "status",
            when (status) {
                RunningStatus.SUCCEEDED -> 1
                RunningStatus.FAILED -> 2
                RunningStatus.TIMEOUT -> 3
                RunningStatus.RECALLED -> 4
                else -> 0
            }
        )
        return this
    }

    /**
     * 接收到plan事件的时间
     */
    fun setReceivedTime(receivedTime: String): RTPlanTrace {
        addData("plan_received_time", receivedTime)
        return this
    }

    /**
     * plan开始执行的时间
     */
    fun setStartTime(startTime: String): RTPlanTrace {
        addData("plan_start_time", startTime)
        return this
    }

    /**
     * plan的执行耗时
     */
    fun setExecutionElapseTime(elapseTime: Int): RTPlanTrace {
        addData("execution_elapse_time", elapseTime)
        return this
    }

    /**
     * user_end_speech_timestamp
     * 记录用户停止说话的时间戳
     */
    fun setUserEndSpeechTimestamp(timestamp: Long): RTPlanTrace {
        addData("user_end_speech_timestamp", timestamp)
        return this
    }

    /**
     * final_asr
     * 用户说的最后一段话 的 ASR 耗时； 比如用户说： 我 。。。 啊。。 那个。。。
     * 今天天气怎么样。此时统计的是“今天天气怎么样”的ASR耗时
     */
    fun setFinalAsr(finalAsr: Int): RTPlanTrace {
        addData("final_asr", finalAsr)
        return this
    }

    /**
     * plan_total
     * 规划总用时（select_few_shot  + summary + agent）
     */
    fun setPlanTotal(planTotal: Int): RTPlanTrace {
        addData("plan_total", planTotal)
        return this
    }

    /**
     * select_few_shot
     * 动态选择few shot 用时
     */
    fun setSelectFewShot(selectFewShot: Int): RTPlanTrace {
        addData("select_few_shot", selectFewShot)
        return this
    }

    /**
     * summary: fewshot用时
     */
    fun setSummaryFewShot(summaryFewShot: Int): RTPlanTrace {
        addData("summary_few_shot", summaryFewShot)
        return this
    }

    /**
     * agent: 规划用时
     */
    fun setAgentElapse(agentElapse: Int): RTPlanTrace {
        addData("agent_elapse", agentElapse)
        return this
    }

    /**
     * 当前APK应用包名
     */
    fun setPackageName(packageName: String): RTPlanTrace {
        addData("cur_package_name", packageName)
        return this
    }

    /**
     * 当前OPK应用名
     */
    fun setAppName(appName: String): RTPlanTrace {
        addData("cur_app_name", appName)
        return this
    }

    /**
     * 当前OPK应用ID
     */
    fun setAppId(appId: String): RTPlanTrace {
        addData("cur_app_id", appId)
        return this
    }

    /**
     * 当前OPK页面ID
     */
    fun setPageId(appId: String): RTPlanTrace {
        addData("cur_page_id", appId)
        return this
    }

    /**
     * 其它描述消息，备用
     */
    fun setExtraMsg(extraMsg: String): RTPlanTrace {
        addData("extra_msg", extraMsg)
        return this
    }

    override fun report() {
        setRobotActVer()
        setWakeupId(Config.wakeupId)
        super.report()
    }
}