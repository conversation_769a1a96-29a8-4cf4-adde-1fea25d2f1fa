package com.ainirobot.bi

import com.ainirobot.common.Config
import com.ainirobot.common.utils.KLog

class RTQueryTrace : BaseReport("gb_agent_os_query_trace") {

    companion object {

        private val traces = mutableMapOf<String, RTQueryTrace>()

        private fun obtain(sid: String, remove: Boolean = false): RTQueryTrace? {
            synchronized(traces) {
                return if (remove) traces.remove(sid) else traces[sid]
            }
        }

        private fun clean() {
            synchronized(traces) {
                traces.entries.removeIf { !it.value.checkValid() }
            }
        }

        fun remove(sid: String) {
            synchronized(traces) {
                traces.remove(sid)
            }
        }

        fun reportVadBegin(sid: String, duration: Int) {
            val trace: RTQueryTrace
            synchronized(traces) {
                trace = traces[sid] ?: RTQueryTrace().also { traces[sid] = it }
            }
            trace.setVadSid(sid)
                .setVadBegin(System.currentTimeMillis())
                .setSpeakBegin(duration)
        }

        fun reportFaceAngle(sid: String) {
            obtain(sid)?.setFaceAngle(System.currentTimeMillis())
        }

        fun reportVadEnd(sid: String) {
            obtain(sid)?.setVadEnd(System.currentTimeMillis())
        }

        fun reportCaptionReceived(sid: String) {
            obtain(sid)?.setCaptionReceived(System.currentTimeMillis())
        }

        fun reportPlanReceived(queryId: String) {
            val index = queryId.lastIndexOf('_')
            val sid = if (index == -1) queryId else queryId.substring(index + 1)
            obtain(sid, true)
                ?.setQueryId(queryId)
                ?.setPlanReceived(System.currentTimeMillis())
                ?.setWakeupId(Config.wakeupId)
                ?.report()
            clean()
            KLog.d("query trace slices length is ${traces.size}, sid: $sid", "RTQueryTrace")
        }
    }

    private val createTime = System.currentTimeMillis()

    private fun checkValid(): Boolean {
        return System.currentTimeMillis() - createTime < 60000
    }

    /**
     * vad slice sid
     */
    fun setVadSid(sid: String): RTQueryTrace {
        KLog.d("sid: $sid", "RTQueryTrace")
        addData("vad_sid", sid)
        return this
    }

    /**
     * user speak - vad begin
     */
    fun setSpeakBegin(duration: Int): RTQueryTrace {
        KLog.d("speak_begin_duration: $duration", "RTQueryTrace")
        addData("speak_begin_duration", duration)
        return this
    }

    /**
     * vad begin
     */
    fun setVadBegin(timestamp: Long): RTQueryTrace {
        KLog.d("vad_begin: $timestamp", "RTQueryTrace")
        addData("vad_begin", timestamp)
        return this
    }

    /**
     * face angle
     */
    fun setFaceAngle(timestamp: Long): RTQueryTrace {
        KLog.d("face_angle: $timestamp", "RTQueryTrace")
        addData("face_angle", timestamp)
        return this
    }

    /**
     * vad end
     */
    fun setVadEnd(timestamp: Long): RTQueryTrace {
        KLog.d("vad_end: $timestamp", "RTQueryTrace")
        addData("vad_end", timestamp)
        return this
    }

    /**
     * 唤醒id
     */
    fun setWakeupId(wakeupId: String): RTQueryTrace {
        KLog.d("wakeup: $wakeupId", "RTQueryTrace")
        addData("wakeup_id", wakeupId)
        return this
    }

    /**
     * query id
     */
    fun setQueryId(queryId: String): RTQueryTrace {
        KLog.d("query_id: $queryId", "RTQueryTrace")
        addData("query_id", queryId)
        return this
    }

    /**
     * 接收到caption事件的时间
     */
    fun setCaptionReceived(timestamp: Long): RTQueryTrace {
        KLog.d("caption_received: $timestamp", "RTQueryTrace")
        addData("caption_received", timestamp)
        return this
    }

    /**
     * 接收到plan事件的时间
     */
    fun setPlanReceived(timestamp: Long): RTQueryTrace {
        KLog.d("plan_received: $timestamp", "RTQueryTrace")
        addData("plan_received", timestamp)
        return this
    }
}