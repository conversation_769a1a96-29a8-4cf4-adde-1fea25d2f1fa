package com.ainirobot.bi

import com.ainirobot.agent.BuildConfig
import com.ainirobot.common.debug.Env
import com.ainirobot.coreservice.client.upload.bi.BaseBiReport

abstract class BaseReport(tableName: String) : BaseBiReport(tableName) {

    /**
     * 注意：此方法内的字段不在公共字段表中，所以在申请表时也要添加；
     */
    private fun onReportBefore() {
        // Agent环境，0正式，1开发，2灰度
        addData("agent_env", Env.agentEnv.toByte())
        // App版本
        addData("app_version", BuildConfig.VERSION_NAME)
    }

    override fun report() {
        onReportBefore()
        super.report()
    }
}
