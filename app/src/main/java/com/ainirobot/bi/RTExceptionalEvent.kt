package com.ainirobot.bi

class RTExceptionalEvent : BaseReport("gb_agent_os_exceptional_event") {

    init {
        setException(null)
        setMessage("")
        setLevel(0)
    }

    /**
     * 事件类型，0特殊事件，1异常，2错误，3警告
     */
    fun setType(type: Byte): RTExceptionalEvent {
        addData("event_type", type)
        return this
    }

    /**
     * 事件标签，用于区分不同的事件
     */
    fun setTag(type: String): RTExceptionalEvent {
        addData("event_tag", type)
        return this
    }

    /**
     * 事件等级，0普通，1重要，2紧急
     */
    fun setLevel(level: Int): RTExceptionalEvent {
        addData("event_level", level)
        return this
    }

    /**
     * 事件描述
     */
    fun setMessage(message: String): RTExceptionalEvent {
        addData("message", message)
        return this
    }

    /**
     * 异常堆栈信息
     */
    fun setException(exception: Throwable? = null): RTExceptionalEvent {
        if (exception == null) {
            addData("exception", "")
        } else {
            addData("exception", exception.stackTraceToString())
        }
        return this
    }
}