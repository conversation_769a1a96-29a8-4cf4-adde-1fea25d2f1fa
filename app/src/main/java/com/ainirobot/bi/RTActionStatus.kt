package com.ainirobot.bi

import com.ainirobot.agent.core.RunningStatus

class RTActionStatus : BaseReport("gb_agent_os_action_status") {

    /**
     * plan的id，可重复
     */
    fun setPlanId(planId: String): RTActionStatus {
        addData("plan_id", planId)
        return this
    }

    /**
     * 一次完整的对话交互的运行id，不重复
     */
    fun setRunId(runId: String): RTActionStatus {
        addData("run_id", runId)
        return this
    }

    /**
     * Action事件的唯一标识
     */
    fun setActionName(name: String): RTActionStatus {
        addData("action_name", name)
        return this
    }

    /**
     * Action事件的Display Name
     */
    fun setDisplayName(name: String): RTActionStatus {
        addData("display_name", name)
        return this
    }

    /**
     * Action事件的id，不同的Plan可重复
     */
    fun setActionId(actionId: String): RTActionStatus {
        addData("action_id", actionId)
        return this
    }

    /**
     * action操作，1开始，2结束
     */
    fun setOption(option: Byte): RTActionStatus {
        addData("option", option)
        return this
    }

    /**
     * action的执行状态，1成功，2失败，3超时，4被同一action打断
     */
    fun setStatus(status: RunningStatus): RTActionStatus {
        addData(
            "status",
            when (status) {
                RunningStatus.SUCCEEDED -> 1
                RunningStatus.FAILED -> 2
                RunningStatus.TIMEOUT -> 3
                RunningStatus.RECALLED -> 4
                else -> 0
            }
        )
        return this
    }

    /**
     * action的执行耗时
     */
    fun setElapsedTime(elapsedTime: Int): RTActionStatus {
        addData("elapsed_time", elapsedTime)
        return this
    }

    /**
     * action的执行结果信息
     */
    fun setMessage(message: String): RTActionStatus {
        addData("message", message)
        return this
    }
}