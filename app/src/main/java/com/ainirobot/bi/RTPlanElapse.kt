package com.ainirobot.bi

class RTPlanElapse : BaseReport("gb_agent_os_plan_elapse") {

    companion object {

        private val fields = setOf(
            "vad_start_timestamp",
            "vad_end_timestamp",
            "asr_end_timestamp",
            "asr_cost_time",
            "wakeup_cost_time",
            "answer_start_timestamp",
            "plan_start_timestamp",
            "plan_end_timestamp",
            "plan_total_cost_time",
            "total_cost_time",
            "agent_core_load_context_time",
            "agent_core_load_action_time",
            "agent_core_embedded_time",
            "agent_core_call_summary_llm_time",
            "agent_core_select_few_shot_time",
            "agent_core_call_select_action_llm_time",
            "agent_core_load_user_profile_time",
            "agent_core_total_time",
            "agent_core_end_timestamp",
            "agent_core_start_timestamp",
            "agent_core_llm_retry_count"
        )

        private fun contains(field: String): Boolean {
            return fields.contains(field)
        }
    }

    /**
     * 用户QueryId
     */
    fun setQueryId(queryId: String): RTPlanElapse {
        addData("query_id", queryId)
        return this
    }

    /**
     * 用户说的话
     */
    fun setUserQuery(query: String): RTPlanElapse {
        addData("user_query", query)
        return this
    }

    /**
     * 一次完整的对话交互的运行id，不重复
     */
    fun setRunId(runId: String): RTPlanElapse {
        addData("run_id", runId)
        return this
    }

    /**
     * 接收到plan事件的时间
     */
    fun setReceivedTime(receivedTime: String): RTPlanElapse {
        addData("plan_received_time", receivedTime)
        return this
    }

    /**
     * plan开始执行的时间
     */
    fun setStartTime(startTime: String): RTPlanElapse {
        addData("plan_start_time", startTime)
        return this
    }

    /**
     * plan的执行耗时
     */
    fun setExecutionElapseTime(elapseTime: Int): RTPlanElapse {
        addData("execution_elapse_time", elapseTime)
        return this
    }

    fun setField(field: String, value: String): RTPlanElapse {
        if (contains(field)) {
            addData(field, value)
        }
        return this
    }

    fun setField(field: String, value: Int): RTPlanElapse {
        if (contains(field)) {
            addData(field, value)
        }
        return this
    }
}