package com.ainirobot.bi

import com.ainirobot.agent.core.RunningStatus
import com.ainirobot.common.Config

class RTPlanStatus : BaseReport("gb_agent_os_plan_status") {

    /**
     * 服务端下发Plan的ActionConfig协议版本号
     */
    fun setVersion(version: String): RTPlanStatus {
        addData("action_version", version)
        return this
    }

    /**
     * 机器人本地的ActionConfig协议版本号
     */
    private fun setRobotActVer(): RTPlanStatus {
        addData("robot_act_ver", Config.actionVersion)
        return this
    }

    /**
     * plan下发时对应的消息id
     */
    fun setMsgId(msgId: String): RTPlanStatus {
        addData("msg_id", msgId)
        return this
    }

    /**
     * 用以定位音频文件
     */
    fun setQueryId(queryId: String): RTPlanStatus {
        addData("query_id", queryId)
        return this
    }

    /**
     * 用户说的话
     */
    fun setUserQuery(query: String): RTPlanStatus {
        addData("user_query", query)
        return this
    }

    /**
     * plan的id，可重复
     */
    fun setPlanId(planId: String): RTPlanStatus {
        addData("plan_id", planId)
        return this
    }

    /**
     * 一次完整的对话交互的运行id，不重复
     */
    fun setRunId(runId: String): RTPlanStatus {
        addData("run_id", runId)
        return this
    }

    /**
     * 当前plan事件的目标操作, 1开始执行，2执行完成，3显示
     */
    fun setOption(option: Byte): RTPlanStatus {
        addData("option", option)
        return this
    }

    /**
     * plan的生成或者执行状态，1成功，2失败，3超时, 4被同一action打断
     */
    fun setStatus(status: RunningStatus): RTPlanStatus {
        addData(
            "status",
            when (status) {
                RunningStatus.SUCCEEDED -> 1
                RunningStatus.FAILED -> 2
                RunningStatus.TIMEOUT -> 3
                RunningStatus.RECALLED -> 4
                else -> 0
            }
        )
        return this
    }

    /**
     * plan的类型
     */
    fun setPlanType(planType: String): RTPlanStatus {
        addData("plan_type", planType)
        return this
    }

    /**
     * plan的执行耗时
     */
    fun setExecutionElapseTime(elapseTime: Int): RTPlanStatus {
        addData("execution_elapse_time", elapseTime)
        return this
    }

    /**
     * user_end_speech_timestamp
     * 记录用户停止说话的时间戳
     */
    fun setUserEndSpeechTimestamp(timestamp: Long): RTPlanStatus {
        addData("user_end_speech_timestamp", timestamp)
        return this
    }

    /**
     * final_asr
     * 用户说的最后一段话 的 ASR 耗时； 比如用户说： 我 。。。 啊。。 那个。。。
     * 今天天气怎么样。此时统计的是“今天天气怎么样”的ASR耗时
     */
    fun setFinalAsr(finalAsr: Int): RTPlanStatus {
        addData("final_asr", finalAsr)
        return this
    }

    /**
     * plan_total
     * 规划用时
     */
    fun setPlanTotal(planTotal: Int): RTPlanStatus {
        addData("plan_total", planTotal)
        return this
    }

    /**
     * select_few_shot
     * 动态选择few shot 用时
     */
    fun setSelectFewShot(selectFewShot: Int): RTPlanStatus {
        addData("select_few_shot", selectFewShot)
        return this
    }

    /**
     * 其它描述消息，备用
     */
    fun setExtraMsg(extraMsg: String): RTPlanStatus {
        addData("extra_msg", extraMsg)
        return this
    }

    override fun report() {
        setRobotActVer()
        super.report()
    }
}