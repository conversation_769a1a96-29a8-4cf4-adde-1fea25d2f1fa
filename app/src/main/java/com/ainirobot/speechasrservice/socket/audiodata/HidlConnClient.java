package com.ainirobot.speechasrservice.socket.audiodata;

import android.util.Log;

import com.cm.speech.asr.AwakeAngleJni;

public class HidlConnClient extends ConnClient {

    private static final String TAG = HidlConnClient.class.getSimpleName();
    private int fd = -1;

    @Override
    protected void stopConnection() {
        int isRelease = AwakeAngleJni.release(fd);
        Log.i(TAG, "stopConnection isRelease:" + isRelease);
    }

    @Override
    protected int readMsg(byte[] btMsgHeadRecv) {
        return AwakeAngleJni.receive(fd, btMsgHeadRecv);
    }

    @Override
    protected boolean writeMsg(byte[] btMsgHeadSend) {
        int sendStatus = AwakeAngleJni.send(fd, btMsgHeadSend);
        return sendStatus > 0;
    }

    @Override
    protected boolean initConnection() {
        fd = AwakeAngleJni.init(AwakeAngleJni.SOCKET_PCM_DUMP);
        Log.i(TAG, "end connect,isInit:" + fd);
        return fd > 0;
    }
}
