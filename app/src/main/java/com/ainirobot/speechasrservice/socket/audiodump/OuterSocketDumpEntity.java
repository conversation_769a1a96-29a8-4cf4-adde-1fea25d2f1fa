package com.ainirobot.speechasrservice.socket.audiodump;

import android.util.Log;

import com.ainirobot.speechasrservice.socket.AudioDumpManager;
import com.ainirobot.speechasrservice.socket.audioinfo.SocketConnectStatus;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;


/**
 * <AUTHOR>
 */
public class OuterSocketDumpEntity extends AudioDumpEntity {

    public static final String TAG = OuterSocketDumpEntity.class.getSimpleName();
    private static final int PORT = 7789;
    private ServerSocket serverSocket;
    private OutputStream mOutputStream;
    private InputStream mInputStream;
    private boolean isSocketRunning;

    public OuterSocketDumpEntity(AudioDumpManager.SocketCallBack socketCallBack) {
        super(socketCallBack);
    }


    @Override
    protected boolean readBodyData() {
//        try {
//            int read = mInputStream.read(readHeadBuffer);
//            if (read == readHeadBuffer.length) {
//                Log.i(TAG,
//                        String.format("threadName = %s,audio_wrapper head = %s",
//                                Thread.currentThread().getName(), Arrays.toString(readHeadBuffer)));
//                ByteBuffer buffer = ByteBuffer.wrap(readHeadBuffer);
//                buffer.order(ByteOrder.nativeOrder());
//                buffer.clear();
//                short msg_type = buffer.getShort();
//                //判断是否是底层返回数据
//                if (msg_type == RESPONSE_MSG_TYPE) {
//                    //判断调用者函数名，好像没啥用
//                    short req_type = buffer.getShort();
//                    int seq_number = buffer.getInt();
//                    //目前错误没有设计
//                    int ret_code = buffer.getInt();
//                    int body_len = buffer.getInt();
//                    byte[] body = new byte[body_len];
//                    //body_len==0，无消息体，>0有消息体，需要返回给调用者
//                    int bodyReaded = mInputStream.read(body);
//                    if (bodyReaded == body_len) {
//                        responseData(seq_number, ret_code, body);
//                    } else {
//                        Log.e(TAG,
//                                String.format("audio_wrapper bodyReaded = %s ,body_len = %s",
//                                        bodyReaded, body_len));
//                    }
//                    return true;
//                }
//
//            } else {
//                Log.e(TAG, "read socket length is not right ,readed:" + read);
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
        return false;
    }

    protected SocketConnectStatus connectSocket() {
        if (isConnect()) {
            Log.i(TAG, "serverSocket has started");
            return SocketConnectStatus.CONNECTED;
        }

        try {
            if (null == serverSocket || !serverSocket.isBound()) {
                serverSocket = new ServerSocket(PORT);
                service.execute(new Runnable() {
                    @Override
                    public void run() {
                        acceptSocketMsg();
                    }
                });
            }
            return SocketConnectStatus.CONNECTING;
        } catch (IOException e) {
            e.printStackTrace();
            return SocketConnectStatus.OFFLINE;
        }
    }

    private void acceptSocketMsg() {
        try {
            isSocketRunning = true;
            while (isSocketRunning) {
                Log.i(TAG, "ready to accept socket connection");
                Socket socket = serverSocket.accept();
                Log.i(TAG, "accept a new socket connection");
                close();
                mOutputStream = socket.getOutputStream();
                mInputStream = socket.getInputStream();
                setSocketStatus(SocketConnectStatus.CONNECTED);
                onConnected();
            }
            Log.d(TAG, "Socket has interrupted");
        } catch (IOException e) {
            Log.e(TAG, e.toString());
        }
    }


    protected void close() {
        try {
            if (null != mOutputStream) {
                mOutputStream.close();
                mOutputStream = null;
            }
            if (null != mInputStream) {
                mInputStream.close();
                mInputStream = null;
            }
        } catch (IOException e) {
            Log.e(TAG, e.getMessage());
        }
    }

    @Override
    protected boolean writeData(byte[] array) {
        try {
            if (null != mOutputStream) {
                mOutputStream.write(array);
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void release() {
        super.release();
        isSocketRunning = false;
        if (null != serverSocket) {
            try {
                serverSocket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            serverSocket = null;
        }
    }
}
