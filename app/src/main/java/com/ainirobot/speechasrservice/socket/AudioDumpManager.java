package com.ainirobot.speechasrservice.socket;

import android.util.Log;

import com.ainirobot.speechasrservice.kratos.cofig.ClientConfig;
import com.ainirobot.speechasrservice.socket.audiodump.AudioDumpEntity;
import com.ainirobot.speechasrservice.socket.audiodump.AudioDumpService;
import com.ainirobot.speechasrservice.socket.audioinfo.SocketConnectStatus;
import com.ainirobot.speechasrservice.utils.LogMsg;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

public class AudioDumpManager {

    private static final String TAG = AudioDumpManager.class.getSimpleName();
    private static final long DUMP_INTERVAL = 10 * 1000;

    private AudioDumpService mAudioDumpService;
    private ExecutorService service;
    public static final int TYPE_WAKEUP = 1;
    public static final int TYPE_SUSPECT_WAKEUP = 2;
    public static final int TYPE_ASR = 3;
    private long lastDumpTime;

    public boolean isSocketReady() {
        return mAudioDumpService.isSocketReady();
    }

    private static final class Instance {
        private static final AudioDumpManager INSTANCE = new AudioDumpManager();
    }

    public static AudioDumpManager getInstance() {
        return Instance.INSTANCE;
    }

    private AudioDumpManager() {
        mAudioDumpService = new AudioDumpService();
        service = Executors.newSingleThreadExecutor(new ThreadFactory() {
            private final AtomicInteger mCount = new AtomicInteger(1);

            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r, "audioDump-" + mCount.getAndIncrement());
            }
        });
    }

    /**
     * 定位功能相关socket连接, 小豹使用默认的
     */
    public final void init() {
        try {
            mAudioDumpService.bind(ClientConfig.SOCKETADDRESS);
        } catch (Exception e) {
            Log.w(TAG, e);
        }

    }

    /**
     * 写入一段数据到socket中，robot需要使用
     *
     * @param reqType  请求的type值
     * @param dataInfo 写入的数据
     */
    private void dumpAudioData(int reqType, byte[] dataInfo,
                               AudioDumpEntity.ResponseCallBack responseCallBack) {
        if (isSocketReady()) {
            mAudioDumpService.writeData2Socket(reqType, dataInfo, responseCallBack);
        } else {
            LogMsg.print(TAG, "dump audio socket is not ready");
        }
    }




    /**
     * 释放相关的资源
     */
    public void release() {
        if (mAudioDumpService != null) {
            mAudioDumpService.release();
        }
    }

    public interface SocketCallBack {

        /**
         * socket连接或者断开回调
         */
        void onSocketStatusChanged(SocketConnectStatus status);
    }

}
