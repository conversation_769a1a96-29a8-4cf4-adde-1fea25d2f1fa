package com.ainirobot.speechasrservice.socket.audiodata;

public class PcmSockProtoHead {
    public short msg_type;
    public short req_type;
    public int seq_number;
    public int ret_code;
    public int body_len;

    public PcmSockProtoHead() {
    }

    @Override
    public String toString() {
        return "PcmSockProtoHead{" +
                "msg_type=" + msg_type +
                ", req_type=" + req_type +
                ", seq_number=" + seq_number +
                ", ret_code=" + ret_code +
                ", body_len=" + body_len +
                '}';
    }
}
