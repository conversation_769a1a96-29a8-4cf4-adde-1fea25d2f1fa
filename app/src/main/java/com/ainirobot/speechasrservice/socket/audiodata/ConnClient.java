package com.ainirobot.speechasrservice.socket.audiodata;

import android.util.Log;

public abstract class ConnClient {
    private static final String TAG = ConnClient.class.getSimpleName();
    public static final int SERR_SERVER_INIT_FAILED = -101;
    public static final int SERR_SOCKET_ERROR = -102;
    public static final int SERR_OTHER_ERR = -103;
    protected static final int INIT_FUF_LEN = 1536;

    private ClientCallback mCallback;
    private Thread mThreadSocket;
    private boolean mThreadFlag;

    public int startClient(ClientCallback callback) {
        if (mThreadFlag) {
            Log.e(TAG, "the client already runing");
            return -1;
        } else {
            mThreadFlag = true;
            mCallback = callback;
            startSocket();
            return 0;
        }
    }

    private void startSocket() {
        this.mThreadSocket = new Thread(new Runnable() {
            public void run() {
                try {
                    boolean initSocket = initConnection();
                    if (!initSocket) {
                        Log.e(TAG, "create socket failed");
                        if (mCallback != null) {
                            mCallback.onClientEnded(SERR_SOCKET_ERROR, "create socket failed");
                        }
                        stopClient();
                        return;
                    }

                    int bufferSize = INIT_FUF_LEN;
                    byte[] pcmBytes = null;

                    Log.i(TAG, "this will to send data");
                    int iReadErrTimes = 0;
                    boolean isStarted = false;
                    int seqNumber = 1;


                    int iErrCode = 0;
                    String errStr = "no error";

                    while (mThreadFlag) {
                        byte[] btMsgHeadSend;
                        if (isStarted) {
                            btMsgHeadSend = PcmSockProtoUtils.buildMsgHead(
                                    PcmSockProtoUtils.REQ_TYPE_DATA, seqNumber++, 0,
                                    0);
                        } else {
                            btMsgHeadSend = PcmSockProtoUtils.buildMsgHead(
                                    PcmSockProtoUtils.REQ_TYPE_START, seqNumber++, 0,
                                    0);
                        }
                        boolean write = writeMsg(btMsgHeadSend);
                        if (!write) {
                            return;
                        }
                        byte[] btMsgHeadRecv = new byte[PcmSockProtoUtils.LEN_HEAD];

                        int readed = readMsg(btMsgHeadRecv);

                        if (readed >= PcmSockProtoUtils.LEN_HEAD) {
                            PcmSockProtoHead head = PcmSockProtoUtils.parasMsgHead(btMsgHeadRecv);
                            if (head.req_type == PcmSockProtoUtils.REQ_TYPE_START) {
                                Log.i(TAG, head.toString());
                                if (head.ret_code != 0) {
                                    Log.e(TAG, "audio server start failed," + head.toString());
                                    iErrCode = SERR_SERVER_INIT_FAILED;
                                    errStr = "audio server init failed";
                                    break;
                                }

                                byte[] btMsgBodyStart = new byte[head.body_len];
                                readed = readMsg(btMsgBodyStart);

                                if (readed >= head.body_len) {
                                    isStarted = true;
                                    AudioDumpInfo mPcmParam = PcmSockProtoUtils.parasMsgStartBody(btMsgBodyStart);
                                    if (mPcmParam.frameSize > 0) {
                                        bufferSize = mPcmParam.frameSize;
                                    } else {
                                        bufferSize = (mPcmParam.micChannelCount + mPcmParam.echoChannelCount + mPcmParam.outChannelCount) * mPcmParam.format * mPcmParam.frameCount;
                                    }
                                    pcmBytes = new byte[bufferSize];
                                    Log.i(TAG, mPcmParam.toString() + ", per pkg len:" + bufferSize);
                                    if (mCallback != null) {
                                        mCallback.onPcmParamIn(mPcmParam);                                    }
                                } else {
                                    Log.e(TAG, "read start body failed, ret:" + readed);
                                }
                            } else if (head.req_type == PcmSockProtoUtils.REQ_TYPE_DATA) {
                                readed = readMsg(pcmBytes);
                                if (readed >= bufferSize) {
                                    iReadErrTimes = 0;
                                    if (mCallback != null) {
                                        mCallback.onPcmFrameDataIn(pcmBytes, readed);
                                    }
                                } else {
                                    Log.e(TAG, "read pcm data failed, ret:" + readed);
                                }
                            } else {
                                Log.e(TAG, "msg head type is:" + head.req_type);
                            }

                        } else if (readed < 0) {
                            ++iReadErrTimes;
                            Log.w(TAG, "recv from servcer len < 0:" + readed);
                            if (iReadErrTimes >= 10) {
                                Log.e(TAG, "there read data err times > 10, socket server closed maybe, close client");
                                iErrCode = SERR_SOCKET_ERROR;
                                errStr = "socket server disconned";
                                break;
                            }
                        }
                    }
                    boolean write = writeMsg(PcmSockProtoUtils.buildMsgHead(PcmSockProtoUtils.REQ_TYPE_STOP,
                            seqNumber++, 0, 0));
                    stopConnection();
                    if (mCallback != null) {
                        mCallback.onClientEnded(iErrCode, errStr);
                    }
                    Log.e(TAG, "socket client thread exit");
                } catch (Exception var23) {
                    var23.printStackTrace();
                    if (mCallback != null) {
                        mCallback.onClientEnded(SERR_OTHER_ERR, "other error");
                    }
                }
                stopClient();
            }
        }, "socketClient");
        this.mThreadSocket.start();
    }

    public void stopClient() {
        this.mThreadFlag = false;
        try {
            if (this.mThreadSocket != null) {
                this.mThreadSocket.join(50L);
                this.mThreadSocket = null;
            }
        } catch (Exception var2) {
            var2.printStackTrace();
        }
    }

    protected abstract void stopConnection();

    protected abstract int readMsg(byte[] btMsgHeadRecv);

    protected abstract boolean writeMsg(byte[] btMsgHeadSend);

    protected abstract boolean initConnection();

}
