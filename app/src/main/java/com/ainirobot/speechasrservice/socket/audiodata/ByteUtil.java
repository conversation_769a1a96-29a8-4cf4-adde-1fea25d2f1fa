//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.ainirobot.speechasrservice.socket.audiodata;

public class ByteUtil {
    public ByteUtil() {
    }

    public static void putShort(byte[] b, short s, int index) {
        b[index + 1] = (byte)(s >> 8);
        b[index + 0] = (byte)(s >> 0);
    }

    public static short getShort(byte[] b, int index) {
        return (short)(b[index + 1] << 8 | b[index + 0] & 255);
    }

    public static void putInt(byte[] bb, int x, int index) {
        bb[index + 3] = (byte)(x >> 24);
        bb[index + 2] = (byte)(x >> 16);
        bb[index + 1] = (byte)(x >> 8);
        bb[index + 0] = (byte)(x >> 0);
    }

    public static int getInt(byte[] bb, int index) {
        return (bb[index + 3] & 255) << 24 | (bb[index + 2] & 255) << 16 | (bb[index + 1] & 255) << 8 | (bb[index + 0] & 255) << 0;
    }

}
