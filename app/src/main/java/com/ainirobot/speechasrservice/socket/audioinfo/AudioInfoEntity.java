package com.ainirobot.speechasrservice.socket.audioinfo;

import android.text.TextUtils;
import android.util.SparseArray;

import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig;
import com.ainirobot.speechasrservice.socket.AudioDumpManager;
import com.ainirobot.speechasrservice.socket.AudioInfoManager;
import com.ainirobot.speechasrservice.socket.SocketResponseEntity;
import com.ainirobot.speechasrservice.utils.LogMsg;
import com.ainirobot.speechasrservice.utils.SpeechConfig;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;
import java.util.Random;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public abstract class AudioInfoEntity {
    public static final String TAG = AudioInfoEntity.class.getSimpleName();
    private static final int REQ_ERROR_NUM = 0;
    private static final short REQUEST_MSG_TYPE = 1;
    protected static final short RESPONSE_MSG_TYPE = 2;
    protected static final int HEAD_LENGTH = 16;
    protected static final byte readHeadBuffer[] = new byte[HEAD_LENGTH];
    /**
     * 如果底层返回过慢或者线程调用较多时，该值过小会丢失数据。过大比较消耗内存。该值为经验值
     */
    private static final int MAXSIZE = 50;
    private final int SOCKET_READ_TIMEOUT = 100;
    private SparseArray<RequestEntity> requestEntitySparseArray = new SparseArray<>();
    private volatile SocketConnectStatus connectStatus;

    protected final ExecutorService service;
    private AudioInfoManager.SocketCallBack socketCallBack;
    protected static long DEFALUT_DELAY_TIME = 3000;

    public AudioInfoEntity(AudioInfoManager.SocketCallBack socketCallBack) {
        this.socketCallBack = socketCallBack;
        service = Executors.newFixedThreadPool(2, new ThreadFactory() {
            private final AtomicInteger mCount = new AtomicInteger(1);

            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "asr-connectROM-" + mCount.getAndIncrement());
                t.setPriority(Thread.MAX_PRIORITY);
                android.os.Process.setThreadPriority(-19);
                return t;
            }
        });
    }

    protected synchronized void setSocketStatus(SocketConnectStatus socketStatus) {
        connectStatus = socketStatus;
        LogMsg.print(TAG, "setSocketStatus:" + connectStatus);
        if (null != socketCallBack) {
            socketCallBack.onSocketStatusChanged(connectStatus);
        } else {
            LogMsg.print(TAG, "socketCallBack is null,can't return socket status");
        }
    }

    //输入标准数据格式
    public Future<SocketResponseEntity> writeData2Socket(final int reqType, final byte[] dataInfo, int outTime) {
        LogMsg.print(TAG, "writeData2Socket-->reqType:" + reqType);
        if (!isConnect()) {
            LogMsg.print(TAG, "writeData2Socket-->socket is not ready");
            return null;
        }
        return service.submit(() -> {
            final SocketResponseEntity[] socketResponseEntity = {null};
            CountDownLatch countDownLatch = new CountDownLatch(1);
            int reqNum = new Random().nextInt();

            setCallBack(reqNum, countDownLatch, (errorNum, responseData) -> {
                LogMsg.print(TAG, "errorNum:" + errorNum + ",responseData:" + Arrays.toString(responseData));
                socketResponseEntity[0] = new SocketResponseEntity(errorNum, responseData);
            });
            if (writeToSocket(REQUEST_MSG_TYPE, (short) reqType, reqNum, REQ_ERROR_NUM, dataInfo)) {
                try {
                    if (outTime == 0){
                        LogMsg.print(TAG, "countDownLatch.await 100");
                        countDownLatch.await(SOCKET_READ_TIMEOUT, TimeUnit.MILLISECONDS);
                    }else {
                        LogMsg.print(TAG, "countDownLatch.await " + outTime);
                        countDownLatch.await(outTime, TimeUnit.MILLISECONDS);
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                return socketResponseEntity[0];
            }
            return null;
        });
    }

    private boolean writeToSocket(short requestMsgType, short reqType, int reqNum, int errorNum,
                                  byte[] dataInfo) {
        if (isConnect()) {
            int length = dataInfo.length;
            ByteBuffer byteBuffer = ByteBuffer.allocate(HEAD_LENGTH + length);
            byteBuffer.order(ByteOrder.nativeOrder());
            byteBuffer.putShort(requestMsgType);
            byteBuffer.putShort(reqType);
            byteBuffer.putInt(reqNum);
            byteBuffer.putInt(errorNum);
            byteBuffer.putInt(length);
            byteBuffer.put(dataInfo);
            LogMsg.print(TAG, "write to socket reqType:" + reqType + ",reqNum:" + reqNum
                    + ",dataLenght:" + length);
            boolean writeData = writeData(byteBuffer.array());
            if (!writeData) {
                retryConnect(0);
            } else {
                return true;
            }
        } else {
            LogMsg.print(TAG, "write failed because of connectStatus:" + getSocketStatus());
        }
        return false;
    }

    protected abstract boolean writeData(byte[] array);

    public boolean isConnect() {
        return getSocketStatus() == SocketConnectStatus.CONNECTED;
    }

    private synchronized SocketConnectStatus getSocketStatus() {
        return connectStatus;
    }

    private synchronized void retryConnect(long delayTime) {
        boolean speechReady = true;
        SocketConnectStatus socketStatus = getSocketStatus();
        if (speechReady) {
            if (socketStatus != SocketConnectStatus.CONNECTING) {
                setSocketStatus(SocketConnectStatus.CONNECTING);
                close();
                /*发生IO异常时才去尝试重连*/
                connect(delayTime);
            } else {
                LogMsg.print(TAG, "retryConnect failed because of socket status is " + socketStatus);
            }
        } else {
            setSocketStatus(SocketConnectStatus.OFFLINE);
            close();
            LogMsg.print(TAG, "retryConnect failed because of speechReady is not ready ");
        }
    }

    public void connect(long delayTime) {
        service.submit(() -> {
            try {
                Thread.sleep(delayTime);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            try {
                LogMsg.print(TAG, "connect() called");
                if (!isConnect()) {
                    SocketConnectStatus socketConnectStatus = connectSocket();
                    setSocketStatus(socketConnectStatus);
                    switch (socketConnectStatus) {
                        case CONNECTED:
                            LogMsg.print(TAG, "connect success");
                            String wakeupAlgorithm = EnvironmentConfig.WakeupConfig
                                    .getWakeupAlgorithm();
                            if (TextUtils.equals(wakeupAlgorithm,
                                    SpeechConfig.CONFIG_WAKUP_MULTIPLE_WAVE_NET)) {
                                service.execute(() -> AudioDumpManager.getInstance().init());
                            }
                            onConnected();
                            break;
                        case OFFLINE:
                            retryConnect(DEFALUT_DELAY_TIME);
                            break;
                        case CONNECTING:
                        default:
                            break;
                    }
                }

            } catch (Exception e) {
                LogMsg.print(TAG, "connect error:" + e.getMessage());

                setSocketStatus(SocketConnectStatus.OFFLINE);
                retryConnect(DEFALUT_DELAY_TIME);
            }
        });
    }

    /**
     * 当连接成功时做的操作
     */
    protected void onConnected() {
        while (isConnect()) {
            boolean bodyData = readBodyData();
            if (!bodyData) {
                LogMsg.print(TAG, "read socket length is not right");
                retryConnect(0);
            }
        }
        LogMsg.print(TAG, "socket is interrupt ,mIsConnect:" + isConnect());
    }

    protected void responseData(short msg_type, short req_type, int seq_number, int ret_code,
                                byte[] body) {
        //判断是否是底层返回数据
        if (msg_type == RESPONSE_MSG_TYPE) {
            //body_len==0，无消息体，>0有消息体，需要返回给调用者
            RequestEntity requestEntity = requestEntitySparseArray.get(seq_number);
            if (null != requestEntity) {
                requestEntitySparseArray.remove(seq_number);
                ResponseCallBack responseCallBack = requestEntity.getResponseCallBack();
                CountDownLatch countDownLatch = requestEntity.getCountDownLatch();
                responseCallBack.onResponse(ret_code, body);
                countDownLatch.countDown();
            } else {
                LogMsg.print(TAG, "the seq_number = " + seq_number + " has removed , req_type = "
                        + req_type);
            }
        } else if (msg_type == REQUEST_MSG_TYPE) {
            writeToSocket(RESPONSE_MSG_TYPE, req_type, seq_number, 0, new byte[0]);
            socketCallBack.onSocketReceiveDataCallback(req_type, ret_code, body);
        } else {
            //该种情况不存在
            LogMsg.print(TAG, "wrong msg_type = " + msg_type);
        }
        LogMsg.print(TAG, String.format("audio_wrapper data = %s", Arrays.toString(body)));
    }

    protected abstract boolean readBodyData();

    protected abstract SocketConnectStatus connectSocket();

    void setCallBack(int reqNum, CountDownLatch countDownLatch, ResponseCallBack callBack) {
        if (requestEntitySparseArray.size() == MAXSIZE) {
            requestEntitySparseArray.removeAt(0);
        }
        requestEntitySparseArray.put(reqNum, new RequestEntity(callBack, countDownLatch));
    }

    public void release() {
        close();
        if (null != service) {
            service.shutdownNow();
        }
    }

    protected abstract void close();

    static class RequestEntity {
        private ResponseCallBack responseCallBack;
        private CountDownLatch countDownLatch;

        public RequestEntity(ResponseCallBack responseCallBack, CountDownLatch countDownLatch) {
            this.responseCallBack = responseCallBack;
            this.countDownLatch = countDownLatch;
        }

        public ResponseCallBack getResponseCallBack() {
            return responseCallBack;
        }

        public CountDownLatch getCountDownLatch() {
            return countDownLatch;
        }
    }

    interface ResponseCallBack {
        void onResponse(int errorNum, byte[] responseData);
    }
}
