package com.ainirobot.speechasrservice.socket.audiodump;

import android.net.LocalSocket;
import android.net.LocalSocketAddress;
import android.util.Log;

import com.ainirobot.speechasrservice.socket.AudioDumpManager;
import com.ainirobot.speechasrservice.socket.audioinfo.SocketConnectStatus;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @date 19/04/23
 */
public class InnerSocketDumpEntity extends AudioDumpEntity {

    public static final String TAG = InnerSocketDumpEntity.class.getSimpleName();


    private final String mAddress;

    private LocalSocket mSocket;
    private OutputStream mOutputStream;
    private InputStream mInputStream;


    public InnerSocketDumpEntity(String addressName, AudioDumpManager.SocketCallBack socketCallBack) {
        super(socketCallBack);
        mAddress = addressName;
    }


    @Override
    protected boolean readBodyData() {
//        try {
//            int read = mInputStream.read(readHeadBuffer);
//            if (read == readHeadBuffer.length) {
//                Log.i(TAG,
//                        String.format("threadName = %s,audio_wrapper head = %s",
//                                Thread.currentThread().getName(), Arrays.toString(readHeadBuffer)));
//                ByteBuffer buffer = ByteBuffer.wrap(readHeadBuffer);
//                buffer.order(ByteOrder.nativeOrder());
//                buffer.clear();
//                short msg_type = buffer.getShort();
//                //判断是否是底层返回数据
//                if (msg_type == RESPONSE_MSG_TYPE) {
//                    //判断调用者函数名，好像没啥用
//                    short req_type = buffer.getShort();
//                    int seq_number = buffer.getInt();
//                    //目前错误没有设计
//                    int ret_code = buffer.getInt();
//                    int body_len = buffer.getInt();
//                    byte[] body = new byte[body_len];
//                    //body_len==0，无消息体，>0有消息体，需要返回给调用者
//                    int bodyReaded = mInputStream.read(body);
//                    if (bodyReaded == body_len) {
//                        responseData(seq_number, ret_code, body);
//                    } else {
//                        Log.e(TAG,
//                                String.format("audio_wrapper bodyReaded = %s ,body_len = %s",
//                                        bodyReaded, body_len));
//                    }
//                    return true;
//                }
//
//            } else {
//                Log.e(TAG, "read socket length is not right ,readed:" + read);
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
        return false;
    }

    protected SocketConnectStatus connectSocket() {
        try {
            mSocket = new LocalSocket();
            Log.i(TAG, "begin connect");
            mSocket.connect(new LocalSocketAddress(mAddress, LocalSocketAddress.Namespace.FILESYSTEM));
            Log.i(TAG, "end connect");
            mOutputStream = mSocket.getOutputStream();
            mInputStream = mSocket.getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return (null != mOutputStream && null != mInputStream)
                ? SocketConnectStatus.CONNECTED : SocketConnectStatus.OFFLINE;
    }


    protected void close() {
        if (mSocket != null) {
            Log.i(TAG, "location close.");
            try {
                if (null != mOutputStream) {
                    mOutputStream.close();
                    mOutputStream = null;
                }
                if (null != mInputStream) {
                    mInputStream.close();
                    mInputStream = null;
                }
                if (null != mSocket) {
                    mSocket.close();
                    mSocket = null;
                }

            } catch (Exception e) {
                Log.e(TAG, e.getMessage());
            }
        }
    }

    @Override
    protected boolean writeData(byte[] array) {
        try {
            if (null != mOutputStream) {
                mOutputStream.write(array);
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }
}
