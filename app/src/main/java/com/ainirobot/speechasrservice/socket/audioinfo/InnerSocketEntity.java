package com.ainirobot.speechasrservice.socket.audioinfo;

import android.net.LocalSocket;
import android.net.LocalSocketAddress;

import com.ainirobot.speechasrservice.socket.AudioInfoManager;
import com.ainirobot.speechasrservice.utils.LogMsg;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 19/04/23
 */
public class InnerSocketEntity extends AudioInfoEntity {

    public static final String TAG = InnerSocketEntity.class.getSimpleName();


    private final String mAddress;

    private LocalSocket mSocket;
    private OutputStream mOutputStream;
    private InputStream mInputStream;


    public InnerSocketEntity(String addressName, AudioInfoManager.SocketCallBack socketCallBack) {
        super(socketCallBack);
        mAddress = addressName;
    }


    @Override
    protected boolean readBodyData() {
        try {
            int read = mInputStream.read(readHeadBuffer);
            if (read == readHeadBuffer.length) {
                LogMsg.print(TAG,
                        String.format("threadName = %s,audio_wrapper head = %s",
                                Thread.currentThread().getName(), Arrays.toString(readHeadBuffer)));
                ByteBuffer buffer = ByteBuffer.wrap(readHeadBuffer);
                buffer.order(ByteOrder.nativeOrder());
                buffer.clear();
                short msg_type = buffer.getShort();
                short req_type = buffer.getShort();
                int seq_number = buffer.getInt();
                //目前错误没有设计
                int ret_code = buffer.getInt();
                int body_len = buffer.getInt();
                byte[] body = new byte[body_len];
                //body_len==0，无消息体，>0有消息体，需要返回给调用者
                int bodyReaded = 0;
                if (body_len > 0) {
                    bodyReaded = mInputStream.read(body);
                }
                if (bodyReaded == body_len) {
                    responseData(msg_type, req_type, seq_number, ret_code, body);
                } else {
                    LogMsg.print(TAG,
                            String.format("audio_wrapper bodyReaded = %s ,body_len = %s",
                                    bodyReaded, body_len));
                }
                return true;

            } else {
                LogMsg.print(TAG, "read socket length is not right ,readed:" + read);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    protected SocketConnectStatus connectSocket() {
        try {
            mSocket = new LocalSocket();
            LogMsg.print(TAG, "begin connect");
            mSocket.connect(new LocalSocketAddress(mAddress, LocalSocketAddress.Namespace.FILESYSTEM));
            LogMsg.print(TAG, "end connect");
            mOutputStream = mSocket.getOutputStream();
            mInputStream = mSocket.getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return (null != mOutputStream && null != mInputStream)
                ? SocketConnectStatus.CONNECTED : SocketConnectStatus.OFFLINE;
    }


    protected void close() {
        if (mSocket != null) {
            LogMsg.print(TAG, "location close.");
            try {
                if (null != mOutputStream) {
                    mOutputStream.close();
                    mOutputStream = null;
                }
                if (null != mInputStream) {
                    mInputStream.close();
                    mInputStream = null;
                }
                if (null != mSocket) {
                    mSocket.close();
                    mSocket = null;
                }

            } catch (Exception e) {
                LogMsg.print(TAG, e.getMessage());
            }
        }
    }

    @Override
    protected boolean writeData(byte[] array) {
        try {
            if (null != mOutputStream) {
                mOutputStream.write(array);
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }
}
