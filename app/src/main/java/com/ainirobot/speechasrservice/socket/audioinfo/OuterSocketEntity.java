package com.ainirobot.speechasrservice.socket.audioinfo;

import com.ainirobot.speechasrservice.socket.AudioInfoManager;
import com.ainirobot.speechasrservice.utils.LogMsg;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;


/**
 * <AUTHOR>
 */
public class OuterSocketEntity extends AudioInfoEntity {

    public static final String TAG = OuterSocketEntity.class.getSimpleName();
    private static final int PORT = 7787;
    private ServerSocket serverSocket;
    private OutputStream mOutputStream;
    private InputStream mInputStream;
    private boolean isSocketRunning;

    public OuterSocketEntity(AudioInfoManager.SocketCallBack socketCallBack) {
        super(socketCallBack);
    }


    @Override
    protected boolean readBodyData() {
        try {
            int read = mInputStream.read(readHeadBuffer);
            if (read == readHeadBuffer.length) {
                LogMsg.print(TAG,
                        String.format("threadName = %s,audio_wrapper head = %s",
                                Thread.currentThread().getName(), Arrays.toString(readHeadBuffer)));
                ByteBuffer buffer = ByteBuffer.wrap(readHeadBuffer);
                buffer.order(ByteOrder.nativeOrder());
                buffer.clear();
                short msg_type = buffer.getShort();
                short req_type = buffer.getShort();
                int seq_number = buffer.getInt();
                //目前错误没有设计
                int ret_code = buffer.getInt();
                int body_len = buffer.getInt();
                byte[] body = new byte[body_len];
                //body_len==0，无消息体，>0有消息体，需要返回给调用者
                int bodyReaded = 0;
                if (body_len > 0) {
                    bodyReaded = mInputStream.read(body);
                }
                if (bodyReaded == body_len) {
                    responseData(msg_type, req_type, seq_number, ret_code, body);
                } else {
                    LogMsg.print(TAG,
                            String.format("audio_wrapper bodyReaded = %s ,body_len = %s",
                                    bodyReaded, body_len));
                }
                return true;
            } else {
                LogMsg.print(TAG, "read socket length is not right ,readed:" + read);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    protected SocketConnectStatus connectSocket() {
        if (isConnect()) {
            LogMsg.print(TAG, "serverSocket has started");
            return SocketConnectStatus.CONNECTED;
        }

        try {
            if (null == serverSocket || !serverSocket.isBound()) {
                serverSocket = new ServerSocket(PORT);
                service.execute(this::acceptSocketMsg);
            }
            return SocketConnectStatus.CONNECTING;
        } catch (IOException e) {
            e.printStackTrace();
            return SocketConnectStatus.OFFLINE;
        }
    }

    private void acceptSocketMsg() {
        try {
            isSocketRunning = true;
            while (isSocketRunning) {
                LogMsg.print(TAG, "ready to accept socket connection");
                Socket socket = serverSocket.accept();
                LogMsg.print(TAG, "accept a new socket connection");
                close();
                mOutputStream = socket.getOutputStream();
                mInputStream = socket.getInputStream();
                setSocketStatus(SocketConnectStatus.CONNECTED);
                onConnected();
            }
            LogMsg.print(TAG, "Socket has interrupted");
        } catch (IOException e) {
            LogMsg.print(TAG, e.toString());
        }
    }


    protected void close() {
        try {
            if (null != mOutputStream) {
                mOutputStream.close();
                mOutputStream = null;
            }
            if (null != mInputStream) {
                mInputStream.close();
                mInputStream = null;
            }
        } catch (IOException e) {
            LogMsg.print(TAG, e.getMessage());
        }
    }

    @Override
    protected boolean writeData(byte[] array) {
        try {
            if (null != mOutputStream) {
                mOutputStream.write(array);
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void release() {
        super.release();
        isSocketRunning = false;
        if (null != serverSocket) {
            try {
                serverSocket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            serverSocket = null;
        }
    }
}
