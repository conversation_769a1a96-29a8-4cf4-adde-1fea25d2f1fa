package com.ainirobot.speechasrservice.socket.audiodump;


import android.os.Build;
import android.util.Log;

import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig;
import com.ainirobot.speechasrservice.socket.AudioDumpManager;
import com.ainirobot.speechasrservice.socket.audioinfo.SocketConnectStatus;

public class AudioDumpService implements AudioDumpManager.SocketCallBack {

    private static final String TAG = AudioDumpService.class.getSimpleName();

    private AudioDumpEntity mAudioDumpEntity;

    public AudioDumpService() {
    }

    public void bind(String address) {
        try {
            if (mAudioDumpEntity != null && mAudioDumpEntity.isConnect()) {
                return;
            }
            Log.i(TAG, "address=" + address);
            if (mAudioDumpEntity != null) {
                mAudioDumpEntity.release();
            }
            if (EnvironmentConfig.AsrConfig.isAudioDataFromOut()) {
                mAudioDumpEntity = new OuterSocketDumpEntity(this);
            } else {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                    mAudioDumpEntity = new InnerSocketDumpEntity(address, this);
                } else {
                    mAudioDumpEntity = new HidlDumpEntity(this);
                }
            }
            mAudioDumpEntity.connect(AudioDumpEntity.DEFALUT_DELAY_TIME);
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        }
    }

    public void release() {
        if (null != mAudioDumpEntity) {
            mAudioDumpEntity.release();
            mAudioDumpEntity = null;
        }
    }

    public boolean isSocketReady() {
        if (null != mAudioDumpEntity) {
            return mAudioDumpEntity.isConnect();
        }
        return false;
    }

    public void writeData2Socket(int reqType, byte[] dataInfo,
                                 AudioDumpEntity.ResponseCallBack responseCallBack) {
        if (mAudioDumpEntity == null) {
            return;
        }

        mAudioDumpEntity.writeData2Socket(reqType, dataInfo, responseCallBack);
    }

    @Override
    public void onSocketStatusChanged(SocketConnectStatus status) {
//        EventBus.getDefault().post(status, EventBusTag.SOCKETCONNECTCHANGE);
    }
}
