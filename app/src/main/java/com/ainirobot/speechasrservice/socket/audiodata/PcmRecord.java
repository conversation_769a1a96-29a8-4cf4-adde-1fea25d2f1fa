package com.ainirobot.speechasrservice.socket.audiodata;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class PcmRecord {
    private static final String TAG = PcmRecord.class.getSimpleName();

    public static final int ERR_PARAM_ERR = -1; //输入参数错误
    public static final int ERR_FILE_PATH_ERR = -2; //文件存储路径错误
    public static final int ERR_DATA_SAVE_ERR = -3;     //写文件失败
    public static final int ERR_ALREADY_RECORDING = -4; //已经开始录制了，再次开始录制会返回本错误码
    public static final int ERR_SOCKET_ERR = -5;         //与audioserver之间的通信错误
    public static final int ERR_DUMP_FLAG_NOT_START = -6; //audioserver的调试开关未打开
    public static final int ERR_SERVER_ININ_FAIL = -7; //audioserver初始化失败
    public static final int ERR_OTHER = -10;            //未知错误
    protected static final int DEF_MAX_FILE_SIZE = 57600000;//半个小时的大小(32000*1800),单位字节
    protected long mMaxFileSize = DEF_MAX_FILE_SIZE; //每个文件大小
    protected long mCurFileSize = 0;                //当前文件的大小，这里只记录第一个mic文件的大小
    protected boolean limitFileSize = true;

    protected ConnClient mSocketClent;
    protected AudioDumpInfo mDumpInfo;
    protected String[] mDumpFileNames; //存储的文件名
    protected String mFilsSaveDir;
    protected RecorderCallback mUiCallback;
    protected boolean mIsRecording;

    /**
     * 通过错误码获取错误信息
     *
     * @param errCode 错误码
     * @return 错误信息
     */
    public static String getErrInfo(int errCode) {
        String errInfo = "other error";
        switch (errCode) {
            case ERR_PARAM_ERR:
                errInfo = "param error";
                break;
            case ERR_FILE_PATH_ERR:
                errInfo = "file save path error";
                break;
            case ERR_DATA_SAVE_ERR:
                errInfo = "file save failed";
                break;
            case ERR_ALREADY_RECORDING:
                errInfo = "already recording";
                break;
            case ERR_SOCKET_ERR:
                errInfo = "cannot connect audioserver";
                break;
            case ERR_SERVER_ININ_FAIL:
                errInfo = "audioserver init failed";
                break;
            case ERR_OTHER:
            default:
                break;
        }
        return errInfo;
    }

    protected void buildSaveFileName(int mic_chan_cnt, int echo_chan_cnt, int out_chan_cnt) {
        SimpleDateFormat formatter = new SimpleDateFormat("yy-MM-dd-HH-mm-ss", Locale.getDefault());
        String nameTmp = mFilsSaveDir + formatter.format(new Date());
        int chan_cnt = mic_chan_cnt + echo_chan_cnt + out_chan_cnt;
        this.mDumpFileNames = new String[chan_cnt];
        for (int i = 1; i <= chan_cnt; i++) {
            String tempName = "";
            if (i <= mic_chan_cnt) {
                tempName = nameTmp + "_" + i + ".pcm";
            } else if (i <= mic_chan_cnt + echo_chan_cnt) {
                tempName = nameTmp + "_echo" + (i - mic_chan_cnt) + ".pcm";
            } else if (i == chan_cnt - 1) {
                tempName = nameTmp + "_recogn.pcm";
            } else {
                tempName = nameTmp + "_wakeup.pcm";
            }
            this.mDumpFileNames[i - 1] = tempName;
        }
        mCurFileSize = 0;
    }

    protected void savePcmData(String filename, byte[] bytes, int offset, int len) {
        try {
            OutputStream os = new FileOutputStream(filename, true);
            os.write(bytes, offset, len);
            os.flush();
            os.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }
}
