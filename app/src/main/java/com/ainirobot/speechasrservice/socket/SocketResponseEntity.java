package com.ainirobot.speechasrservice.socket;

public class SocketResponseEntity {
    private byte[] bodyDatas;
    private int errorNum;//0,正常返回，非0错误

    public SocketResponseEntity(int errorNum, byte[] datas) {
        this.errorNum = errorNum;
        this.bodyDatas = datas;
    }

    public int getErrorNum() {
        return errorNum;
    }

    public byte[] getBodyDatas() {
        return bodyDatas;
    }
}
