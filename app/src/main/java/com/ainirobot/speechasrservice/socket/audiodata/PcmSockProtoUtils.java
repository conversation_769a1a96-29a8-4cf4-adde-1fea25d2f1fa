package com.ainirobot.speechasrservice.socket.audiodata;


import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class PcmSockProtoUtils {
    public static final short MSG_TYPE_REQ = 1;
    public static final short MSG_TYPE_RESP = 2;
    public static final short REQ_TYPE_START = 257;
    public static final short REQ_TYPE_STOP = 258;
    public static final short REQ_TYPE_DATA = 259;
    public static final int LEN_HEAD = 16;
    public static final int PCM_FORMAT_S16_LE = 0;
    public static final int PCM_FORMAT_S32_LE = 1;
    public static final int PCM_FORMAT_S8 = 2;
    public static final int PCM_FORMAT_S24_LE = 3;
    public static final int PCM_FORMAT_S24_3LE = 4;

    public PcmSockProtoUtils() {
    }

    public static byte[] buildMsgHead(short reqType, int reqNumb, int ret, int bodyLen) {
        ByteBuffer byteBuffer = ByteBuffer.allocate(PcmSockProtoUtils.LEN_HEAD);
        byteBuffer.order(ByteOrder.nativeOrder());
        byteBuffer.putShort(MSG_TYPE_REQ);
        byteBuffer.putShort(reqType);
        byteBuffer.putInt(reqNumb);
        byteBuffer.putInt(ret);
        byteBuffer.putInt(bodyLen);
        return byteBuffer.array();
    }

    public static PcmSockProtoHead parasMsgHead(byte[] src) {
        PcmSockProtoHead bean = new PcmSockProtoHead();
        bean.msg_type = ByteUtil.getShort(src, 0);
        bean.req_type = ByteUtil.getShort(src, 2);
        bean.seq_number = ByteUtil.getInt(src, 4);
        bean.ret_code = ByteUtil.getInt(src, 8);
        bean.body_len = ByteUtil.getInt(src, 12);
        return bean;
    }

    public static AudioDumpInfo parasMsgStartBody(byte[] src) {
        AudioDumpInfo bean = new AudioDumpInfo();
        bean.micChannelCount = ByteUtil.getShort(src, 0);
        bean.echoChannelCount = ByteUtil.getShort(src, 2);
        bean.outChannelCount = ByteUtil.getShort(src, 4);
        bean.frameCount = ByteUtil.getInt(src, 8);
        bean.format = protoFormatParam2Bytes(ByteUtil.getInt(src, 12));
        bean.sampleRate = ByteUtil.getInt(src, 16);
        if (src.length > 20) {
            bean.frameSize = ByteUtil.getInt(src, 20);
            bean.asrMode = ByteUtil.getInt(src, 24);
        }
        return bean;
    }

    public static int protoFormatParam2Bytes(int val) {
        int bytes = 0;
        switch (val) {
            case PCM_FORMAT_S16_LE:
                bytes = 2;
                break;
            case PCM_FORMAT_S32_LE:
            case PCM_FORMAT_S24_LE:
                bytes = 4;
                break;
            case PCM_FORMAT_S8:
                bytes = 1;
                break;
            case PCM_FORMAT_S24_3LE:
                bytes = 3;
        }

        return bytes;
    }
}
