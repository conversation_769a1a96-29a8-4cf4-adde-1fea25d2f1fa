package com.ainirobot.speechasrservice.socket.audioinfo;


import android.os.Build;
import android.util.Log;

import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig;
import com.ainirobot.speechasrservice.socket.AudioInfoManager;
import com.ainirobot.speechasrservice.socket.SocketResponseEntity;

import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

public class AudioInfoService {

    private static final String TAG = AudioInfoService.class.getSimpleName();

    private final int SOCKET_WRITE_TIMEOUT = 150;

    private AudioInfoEntity mAudioInfoEntity;

    public AudioInfoService() {
    }

    public void bind(String address, AudioInfoManager.SocketCallBack socketCallBack) {
        try {
            if (mAudioInfoEntity != null && mAudioInfoEntity.isConnect()) {
                return;
            }
            Log.i(TAG, "address=" + address);
            if (mAudioInfoEntity != null) {
                mAudioInfoEntity.release();
            }
            if (EnvironmentConfig.AsrConfig.isAudioDataFromOut()) {
                mAudioInfoEntity = new OuterSocketEntity(socketCallBack);
            } else {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                    Log.i(TAG, "InnerSocketEntity=" + address);
                    mAudioInfoEntity = new InnerSocketEntity(address, socketCallBack);
                } else {
                    Log.i(TAG, "HidlEntity=" + address);
                    mAudioInfoEntity = new HidlEntity(socketCallBack);
                }
            }
            mAudioInfoEntity.connect(AudioInfoEntity.DEFALUT_DELAY_TIME);
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        }
    }

    public void release() {
        if (null != mAudioInfoEntity) {
            mAudioInfoEntity.release();
            mAudioInfoEntity = null;
        }
    }

    public boolean isSocketReady() {
        if (null != mAudioInfoEntity) {
            return mAudioInfoEntity.isConnect();
        }
        return false;
    }

    public SocketResponseEntity writeData2Socket(int reqType, byte[] dataInfo, int outTime) {
        if (mAudioInfoEntity == null) {
            return null;
        }
        Future<SocketResponseEntity> future = mAudioInfoEntity.writeData2Socket(reqType, dataInfo,outTime);
        try {
            if (future != null) {
                return future.get(SOCKET_WRITE_TIMEOUT, TimeUnit.MILLISECONDS);
            }
        } catch (Exception e) {
            e.printStackTrace();
//            SkillManager.getInstance().restartASR();
        }
        return null;
    }

}
