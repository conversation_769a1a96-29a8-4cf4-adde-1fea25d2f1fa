package com.ainirobot.speechasrservice.socket.audiodata;

import android.net.LocalSocket;
import android.net.LocalSocketAddress;
import android.util.Log;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class SocketConnClient extends ConnClient {
    private static final String TAG = SocketConnClient.class.getSimpleName();
    private LocalSocket socket;
    private InputStream inputStream;
    private OutputStream outputStream;

    @Override
    protected void stopConnection() {
        try {
            if (null != inputStream)
                inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        try {
            if (null != outputStream)
                outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        try {
            if (null != socket)
                socket.close();
            socket = null;
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected int readMsg(byte[] btMsgHeadRecv) {
        try {
            if (null != inputStream) {
                return inputStream.read(btMsgHeadRecv, 0, btMsgHeadRecv.length);
            }
        } catch (IOException var21) {
            var21.printStackTrace();
        }

        return -1;
    }

    @Override
    protected boolean writeMsg(byte[] btMsgHeadSend) {
        try {
            if (null != outputStream) {
                outputStream.write(btMsgHeadSend, 0, btMsgHeadSend.length);
                return true;
            }
        } catch (Exception var22) {
            var22.printStackTrace();
            Log.e(TAG, "write data failed");
        }
        return false;
    }

    @Override
    protected boolean initConnection() {
        try {
            socket = new LocalSocket();
            int bufferSize = INIT_FUF_LEN;
            socket.connect(new LocalSocketAddress("/data/misc/audioserver/pcm_dump", LocalSocketAddress.Namespace.FILESYSTEM));
            socket.setReceiveBufferSize(bufferSize * 10);
            socket.setSendBufferSize(bufferSize * 10);
            socket.setSoTimeout(1000);
            inputStream = socket.getInputStream();
            outputStream = socket.getOutputStream();
            return true;
        } catch (IOException var3) {
            var3.printStackTrace();
            return false;
        }
    }

}
