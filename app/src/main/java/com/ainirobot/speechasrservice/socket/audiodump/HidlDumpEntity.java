package com.ainirobot.speechasrservice.socket.audiodump;

import android.util.Log;

import com.ainirobot.speechasrservice.socket.AudioDumpManager;
import com.ainirobot.speechasrservice.socket.audioinfo.SocketConnectStatus;
//import com.cm.speech.asr.AwakeAngleJni;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 19/04/23
 */
public class HidlDumpEntity extends AudioDumpEntity {

    public static final String TAG = HidlDumpEntity.class.getSimpleName();
    private int fd = -1;

    public HidlDumpEntity(AudioDumpManager.SocketCallBack socketCallBack) {
        super(socketCallBack);
    }

    protected void close() {
//        int isRelease = AwakeAngleJni.release(fd);
//        Log.i(TAG, "dump close.isRelease:" + isRelease);
    }

    protected SocketConnectStatus connectSocket() {
        Log.i(TAG, "begin connect");
//        fd = AwakeAngleJni.init(AwakeAngleJni.SOCKET_WAKEUP_DUMP);
        Log.i(TAG, "end connect,isInit:" + fd);
        return fd > 0 ? SocketConnectStatus.CONNECTED : SocketConnectStatus.OFFLINE;
    }


    @Override
    protected boolean writeData(byte[] array) {
//        int sendStatus = AwakeAngleJni.send(fd, array);
//        Log.d(TAG, "sendMessage status:" + sendStatus);
//        if (sendStatus > 0) {
//            return true;
//        } else {
            return false;
//        }
    }

    @Override
    protected boolean readBodyData() {
//        int read = AwakeAngleJni.receive(fd, readHeadBuffer);
//        if (read == readHeadBuffer.length) {
//            Log.i(TAG,
//                    String.format("threadName = %s,audio_wrapper readBuffer = %s",
//                            Thread.currentThread().getName(), Arrays.toString(readHeadBuffer)));
//            ByteBuffer buffer = ByteBuffer.wrap(readHeadBuffer);
//            buffer.order(ByteOrder.nativeOrder());
//            buffer.clear();
//            short msg_type = buffer.getShort();
//            Log.i(TAG, "msg_type:" + msg_type);
//            //判断是否是底层返回数据
//            if (msg_type == RESPONSE_MSG_TYPE) {
//                //判断调用者函数名，好像没啥用
//                short req_type = buffer.getShort();
//                int seq_number = buffer.getInt();
//                //目前错误没有设计
//                int ret_code = buffer.getInt();
//                int body_len = buffer.getInt();
//                Log.i(TAG, "body_len:" + body_len);
//                if (body_len > 0) {
//                    int posion = 0;
//                    int index = 0;//包数
//                    //目前该处设计共循环175次
//                    while (body_len > posion) {
//                        //目前该处每次取出640*8字节，共8路数据
//                        byte[] pcmData = new byte[640 * 8];
//                        int bodyReaded = AwakeAngleJni.receive(fd, pcmData);
//                        posion = posion + bodyReaded;
//                        index++;
//                        index = (posion >= body_len) ? -index : index;
//                        if (index < 2) {
//                            Log.i(TAG, "bodyReaded:" + bodyReaded + ",posion:"
//                                    + posion + ",index:" + index);
//                        }
//                        responseData(seq_number, ret_code, index, pcmData);
//                    }
//
//                } else {
//                    Log.e(TAG,
//                            String.format("audio_wrapper bodyReaded = %s ,body_len = %s",
//                                    body_len, body_len));
//                }
//
//            }
//            return true;
//        } else {
            return false;
//        }
    }
}
