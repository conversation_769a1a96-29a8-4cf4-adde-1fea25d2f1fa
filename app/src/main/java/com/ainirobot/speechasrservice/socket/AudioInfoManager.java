package com.ainirobot.speechasrservice.socket;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.speechasrservice.kratos.cofig.ClientConfig;
import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig;
import com.ainirobot.speechasrservice.skill.SkillManager;
import com.ainirobot.speechasrservice.socket.audioinfo.AudioInfoService;
import com.ainirobot.speechasrservice.socket.audioinfo.SocketConnectStatus;
import com.ainirobot.speechasrservice.utils.EventBusTag;
import com.ainirobot.speechasrservice.utils.SpeechConfig;

import org.greenrobot.eventbus.EventBus;

//import org.simple.eventbus.EventBus;

public class AudioInfoManager {

    private static final String TAG = AudioInfoManager.class.getSimpleName();

    private AudioInfoService mAudioInfoService;

    public boolean isSocketReady() {
        return mAudioInfoService.isSocketReady();
    }


    private static final class Instance {
        private static final AudioInfoManager INSTANCE = new AudioInfoManager();
    }

    public static AudioInfoManager getInstance() {
        return Instance.INSTANCE;
    }

    private AudioInfoManager() {
        mAudioInfoService = new AudioInfoService();
    }

    /**
     * 定位功能相关socket连接, 小豹使用默认的
     */
    public final void init() {
        try {
            mAudioInfoService.bind(ClientConfig.SOCKETADDRESS, new SocketCallBack() {
                @Override
                public void onSocketStatusChanged(SocketConnectStatus status) {
                    Log.d(TAG, "onSocketStatusChanged: " + status);
                    if (status == SocketConnectStatus.CONNECTED) {
                        SkillManager.getInstance().onSocketConnected(AudioInfoManager.this::writeData2Socket);
                    } else if (status == SocketConnectStatus.OFFLINE) {
                        SkillManager.getInstance().onSocketDisconnected();
                    }
                }

                @Override
                public void onSocketReceiveDataCallback(int req_type, int ret_code, byte[] body) {
                    doSocketReceiveData(req_type, ret_code, body);
                }
            });
        } catch (Exception e) {
            Log.w(TAG, e);
        }
    }

    private void doSocketReceiveData(int req_type, int ret_code, byte[] body) {
        String wakeupAlgorithm = EnvironmentConfig.WakeupConfig.getWakeupAlgorithm();
        if (TextUtils.equals(wakeupAlgorithm, SpeechConfig.CONFIG_WAKUP_MULTIPLE_WAVE_NET)) {
            if (req_type == ClientConfig.REQTYPE_WAKEUP_ANGLE) {
                int wakeupId = 10000;
                if (ret_code > 0 && ret_code < wakeupId) {
                    if (null != body && body.length > 0) {
                        SkillManager.getInstance().onWakeUp(body, true);
                    }
                    if (ret_code == 2) {//只上传小豹小豹数据
//                        SkillManager.getInstance().dumpAudioData(AudioDumpManager.TYPE_WAKEUP);
                    }
                } else if (ret_code >= wakeupId) {//疑似唤醒数据不包含自定义唤醒词的疑似唤醒数据
//                    SkillManager.getInstance().dumpAudioData(AudioDumpManager.TYPE_SUSPECT_WAKEUP);
                }
                Log.d(TAG, "wakeup message: ret_code " + ret_code);

            } else {
                Log.d(TAG, "req_typee = " + req_type + " undefined");
            }
        } else {
            Log.d(TAG, "wakeupAlgorithm = " + wakeupAlgorithm);
        }
    }

    /**
     * 写入一段数据到socket中，robot需要使用
     *
     * @param reqType  请求的type值
     * @param dataInfo 写入的数据
     */
    public SocketResponseEntity writeData2Socket(int reqType, byte[] dataInfo, int outTime) {
        if (isSocketReady()) {
            return mAudioInfoService.writeData2Socket(reqType, dataInfo, outTime);
        } else {
            return null;
        }
    }


    /**
     * 释放相关的资源
     */
    public void release() {
        if (mAudioInfoService != null) {
            mAudioInfoService.release();
        }
        String wakeupAlgorithm = EnvironmentConfig.WakeupConfig
                .getWakeupAlgorithm();
        if (TextUtils.equals(wakeupAlgorithm,
                SpeechConfig.CONFIG_WAKUP_MULTIPLE_WAVE_NET)) {
            AudioDumpManager.getInstance().release();
        }
    }

    public interface SocketCallBack {
        /**
         * socket连接或者断开回调
         */
        void onSocketStatusChanged(SocketConnectStatus status);

        void onSocketReceiveDataCallback(int req_type, int ret_code, byte[] body);
    }

}
