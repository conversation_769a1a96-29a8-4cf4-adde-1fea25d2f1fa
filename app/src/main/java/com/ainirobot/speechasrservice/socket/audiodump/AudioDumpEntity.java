package com.ainirobot.speechasrservice.socket.audiodump;

import android.util.Log;
import android.util.SparseArray;

import com.ainirobot.speechasrservice.socket.AudioDumpManager;
import com.ainirobot.speechasrservice.socket.AudioInfoManager;
import com.ainirobot.speechasrservice.socket.audioinfo.SocketConnectStatus;
import com.ainirobot.speechasrservice.utils.LogMsg;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

public abstract class AudioDumpEntity {
    public static final String TAG = AudioDumpEntity.class.getSimpleName();
    private static final short REQUEST_MSG_TYPE = 1;
    protected static final short RESPONSE_MSG_TYPE = 2;
    protected static final int HEAD_LENGTH = 16;
    protected static long DEFALUT_DELAY_TIME = 3000;
    protected static final byte readHeadBuffer[] = new byte[HEAD_LENGTH];
    /**
     * 如果底层返回过慢或者线程调用较多时，该值过小会丢失数据。过大比较消耗内存。该值为经验值
     */
    private static final int MAXSIZE = 50;
    private SparseArray<ResponseCallBack> requestEntitySparseArray = new SparseArray<>();
    private int reqNum;
    private volatile SocketConnectStatus connectStatus;

    protected final ExecutorService service;
    private AudioDumpManager.SocketCallBack socketCallBack;

    public AudioDumpEntity(AudioDumpManager.SocketCallBack socketCallBack) {
        this.socketCallBack = socketCallBack;
        service = Executors.newCachedThreadPool(new ThreadFactory() {
            private final AtomicInteger mCount = new AtomicInteger(1);

            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "asr-dumpAudioData-" + mCount.getAndIncrement());
                t.setPriority(Thread.MAX_PRIORITY);
                android.os.Process.setThreadPriority(-19);
                return t;
            }
        });
    }

    protected synchronized void setSocketStatus(SocketConnectStatus socketStatus) {
        connectStatus = socketStatus;
        Log.w(TAG, "setSocketStatus:" + connectStatus);
        if (null != socketCallBack) {
            socketCallBack.onSocketStatusChanged(connectStatus);
        } else {
            Log.w(TAG, "socketCallBack is null,can't return socket status");
        }
    }

    //输入标准数据格式
    public void writeData2Socket(final int reqType, final byte[] dataInfo,
                                 final ResponseCallBack responseCallBack) {
        LogMsg.print(TAG, "writeData2Socket-->reqType:" + reqType);
        if (!isConnect()) {
            LogMsg.print(TAG, "writeData2Socket-->socket is not ready");
            return;
        }
        service.execute(() -> {
            synchronized (AudioDumpEntity.class) {
                if (reqNum == MAXSIZE) {
                    reqNum = 0;
                } else {
                    reqNum++;
                }
                setCallBack(reqNum, responseCallBack);
            }
            writeToSocket((short) reqType, dataInfo);
        });
    }

    private boolean writeToSocket(final short reqType, byte[] dataInfo) {
        if (isConnect()) {
            int length = dataInfo.length;
            ByteBuffer byteBuffer = ByteBuffer.allocate(HEAD_LENGTH + length);
            byteBuffer.order(ByteOrder.nativeOrder());
            byteBuffer.putShort(REQUEST_MSG_TYPE);
            byteBuffer.putShort(reqType);
            byteBuffer.putInt(reqNum);
            byteBuffer.putInt(0);
            byteBuffer.putInt(length);
            byteBuffer.put(dataInfo);
            Log.d(TAG, "write to socket reqType:" + reqType + ",reqNum:" + reqNum
                    + ",dataLenght:" + length);
            boolean writeData = writeData(byteBuffer.array());
            if (!writeData) {
                retryConnect(0);
            } else {
                return true;
            }
        } else {
            Log.e(TAG, "write failed because of connectStatus:" + getSocketStatus());
        }
        return false;
    }

    protected abstract boolean writeData(byte[] array);

    public boolean isConnect() {
        return getSocketStatus() == SocketConnectStatus.CONNECTED;
    }

    private synchronized SocketConnectStatus getSocketStatus() {
        return connectStatus;
    }

    private synchronized void retryConnect(long delayTime) {
        SocketConnectStatus socketStatus = getSocketStatus();
        boolean socketReady = AudioInfoManager.getInstance().isSocketReady();
        if (socketReady) {
            if (socketStatus != SocketConnectStatus.CONNECTING) {
                setSocketStatus(SocketConnectStatus.CONNECTING);
                close();
                /*发生IO异常时才去尝试重连*/
                connect(delayTime);
            } else {
                LogMsg.print(TAG, "retryConnect failed because of socket status is " + socketStatus);
            }
        } else {
            setSocketStatus(SocketConnectStatus.OFFLINE);
            close();
            LogMsg.print(TAG, "retryConnect failed because of audio info socket is not ready ");
        }
    }

    public void connect(long delayTime) {
        service.submit(() -> {
            try {
                Thread.sleep(delayTime);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            try {
                Log.i(TAG, "connect() called");
                if (!isConnect()) {
                    SocketConnectStatus socketConnectStatus = connectSocket();
                    setSocketStatus(socketConnectStatus);
                    switch (socketConnectStatus) {
                        case CONNECTED:
                            Log.i(TAG, "connect success");
                            onConnected();
                            break;
                        case OFFLINE:
                            retryConnect(DEFALUT_DELAY_TIME);
                            break;
                        case CONNECTING:
                        default:
                            break;
                    }
                }

            } catch (Exception e) {
                Log.w(TAG, "connect error:" + e.getMessage());
                setSocketStatus(SocketConnectStatus.OFFLINE);
                retryConnect(DEFALUT_DELAY_TIME);
                Log.w(TAG, "connect error:" + e.getMessage());
            }
        });
    }

    /**
     * 当连接成功时做的操作
     */
    protected void onConnected() {
        while (isConnect()) {
            boolean bodyData = readBodyData();
            if (!bodyData) {
                Log.e(TAG, "read socket length is not right");
                retryConnect(0);
            }
        }
        Log.d(TAG, "socket is interrupt ,mIsConnect:" + isConnect());
    }

    protected void responseData(int seq_number, int ret_code, int index, byte[] pcmData) {
        //body_len==0，无消息体，>0有消息体，需要返回给调用者
        ResponseCallBack responseCallBack = requestEntitySparseArray.get(seq_number);
        if (index < 0) {
            requestEntitySparseArray.remove(seq_number);
        }
        responseCallBack.onResponse(index, pcmData);
    }

    protected abstract boolean readBodyData();

    protected abstract SocketConnectStatus connectSocket();

    void setCallBack(int reqNum, ResponseCallBack responseCallBack) {
        if (requestEntitySparseArray.size() == MAXSIZE) {
            requestEntitySparseArray.removeAt(0);
        }
        requestEntitySparseArray.put(reqNum, responseCallBack);
    }

    public void release() {
        close();
        if (null != service) {
            service.shutdownNow();
        }
    }

    protected abstract void close();


    public interface ResponseCallBack {
        void onResponse(int index, byte[] responseData);
    }

}
