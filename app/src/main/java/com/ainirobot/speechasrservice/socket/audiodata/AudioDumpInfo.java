package com.ainirobot.speechasrservice.socket.audiodata;

import android.support.annotation.IntDef;
import android.support.annotation.NonNull;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Locale;

public class AudioDumpInfo {
    @IntDef({AsrMode.M0, AsrMode.M2})
    @Retention(RetentionPolicy.RUNTIME)
    public @interface AsrMode {
        int M0 = 0;
        int M2 = 2;
    }

    public short micChannelCount;  //mic数量
    public short echoChannelCount; //左、右、低音三路数据
    public int outChannelCount; //算法输出，识别与唤醒两路数据
    public int frameCount; //单帧数据大小
    public int format; //单个数据占用16bit，format是占用byte数，为2
    public int sampleRate; //采样率16K
    public int frameSize; //单帧大小（各版本帧大小不一致，直接底层传递，不需要上层计算）
    public @AsrMode int asrMode; //Asr算法模式，m0 固定帧，m1 动态帧（只有识别及唤醒两路数据为动态, 最大10帧）

    public AudioDumpInfo() {
    }

    @NonNull
    @Override
    public String toString() {
        return String.format(Locale.getDefault(), "Audio dump info, micChannelCount:%d, echoChannelCount:%d, outChannelCount:%d, " +
                        "sampleRate:%d, format:%d, frameCount:%d, frameSize:%d, asrMode:%d", micChannelCount, echoChannelCount,
                outChannelCount, sampleRate, format, frameCount, frameSize, asrMode);
    }

    /**
     * 单通道单帧大小
     */
    public int channelFrameSize() {
        return frameCount * format;
    }

    /**
     * 是否输出动态帧（m0 固定帧，m2 动态帧）
     */
    public boolean isDynaFrame() {
        return asrMode >= AsrMode.M2;
    }

    public int getFrameSize() {
        if (frameSize > 0) {
            return frameSize;
        }
        return channelFrameSize() * (micChannelCount + echoChannelCount + outChannelCount);
    }
}
