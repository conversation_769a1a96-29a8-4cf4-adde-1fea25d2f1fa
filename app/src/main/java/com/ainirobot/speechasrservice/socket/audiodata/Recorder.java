package com.ainirobot.speechasrservice.socket.audiodata;


import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import java.io.File;

/**
 * 原始PCM数据录制类
 */
public class Recorder extends PcmRecord {
    private static final String TAG = Recorder.class.getSimpleName();
    private static Recorder mInstance;

    private Recorder() {
    }

    public static Recorder getInstance() {
        if (mInstance == null) {
            mInstance = new Recorder();
        }

        return mInstance;
    }

    public boolean isRecording() {
        return mIsRecording;
    }

    public int startRecord(String filSaveDir, RecorderCallback evetnCallback) {
        Log.i(TAG, "savedir:" + filSaveDir);
        if (mIsRecording) {
            Log.e(TAG, "this is recording, cannot restart");
            return ERR_ALREADY_RECORDING;
        } else if (TextUtils.isEmpty(filSaveDir) || !(new File(filSaveDir)).exists()) {
            Log.e(TAG, "input save file dir error:" + filSaveDir);
            return ERR_FILE_PATH_ERR;
        }

        if (!filSaveDir.endsWith("/")) {
            filSaveDir += "/";
        }
        mFilsSaveDir = filSaveDir;
        mUiCallback = evetnCallback;


        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            mSocketClent = new SocketConnClient();
        } else {
            mSocketClent = new HidlConnClient();
        }
        int iRet = mSocketClent.startClient(mClientCallback);
        if (iRet != 0) {
            Log.e(TAG, "record failed," + iRet);
            mSocketClent.stopClient();
            mSocketClent = null;
            return ERR_ALREADY_RECORDING;
        }
        mIsRecording = true;
        return 0;
    }

    /**
     * 结束录制
     */
    public void stopRecord() {
        Log.i(TAG, "this stop record");
        if (!mIsRecording) {
            Log.i(TAG, "there is already stoped");
            return;
        }
        mIsRecording = false;
        if (mUiCallback != null) {
            mUiCallback.onFilesSaveEnded(mDumpFileNames);
        }

        if (mSocketClent != null) {
            mSocketClent.stopClient();
            mSocketClent = null;
        }
    }

    /**
     * @param recordTime 录制时长，单位s。
     * @return 0表示成功, 其他值失败
     */
    public int setRecorderTime(int recordTime) {
        if (mIsRecording) {
            Log.e(TAG, "this is recording, cannot set param");
            return ERR_ALREADY_RECORDING;
        }
        int perSecondData = 16000 * 2;
        long fileSize = recordTime * perSecondData;
        Log.i(TAG, "max file size:" + fileSize);
        if (fileSize < perSecondData) {
            Log.e(TAG, "set file size cnt err,input:" + fileSize);
            return ERR_PARAM_ERR;
        }
        mMaxFileSize = fileSize;
        return 0;
    }

    public void limitFileSize(boolean limit) {
        limitFileSize = limit;
    }

    private ClientCallback mClientCallback = new ClientCallback() {
        public void onPcmParamIn(AudioDumpInfo info) {
            mDumpInfo = info;
            Log.d(Recorder.TAG, info.toString());
            buildSaveFileName(info.micChannelCount, info.echoChannelCount, info.outChannelCount);
        }

        public void onPcmFrameDataIn(byte[] data, int len) {
            if (!mIsRecording) {
                Log.i(Recorder.TAG, "there is not recording");
                return;
            }
            int channelFrameSize = mDumpInfo.channelFrameSize();
            if (len != mDumpInfo.getFrameSize()) {
                Log.e(TAG, "read length is not right, len = " + len);
                return;
            }

            if (mDumpInfo.isDynaFrame()) {
                //存储mic + echo数据，除识别与唤醒外，其他通道帧大小固定
                int preSize = saveDumpPcmData(mDumpInfo.micChannelCount + mDumpInfo.echoChannelCount, data, channelFrameSize);
                //获取帧数
                int frameCount = ByteUtil.getInt(data, preSize);
                //识别数据起始位置
                int recog = preSize + 4;
                //存储识别数据
                savePcmData(mDumpFileNames[9], data, recog, channelFrameSize * frameCount);

                //存储唤醒数据
                int wake = recog + (data.length - recog) / 2;
                if (mDumpInfo.outChannelCount > 1) {
                    savePcmData(mDumpFileNames[10], data, wake, channelFrameSize * frameCount);
                }

                Log.d(Recorder.TAG, "Save dyna data, data size : " + data.length + ", channel size : " + preSize + ", dyna frame : " + frameCount + ", recog start : " + recog + ", wake start : " + wake);
            } else {
                saveDumpPcmData(mDumpInfo.micChannelCount + mDumpInfo.echoChannelCount + mDumpInfo.outChannelCount, data, channelFrameSize);
            }

//            if (mUiCallback != null) {
//                mUiCallback.onError(ERR_DATA_SAVE_ERR, PcmRecord.getErrInfo(ERR_DATA_SAVE_ERR));
//            }

            if (mCurFileSize > mMaxFileSize) {
                Log.e(Recorder.TAG, "file size max than size, will new file:" + mMaxFileSize);
                stopRecord();
                if (mUiCallback != null) {
                    mUiCallback.onFilesSaveEnded(mDumpFileNames);
                }
            }

        }

        public void onClientEnded(int errcode, String info) {
            String errOut = "录制结束," + errcode + "," + info;
            Log.e(Recorder.TAG, errOut);
            if (errcode != 0 && mUiCallback != null) {
                int err = errcode;
                switch (errcode) {
                    case ConnClient.SERR_OTHER_ERR:
                        err = ERR_SERVER_ININ_FAIL;
                        break;
                    case ConnClient.SERR_SOCKET_ERROR:
                        err = ERR_SOCKET_ERR;
                        break;
                    case ConnClient.SERR_SERVER_INIT_FAILED:
                        err = ERR_OTHER;
                }

                mUiCallback.onError(err, PcmRecord.getErrInfo(err));
            }

            stopRecord();
        }
    };


    private int saveDumpPcmData(int channelCount, byte[] data, int mPerFramLength) {
        int saveLen = 0;
        for (int i = 0; i < channelCount; i++) {
            savePcmData(mDumpFileNames[i], data, saveLen, mPerFramLength);
            saveLen += mPerFramLength;
        }
        mCurFileSize += mPerFramLength;
        return saveLen;
    }
}
