package com.ainirobot.speechasrservice.socket.audioinfo;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Process;

import com.ainirobot.app.MainApp;
import com.ainirobot.speechasrservice.socket.AudioInfoManager;
import com.ainirobot.speechasrservice.utils.LogMsg;
import com.cm.speech.asr.AwakeAngleJni;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 19/04/23
 */
public class HidlEntity extends AudioInfoEntity {

    public static final String TAG = HidlEntity.class.getSimpleName();
    private int fd = -1;

    public HidlEntity(AudioInfoManager.SocketCallBack socketCallBack) {
        super(socketCallBack);
    }

    protected void close() {
        int isRelease = AwakeAngleJni.release(fd);
        LogMsg.print(TAG, "location close.isRelease:" + isRelease);
    }

    protected SocketConnectStatus connectSocket() {
        LogMsg.print(TAG, "begin connect "+ getProcessName());
        fd = AwakeAngleJni.init(AwakeAngleJni.SOCKET_ASR);
        LogMsg.print(TAG, "end connect,isInit:" + fd);
        return fd > 0 ? SocketConnectStatus.CONNECTED : SocketConnectStatus.OFFLINE;
    }

    private String getProcessName() {
        int pid = Process.myPid();
        ActivityManager am = (ActivityManager) MainApp.getInstance().getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);

        if (am != null) {
            for (ActivityManager.RunningAppProcessInfo processInfo : am.getRunningAppProcesses()) {
                if (processInfo.pid == pid) {
                    return processInfo.processName;
                }
            }
        }
        return null;
    }


    @Override
    protected boolean writeData(byte[] array) {
        int sendStatus = AwakeAngleJni.send(fd, array);
        LogMsg.print(TAG, "sendMessage status:" + sendStatus + " , array length " + array.length);
        if (sendStatus > 0) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    protected boolean readBodyData() {
        int read = AwakeAngleJni.receive(fd, readHeadBuffer);
        if (read == readHeadBuffer.length) {
            LogMsg.print(TAG,
                    String.format("threadName = %s,audio_wrapper readBuffer = %s",
                            Thread.currentThread().getName(), Arrays.toString(readHeadBuffer)));
            ByteBuffer buffer = ByteBuffer.wrap(readHeadBuffer);
            buffer.order(ByteOrder.nativeOrder());
            buffer.clear();
            short msg_type = buffer.getShort();
            short req_type = buffer.getShort();
            int seq_number = buffer.getInt();
            //目前错误没有设计
            int ret_code = buffer.getInt();
            int body_len = buffer.getInt();
            byte[] body = new byte[body_len];
            //body_len==0，无消息体，>0有消息体，需要返回给调用者
            int bodyReaded = 0;
            if (body_len > 0) {
                bodyReaded = AwakeAngleJni.receive(fd, body);
            }
            if (bodyReaded == body_len) {
                responseData(msg_type, req_type, seq_number, ret_code, body);
            } else {
                LogMsg.print(TAG,
                        String.format("audio_wrapper bodyReaded = %s ,body_len = %s",
                                bodyReaded, body_len));
            }
            return true;
        } else {
            LogMsg.print(TAG, "read socket length is not right ,readed:" + read);
        }
        return false;
    }
}
