package com.ainirobot.speechasrservice.skill.entity;

import android.text.TextUtils;

public class OutputEntity {
    private String sid;
    private String traceId;
    private int queryType;
    private String englishDomain;
    private String englishIntent;
    private String intent;
    private String slots;
    private String skillData;
    private String card;
    private String nlpData;
    private String userText;
    private String answerText;
    private boolean answerTextPlay;
    private int soundAngle = -1;
    private AudioAuth audioAuth;


    public OutputEntity(String sid, int queryType, String englishDomain, String englishIntent,
                        String intent, String slots, String skillData, String nlpData,
                        String card, String userText, String answerText, boolean answerTextPlay) {
        this.sid = sid;
        this.queryType = queryType;
        this.englishDomain = englishDomain;
        this.englishIntent = englishIntent;
        this.intent = intent;
        this.slots = slots;
        this.skillData = skillData;
        this.nlpData = nlpData;
        this.card = card;
        this.userText = userText;
        this.answerText = answerText;
        this.answerTextPlay = answerTextPlay;
        this.nlpData = nlpData;
    }

    public String getNlpData() {
        return nlpData;
    }

    public void setNlpData(String nlpData) {
        this.nlpData = nlpData;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public int getQueryType() {
        return queryType;
    }

    public void setQueryType(int queryType) {
        this.queryType = queryType;
    }

    public String getEnglishDomain() {
        return englishDomain;
    }

    public void setEnglishDomain(String englishDomain) {
        this.englishDomain = englishDomain;
    }

    public String getEnglishIntent() {
        return englishIntent;
    }

    public void setEnglishIntent(String englishIntent) {
        this.englishIntent = englishIntent;
    }

    public String getIntent() {
        return intent;
    }

    public void setIntent(String intent) {
        this.intent = intent;
    }

    public String getSlots() {
        return slots;
    }

    public void setSlots(String slots) {
        this.slots = slots;
    }

    public String getSkillData() {
        return skillData;
    }

    public void setSkillData(String skillData) {
        this.skillData = skillData;
    }

    public String getCard() {
        return card;
    }

    public void setCard(String card) {
        this.card = card;
    }

    public String getUserText() {
        return userText;
    }

    public void setUserText(String userText) {
        this.userText = userText;
    }

    public String getAnswerText() {
        return answerText;
    }

    public void setAnswerText(String answerText) {
        this.answerText = answerText;
    }

    public boolean isAnswerTextPlay() {
        return answerTextPlay;
    }

    public void setAnswerTextPlay(boolean answerTextPlay) {
        this.answerTextPlay = answerTextPlay;
    }

    public int getSoundAngle() {
        return soundAngle;
    }

    public void setSoundAngle(int soundAngle) {
        this.soundAngle = soundAngle;
    }

    public AudioAuth getAudioAuth() {
        return audioAuth;
    }

    public void setAudioAuth(AudioAuth audioAuth) {
        this.audioAuth = audioAuth;
    }

    @Override
    public String toString() {
        return "OutputEntity{" +
                "sid='" + sid + '\'' +
                ", traceId='" + traceId + '\'' +
                ", queryType=" + queryType +
                ", englishDomain='" + englishDomain + '\'' +
                ", englishIntent='" + englishIntent + '\'' +
                ", intent='" + intent + '\'' +
                ", slots='" + slots + '\'' +
                ", skillData='" + skillData + '\'' +
                ", card='" + card + '\'' +
                ", nlpData='" + nlpData + '\'' +
                ", userText='" + userText + '\'' +
                ", answerText='" + answerText + '\'' +
                ", answerTextPlay=" + answerTextPlay +
                ", soundAngle=" + soundAngle +
                ", audioAuth=" + audioAuth +
                '}';
    }

    public enum ActionType {
        TYPE_UNDEFINED(-1),
        TYPE_VOICE_INPUT(1),
        TYPE_TEXT_INPUT(2),
        TYPE_LOCALVOICE_INPUT(3);


        private final int queryType;

        ActionType(int queryType) {
            this.queryType = queryType;
        }

        public static int value(String name) {
            if (TextUtils.isEmpty(name)) {
                return TYPE_UNDEFINED.value();
            }
            try {
                return valueOf(name.toUpperCase()).value();
            } catch (Exception e) {
                return TYPE_UNDEFINED.value();
            }
        }

        private int value() {
            return this.queryType;
        }
    }

    public static class AudioAuth {
        private String voice_id;
        private float score;

        public AudioAuth(String voice_id, float score) {
            this.voice_id = voice_id;
            this.score = score;
        }

        public String getVoice_id() {
            return voice_id;
        }

        public void setVoice_id(String voice_id) {
            this.voice_id = voice_id;
        }

        public float getScore() {
            return score;
        }

        public void setScore(float score) {
            this.score = score;
        }

        @Override
        public String toString() {
            return "AudioAuth{" +
                    "voice_id='" + voice_id + '\'' +
                    ", score='" + score + '\'' +
                    '}';
        }
    }
}
