package com.ainirobot.speechasrservice.skill;

import android.support.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * Skill对应的Domain、Intent常量值定义
 *
 * <AUTHOR>
 */
public interface SkillConstant {

    @StringDef({Domain.DOMAIN_EXCEPTION, Domain.DOMAIN_OTHER, Domain.DOMAIN_LEADING,
            Domain.DOMAIN_REGISTER, Domain.DOMAIN_WEATHER, Domain.DOMAIN_CALENDAR, Domain.DOMAIN_CALCULATOR,
            Domain.DOMAIN_CHAT, Domain.DOMAIN_FOOD_RECOMMENDATION, Domain.DOMAIN_NEWS, Domain.DOMAIN_FM,
            Domain.DOMAIN_MUSIC, Domain.DOMAIN_GENERAL_COMMAND, Domain.DOMAIN_TELL_ME_WHY})
    @Retention(RetentionPolicy.SOURCE)
    @interface Domain {
        String DOMAIN_EXCEPTION = "exception";//语义异常
        String DOMAIN_OTHER = "other";//其它
        String DOMAIN_LEADING = "guide";//引导
        String DOMAIN_REGISTER = "register";//注册
        String DOMAIN_WEATHER = "weather";//天气
        String DOMAIN_CALENDAR = "calendar";//日历
        String DOMAIN_CALCULATOR = "calculator";// 计算器
        String DOMAIN_CHAT = "chat";//聊天
        String DOMAIN_FOOD_RECOMMENDATION = "food_recommendation";//食物推荐
        String DOMAIN_NEWS = "news";//新闻
        String DOMAIN_FM = "fm";//电台
        String DOMAIN_MUSIC = "music";//音乐
        String DOMAIN_GENERAL_COMMAND = "general_command";//通用指令
        String DOMAIN_TELL_ME_WHY = "tell_me_why";//百度百科
        String DOMAIN_STREAM = "stream";
    }

    @StringDef({Intent.INTENT_OTHER, Intent.INTENT_SINGLE_OTHER, Intent.INTENT_GET_WEATHER,
            Intent.INTENT_CHAT, Intent.INTENT_COMMON, Intent.INTENT_CHATGPT})
    @Retention(RetentionPolicy.SOURCE)
    @interface Intent {
        String INTENT_OTHER = "other";
        String INTENT_SINGLE_OTHER = "single_other";
        String INTENT_GET_WEATHER = "get_weather";
        String INTENT_CHAT = "chat";
        String INTENT_COMMON = "common";
        String INTENT_CHATGPT = "chatgpt";
        String INTENT_STREAM_WS = "streaming";
        String INTENT_STREAM_SSE = "events";
    }
}
