/*
 *     Copyright (C) 2017 OrionStar Technology Project
 *
 *     Licensed under the Apache License, Version 2.0 (the "License");
 *     you may not use this file except in compliance with the License.
 *     You may obtain a copy of the License at
 *
 *          http://www.apache.org/licenses/LICENSE-2.0
 *
 *     Unless required by applicable law or agreed to in writing, software
 *     distributed under the License is distributed on an "AS IS" BASIS,
 *     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     See the License for the specific language governing permissions and
 *     limitations under the License.
 */

package com.ainirobot.speechasrservice.skill;

import android.content.Context;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.coreservice.listener.IMusicListener;
import com.ainirobot.coreservice.listener.IToneListener;

import com.ainirobot.speechasrservice.kratos.cofig.ClientConfig;
import com.ainirobot.speechasrservice.utils.Singleton;
import com.cm.speech.tts.AudioPlayer;
import com.cm.speech.tts.TTSPlayer;
import com.cm.speech.tts.player.PlayerListener;

import java.io.File;


public class VolumeServer {
    private final static String TAG = VolumeServer.class.getName();

    private AudioPlayer mTonePlayer;
    private static Singleton<VolumeServer> sSingleton = new Singleton<VolumeServer>() {
        @Override
        protected VolumeServer create() {
            return new VolumeServer();
        }
    };
    private boolean isTtsPlaying;
    private TTSPlayer ttsSynthesizer;
    private AudioPlayer mAudioPlayer;
    private String localMusicFilePath = ClientConfig.BASE_LOCAL_PATH + "/LocalMusic/";
    private Context mContext;
    private IToneListener mToneListener;

    private VolumeServer() {
    }

    public static VolumeServer getInstance() {
        return sSingleton.get();
    }

    public void init(Context context) {
        this.mContext = context;
        mTonePlayer = new AudioPlayer.Builder(context, false, false,
                new PlayerListener() {
                    @Override
                    public void onPlayerStart() {
                        super.onPlayerStart();
                        Log.d(TAG, "playToneByLocalPath onPlayerStart");
                        if (null != mToneListener) {
                            try {
                                mToneListener.onStart();
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onPlayerStop() {
                        super.onPlayerStop();
                        Log.d(TAG, "playToneByLocalPath onPlayerStop");
                        if (null != mToneListener) {
                            try {
                                mToneListener.onStop();
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onPlayerError() {
                        super.onPlayerError();
                        Log.d(TAG, "playToneByLocalPath onPlayerError");
                        if (null != mToneListener) {
                            try {
                                mToneListener.onError();
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onPlayerComplete() {
                        super.onPlayerComplete();
                        Log.d(TAG, "playToneByLocalPath onPlayerComplete");
                        if (null != mToneListener) {
                            try {
                                mToneListener.onComplete();
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }).build();
    }

    public void toneToSpeechByLocalPath(String localPath,
                                           IToneListener listener) {
        if (null != mTonePlayer) {
            this.mToneListener = listener;
            mTonePlayer.playMusicByLocalPath(localPath);
        } else {
            Log.e(TAG, "tonePlayer is null");
        }
    }

    public void playMusicByLocalPath(String localPath, boolean looping, boolean enableAudioFocus,
                                        IMusicListener listener) {
        releaseMusicMedia();
        mAudioPlayer = new AudioPlayer.Builder(mContext, looping, enableAudioFocus,
                new PlayerListener() {
                    @Override
                    public void onPlayerStart() {
                        super.onPlayerStart();
                        Log.d(TAG, "playMusicByLocalPath onPlayerStart");
                        if (null != listener) {
                            try {
                                listener.onStart();
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onPlayerResume() {
                        super.onPlayerResume();
                        Log.d(TAG, "playMusicByLocalPath onPlayerResume");
                        if (null != listener) {
                            try {
                                listener.onResume();
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    @Override
                    public void onPlayerPause() {
                        super.onPlayerPause();
                        Log.d(TAG, "playMusicByLocalPath onPlayerPause");
                        try {
                            listener.onPause();
                        } catch (RemoteException e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onPlayerStop() {
                        super.onPlayerStop();
                        Log.d(TAG, "playMusicByLocalPath onPlayerStop");
                        if (null != listener) {
                            try {
                                listener.onStop();
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                        mAudioPlayer.release();
                    }

                    @Override
                    public void onPlayerComplete() {
                        super.onPlayerComplete();
                        Log.d(TAG, "playMusicByLocalPath onPlayerComplete");
                        if (null != listener) {
                            try {
                                listener.onComplete();
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                        mAudioPlayer.release();
                    }

                    @Override
                    public void onPlayerError() {
                        super.onPlayerError();
                        Log.d(TAG, "playMusicByLocalPath onPlayerError");
                        if (null != listener) {
                            try {
                                listener.onError();
                            } catch (RemoteException e) {
                                e.printStackTrace();
                            }
                        }
                        mAudioPlayer.release();
                    }
                }).build();
        mAudioPlayer.playMusicByLocalPath(localPath);
    }

    protected void toneToSpeechByRawId(int rawResourceId,
                                       IToneListener listener) {
        if (null != mTonePlayer) {
            this.mToneListener = listener;
            if (rawResourceId <= 0) {
                if (null != listener) {
                    try {
                        listener.onError();
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                return;
            }
            String path = "android.resource://" + mContext.getPackageName() + File.separator + rawResourceId;
            mTonePlayer.playMusicByLocalPath(path);
        } else {
            Log.e(TAG, "tonePlayer is null");
        }
    }

    private void releaseMusicMedia() {
        if (null != mAudioPlayer) {
            mAudioPlayer.stopPlay();
            mAudioPlayer.release();
            mAudioPlayer = null;
        }
    }

    protected void stopTone() {
        if (null != mTonePlayer) {
            mTonePlayer.stopPlay();
        }
    }

    protected void setTTSIsOnline(boolean isOnline) {
        if (null != ttsSynthesizer) {
            ttsSynthesizer.setTTSIsOnline(isOnline);
        }
    }

    public void cancelDownloadTtsAudio() {
        ttsSynthesizer.cancelDownloadTtsAudio();
    }

    public void stopMusicPlay() {
        releaseMusicMedia();
    }
}
