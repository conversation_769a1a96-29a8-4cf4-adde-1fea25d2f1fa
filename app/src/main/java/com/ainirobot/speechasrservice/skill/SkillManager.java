package com.ainirobot.speechasrservice.skill;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.agent.R;
import com.ainirobot.common.utils.DeviceOS;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.listener.IToneListener;
import com.ainirobot.speechasrservice.data.ConstantDB;
import com.ainirobot.speechasrservice.data.DBOperater;
import com.ainirobot.speechasrservice.data.spokesman.SpokermanServer;
import com.ainirobot.speechasrservice.kratos.cofig.ClientConfig;
import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig;
import com.ainirobot.speechasrservice.socket.AudioInfoManager;
import com.ainirobot.speechasrservice.socket.SocketResponseEntity;
import com.ainirobot.speechasrservice.utils.Constants;
import com.ainirobot.speechasrservice.utils.Executors;
import com.ainirobot.speechasrservice.utils.LogMsg;
import com.ainirobot.speechasrservice.utils.Singleton;
import com.ainirobot.speechasrservice.utils.SpeechConfig;
import com.cm.speech.asr.PinYinScoreJni;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.List;
import java.util.Random;

import kotlin.jvm.functions.Function3;

public class SkillManager {

    private static final String TAG = SkillManager.class.getSimpleName();

    private Context mContext;

    /**
     * 此处是指与audio_wrapper通信的Socket
     */
    private Function3<Integer, byte[], Integer, SocketResponseEntity> write2Socket;

    private boolean isWakeupHintClosed = false;
    private Random random = new Random();

    private static Singleton<SkillManager> sSingleton = new Singleton<SkillManager>() {
        @Override
        protected SkillManager create() {
            return new SkillManager();
        }
    };

    public static SkillManager getInstance() {
        return sSingleton.get();
    }

    public void init(Context context) {
        mContext = context;
        VolumeServer.getInstance().init(context);
        SpokermanServer.getInstance().init(context);
    }

    public void onSocketConnected(Function3<Integer, byte[], Integer, SocketResponseEntity> write2Socket) {
        this.write2Socket = write2Socket;

        String wakeupAlgorithm = EnvironmentConfig.WakeupConfig.getWakeupAlgorithm();
        if (TextUtils.equals(wakeupAlgorithm, SpeechConfig.CONFIG_WAKUP_MULTIPLE_WAVE_NET)) {
            initCustomizedWakeupWord(wakeupAlgorithm);
        }
    }

    public void onSocketDisconnected() {
        write2Socket = null;
    }

    public SocketResponseEntity writeData2Socket(int reqtype, byte[] data, int outTime) {
        if (write2Socket == null) {
            Log.e(TAG, "socket is not ready");
            return null;
        }
        return write2Socket.invoke(reqtype, data, outTime);
    }

    public SocketResponseEntity writeData2Socket(int reqtype, byte[] data) {
        return writeData2Socket(reqtype, data, 0);
    }

    public void onWakeUp(byte[] angleInfo, boolean fromOuter) {
        int wakeupAngle = getWakeupAngle(angleInfo);
        Log.i(TAG, "--wakeup angle: " + wakeupAngle);
        playWakeupTone();
        RobotCore.sendRequest(Definition.REQ_SPEECH_WAKEUP,
                null, String.valueOf(wakeupAngle));
    }

    private int getWakeupAngle(byte[] angleInfo) {
        int wakeupAngle = 0;
        if (null != angleInfo && angleInfo.length > 0) {
            ByteBuffer byteBuffer = ByteBuffer.wrap(angleInfo);
            byteBuffer.order(ByteOrder.nativeOrder());
            wakeupAngle = byteBuffer.getInt();
            wakeupAngle = (wakeupAngle + Constants.WAKEUP_OFFSET) % 360;
        }
        return wakeupAngle;
    }

    private void playWakeupTone() {
        if (!isWakeupHintClosed) {
            List<String> wakeupAudioByCurrentType = SpokermanServer.getInstance()
                                                                   .getWakeupAudioByCurrentType(EnvironmentConfig.TtsConfig.getTtsLanguage(),
                                                                           DeviceOS.INSTANCE.getSpokesman());
            Log.d(TAG, "playWakeupTone: " + wakeupAudioByCurrentType.toString() + "," + DeviceOS.INSTANCE.getSpokesman());
            if (wakeupAudioByCurrentType.size() > 0) {
                toneToSpeechByLocalPath(wakeupAudioByCurrentType
                        .get(random.nextInt(wakeupAudioByCurrentType.size())), null);
            } else {
                String[] stringArray = mContext.getResources()
                                               .getStringArray(ProductInfo.isOverSea() ? R.array.xiaoya_sweet_oversea : R.array.xiaoya_sweet);
                Log.d(TAG, "playWakeupTone: stringArray length = " + stringArray.length);
                int rawResourceId = mContext.getResources().getIdentifier(
                        stringArray[random.nextInt(stringArray.length)],
                        "raw", mContext.getPackageName());
                toneToSpeechByRawId(rawResourceId, null);
            }

        }
    }

    /**
     * 短音频播放
     *
     * @param rawResourceId 短音频资源id
     * @param listener      短音频播放的回调
     */
    public void toneToSpeechByRawId(int rawResourceId,
                                    IToneListener listener) {
        VolumeServer.getInstance().toneToSpeechByRawId(rawResourceId, listener);
    }

    /**
     * 短音频播放
     *
     * @param localPath 短音频本地路径
     * @param listener  短音频播放的回调
     */
    public void toneToSpeechByLocalPath(String localPath, IToneListener listener) {
        VolumeServer.getInstance().toneToSpeechByLocalPath(localPath, listener);
    }

    public void setWakeupHintClosed(boolean isWakeupHintClosed) {
        this.isWakeupHintClosed = isWakeupHintClosed;
    }

    public int getPinYinScore(String pinyin, String separator) {
        String replacePinyin = pinyin.trim().replace(separator,
                " ").trim().replace(" ", ",");
        int score = PinYinScoreJni.GetPinYinScore(replacePinyin);
        Log.d(TAG, "getPinYinScore:" + replacePinyin + ", score: " + score);
        return score;
    }

    public String queryPinYinFromChinese(String chinese) {
        Log.d(TAG, "queryPinYinFromChinese: " + chinese);
        return DBOperater.queryWakeupWordSpellFormDb(mContext, chinese);
    }

    public String queryPinYinMappingTable(String spell) {
        Log.d(TAG, "queryPinYinMappingTable");
        return DBOperater.queryWordMappingFormDb(mContext, spell);
    }

    public String queryUserSetWakeUpWord() {
        Log.d(TAG, "queryUserSetWakeUpWord");
        return DBOperater.queryUserSetWord(mContext, ConstantDB.WordUser.FIELD_WORD_MULTI);
    }

    public int closeCustomizeWakeUpWord() {
        Log.d(TAG, "closeCustomizeWakeUpWord");
        DBOperater.deleteUserSetWord(mContext);
        String wakeupAlgorithm = EnvironmentConfig.WakeupConfig.getWakeupAlgorithm();
        int code;
        if (TextUtils.equals(wakeupAlgorithm, SpeechConfig.CONFIG_WAKUP_MULTIPLE_WAVE_NET)) {
            SocketResponseEntity socketResponseEntity =
                    writeData2Socket(ClientConfig.REQTYPE_WAKEUP_DEL_ALL_KEYWORD, new byte[0], 200);
            code = socketResponseEntity != null ? socketResponseEntity.getErrorNum() : -1;
        } else {
//            code = AsrControllerImpl.getInstance().getASRController().closeCustomizeWakeUpWord();
            //目前都是多通道wavenet
            code = -1;
        }
        if (code == 0) {
            Log.d(TAG, "closeCustomizeWakeUpWord succeed ");
            initCustomizedWakeupWord(wakeupAlgorithm);
        }
        return code;
    }


    /**
     * "小豹小豹"是主唤醒词
     * 该方法是初始化的副唤醒词(即为未设置自定义唤醒词时的副唤醒词)
     * <p>
     * setCustomizeWakeUpWord
     *
     * @param wakeupAlgorithm
     */
    private void initCustomizedWakeupWord(String wakeupAlgorithm) {
        Executors.getBgHandler().post(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "initCustomizedWakeupWord");
                String userSetWord = DBOperater.queryUserSetWord(mContext, ConstantDB.WordUser.FIELD_WORD_SPELL);
                String systemWord = RobotSettingApi.getInstance()
                                                   .getRobotString("robot_setting_custom_wakeup_word");
                if (!TextUtils.isEmpty(systemWord) && TextUtils.isEmpty(userSetWord)) {
                    int code = setCustomizeWakeUpWord(wakeupAlgorithm, systemWord);
                    Log.d(TAG, "systemWord:" + systemWord + ", code: " + code);
                }
                if (!TextUtils.isEmpty(userSetWord)) {
                    // 如果当前交互设置了自定义唤醒词，则使用交互设置的。
                    int code = setCustomizeWakeUpWord(wakeupAlgorithm, userSetWord);
                    Log.d(TAG, "userSetWord:" + userSetWord + ", code: " + code);
                } else {
                    // 如果当前交互未设置自定义唤醒词，则使用default的副唤醒词。
                    // 注意！目前增加多个自定义,唤醒词会导致asr错误重启，暂时只保留核心的一个default唤醒词
                    String mPresetChineseWord = "";
                    String mPresetDefaultWakeUpWordSpell = "";
                    if (ProductInfo.isDeliveryOverSea()) {
                        mPresetDefaultWakeUpWordSpell = "oo:ou k:ei l:a k:ei";
                        mPresetChineseWord = Constants.SAIPH_PRESET_DEFAULT_CHINESE_WORD;
                        int code = setCustomizeWakeUpWord(wakeupAlgorithm, mPresetDefaultWakeUpWordSpell);
                        Log.d(TAG, "saiph mPresetDefaultWakeUpWords　:" + mPresetDefaultWakeUpWordSpell + ", code: " + code);
                    } else if (ProductInfo.isMiniOverSea()) {
                        mPresetDefaultWakeUpWordSpell = "oo:ou k:ei m:i n:i";
                        mPresetChineseWord = Constants.MINI_PRESET_DEFAULT_CHINESE_WORD;
                        int code = setCustomizeWakeUpWord(wakeupAlgorithm, mPresetDefaultWakeUpWordSpell);
                        Log.d(TAG, "mini mPresetDefaultWakeUpWords　:" + mPresetDefaultWakeUpWordSpell + ", code: " + code);
                    } else {
                        // do nothing
                    }
                }
            }
        });
    }

    private int setCustomizeWakeUpWord(String wakeupAlgorithm, String wakeUpWordSpell) {
        Log.d(TAG, "setCustomizeWakeUpWord " + wakeUpWordSpell);
        int code;
        if (TextUtils.equals(wakeupAlgorithm, SpeechConfig.CONFIG_WAKUP_MULTIPLE_WAVE_NET)) {
            byte[] data = wakeUpWordSpell.replaceAll(":", " ").getBytes();
            SocketResponseEntity socketResponseEntity = writeData2Socket(
                    ClientConfig.REQTYPE_WAKEUP_SET_KEYWORD, data, 200);
            code = socketResponseEntity != null ? socketResponseEntity.getErrorNum() : -1;
        } else {
            //目前只支持多通道wavenet
            code = -1;
        }
        return code;
    }


    public int setCustomizeWakeUpWord(String wakeUpWordChinese, String wakeUpWordSpell,
                                      String separator) {
        wakeUpWordChinese = wakeUpWordChinese.replace(separator, "");
        wakeUpWordSpell = wakeUpWordSpell.replace(separator, " ").trim();
        String wakeupAlgorithm = EnvironmentConfig.WakeupConfig.getWakeupAlgorithm();
        int code = setCustomizeWakeUpWord(wakeupAlgorithm, wakeUpWordSpell);
        if (code == 0) {
            DBOperater.saveUserSetWord(mContext, wakeUpWordChinese, wakeUpWordSpell);
        }
        return code;
    }

}
