package com.ainirobot.speechasrservice

import com.ainirobot.coreservice.ISkillCallback
import com.ainirobot.coreservice.ISkillServerCheckListener
import com.ainirobot.coreservice.config.SceneEntity
import com.ainirobot.coreservice.listener.IMusicListener
import com.ainirobot.coreservice.listener.ITextListener
import com.ainirobot.coreservice.listener.IToneListener

interface ISpeechDelegate {

    fun registerCallBack(cb: ISkillCallback?)

    fun unregisterCallBack(cb: ISkillCallback?)

    fun playText(text: String?, listener: ITextListener?)

    fun playTone(type: String?, listener: IToneListener?)

    fun stopTTS()

    fun setRecognizeMode(isContinue: Boolean)
    
    fun setASREnabled(enable: Boolean)

    fun setRecognizable(enable: Boolean)
    
    fun queryByText(text: String?)

    fun getActiveAsk(properType: String?, robotProperJson: String?)

    fun cancleAudioOperation()

    fun setWakeupHintClosed(isWakeupHintClosed: Boolean)

    fun setAngleCenterRange(angle_center: Float, angle_range: Float)

    fun setTTSParams(ttsType: String?, value: Int)

    fun setMultipleModeEnable(enale: Boolean)

    fun setCustomizeWakeUpWord(
        wakeUpWordChinese: String?,
        wakeUpWordSpell: String?,
        separator: String?
    ): Int

    fun closeCustomizeWakeUpWord(): Int

    fun getPinYinScore(pinyin: String?, separator: String?): Int

    fun queryPinYinFromChinese(chineseWord: String?): String

    fun queryPinYinMappingTable(pinyin: String?): String

    fun queryUserSetWakeUpWord(): String

    fun setLangRec(autoLangJson: String?)

    fun registerServerCheck(Listener: ISkillServerCheckListener?)

    fun unregisterServerCheck()

    fun playToneByLocalPath(localPath: String?, listener: IToneListener?)

    fun stopTone()

    fun switchScene(scene: SceneEntity?)

    fun setAsrExtendProperty(propertyJson: String?): Boolean

    fun setASRParams(asrType: String?, value: String?)

    fun onCreate(app_id: String?)

    fun onForeground(app_id: String?)

    fun onBackground(app_id: String?)

    fun onDestroy(app_id: String?)

    fun setVersion(app_id: String?, app_version: String?)

    fun setPath(app_id: String?, path: String?)

    fun sendAgentMessage(type: String?, code: Int, message: String?)

    fun setSyncReportCustomNlpData(app_id: String?, data: String?)

    fun setAsyncReportCustomNlpData(app_id: String?, data: String?)

    fun resetNlpState()

    fun setServerApp(appList: MutableList<String>?)

    fun setDebug(value: Boolean)

    fun setSyncCustomNlpData(map: MutableMap<Any?, Any?>?)

    fun setAsyncCustomNlpData(opt: String?, data: String?): String

    fun getTtsPlayStatus(): Int

    fun downloadTtsAudio(ttsEntitiesJson: String?)

    fun getSpokemanListByLanguage(lang: String?): String

    fun playMusicByLocalPath(
        localPath: String?,
        looping: Boolean,
        enableAudioFocus: Boolean,
        listener: IMusicListener?
    )

    fun stopMusicPlay()

    fun closeStreamDataReceived(statusJson: String?)

    fun isRecognizeContinue(): Boolean

    fun isRecognizable(): Boolean

    fun setRecognizeModeForce(isContinue: Boolean)

    fun setRecognizeModeNew(isContinue: Boolean, isCloseStreamData: Boolean)

    fun onAgentActionFinish(action: String, code: Int, message: String?)

    fun onAgentActionState(action: String, state: Int, data: String?)

    fun queryByTextWithThinking(text: String?, isShowThinking: Boolean)
}