package com.ainirobot.speechasrservice.net;


import android.support.annotation.IntDef;
import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.util.Log;
import android.util.SparseArray;

import com.ainirobot.common.Config;
import com.ainirobot.speechasrservice.utils.Constants;
import com.ainirobot.speechasrservice.utils.SecureUtils;
import com.ainirobot.speechasrservice.utils.Singleton;

import java.io.IOException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Observable;
import java.util.concurrent.TimeUnit;

import okhttp3.ConnectionSpec;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;


@Keep
public class OkHttpClientMgr extends Observable {
    private static final String TAG = OkHttpClientMgr.class.getSimpleName();
    private static final long READ_TIMEOUT = 20;
    private static final long WRITE_TIMEOUT = 20;
    private static final long CONNECT_TIMEOUT = 20;

    private static Singleton<OkHttpClientMgr> singleton = new Singleton<OkHttpClientMgr>() {
        @Override
        protected OkHttpClientMgr create() {
            return new OkHttpClientMgr();
        }
    };

    private SparseArray<OkHttpClient> mClients = new SparseArray<>();

    private OkHttpClientMgr() {
    }

    public static OkHttpClientMgr getInstance() {
        return singleton.get();
    }


    public OkHttpClient initClient(@Client int client) {
        OkHttpClient.Builder defaultBuilder = null;
        if (client == CLIENT_SSL) {
            try {
                defaultBuilder = new OkHttpClient.Builder().sslSocketFactory
                        (SecureUtils.getSSLSocketFactory(Config.INSTANCE.getContext().getAssets()
                                .open(Constants.KEYSTORE_FILE), Constants.KEYSTORE_PASS));
            } catch (IOException e) {
                e.printStackTrace();
                Log.e(TAG, "SSL is error");
                defaultBuilder = new OkHttpClient.Builder();
            }
        } else {
            defaultBuilder = new OkHttpClient.Builder();
        }
        OkHttpClient httpClient = createClient(defaultBuilder);
        mClients.put(client, httpClient);
        return httpClient;
    }

    private OkHttpClient createClient(OkHttpClient.Builder builder) {
        builder.retryOnConnectionFailure(false);
        builder.connectionSpecs(Arrays.asList(ConnectionSpec.COMPATIBLE_TLS,ConnectionSpec.CLEARTEXT,ConnectionSpec.MODERN_TLS));
        builder.readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)//设置读取超时时间
                .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)//设置写的超时时间
                .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS);//设置连接超时时间
        return builder.build();
    }

    class NetTimeoutInterceptor implements Interceptor {

        public static final String TAG = "NetWorkLogger";

        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            Response response = null;
            try {
                response = chain.proceed(request);
            } catch (IOException e) {
                if ((e instanceof SocketTimeoutException) || (e instanceof ConnectException)) {
                    Log.d(TAG, "Interceptor get request timeout !!!");
                    //执行更换IP逻辑
                    setChanged();
                    notifyObservers();
                }
            }
            if (response == null) {
                throw new IOException("request timeout，response is null ");
            }
            return response;
        }

    }


    public
    @NonNull
    OkHttpClient getClient(@Client int client) {
        OkHttpClient httpClient = mClients.get(client);
        if (httpClient == null) {
            return initClient(client);
        }
        return httpClient;
    }

    public static final int CLIENT_DEFAULT = 0;
    public static final int CLIENT_SSL = 1;

    @IntDef({CLIENT_DEFAULT, CLIENT_SSL})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Client {
    }

    public static Object create(Class classes, @Client int client) {
        return new Retrofit.Builder()
                .baseUrl("https://speech-bxm.ainirobot.com/")
                .addConverterFactory(GsonConverterFactory.create())
                .client(singleton.get().getClient(client))
                .build().create(classes);
    }

    public static Object create(String baseUrl, Class classes, @Client int client) {
        return new Retrofit.Builder()
                .baseUrl(baseUrl)
                .addConverterFactory(GsonConverterFactory.create())
                .client(singleton.get().getClient(client))
                .build().create(classes);
    }

    public static Object createString(String baseUrl, Class classes, @Client int client) {
        return new Retrofit.Builder()
                .baseUrl(baseUrl)
                .addConverterFactory(ToStringConvertFactory.create())
                .client(singleton.get().getClient(client))
                .build().create(classes);
    }

    public static Object createString(Class classes, @Client int client) {
        return new Retrofit.Builder()
                .baseUrl("https://speech-bxm.ainirobot.com/")
                .addConverterFactory(ToStringConvertFactory.create())
                .client(singleton.get().getClient(client))
                .build().create(classes);
    }
}
