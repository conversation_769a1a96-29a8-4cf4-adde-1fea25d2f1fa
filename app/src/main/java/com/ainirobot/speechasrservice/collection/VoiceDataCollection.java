package com.ainirobot.speechasrservice.collection;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.os.Binder;
import android.os.RemoteException;

import com.ainirobot.app.MainApp;
import com.ainirobot.common.utils.DeviceOS;
import com.ainirobot.common.utils.KLog;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingListener;
import com.ainirobot.coreservice.utils.DelayTask;

import com.ainirobot.speech.IVoiceCallback;
import com.ainirobot.speech.IVoiceData;
import com.ainirobot.speechasrservice.kratos.cofig.AsrExpendInfo;
import com.ainirobot.speechasrservice.multiplemode.SoundInfo;
import com.ainirobot.speechasrservice.multiplemode.SoundInfoTask;
import com.ainirobot.speechasrservice.socket.AudioInfoManager;
import com.ainirobot.speechasrservice.socket.audiodata.Recorder;
import com.ainirobot.speechasrservice.socket.audiodata.RecorderCallback;
import com.ainirobot.speechasrservice.utils.LogMsg;
import com.cm.speech.warpper.ASRController;
import com.cm.speech.warpper.ASRManager;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Collections;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import android.os.Process;
import android.util.Log;

public class VoiceDataCollection extends IVoiceData.Stub {
    private static final String TAG = "VoiceDataCollection";
    private static final String ACTION = "com.ainirobot.speech.voicedata";

    private static final String ACTION2 = "com.ainirobot.speech.voicedata.coreaudio";

    public static final String ROBOT_SETTING_VOICE_MODE = "robot_setting_voice_mode";
    public static final String ROBOT_SETTING_NOT_INTERRUPT_SWITCH = "robot_setting_not_interrupt_switch";

    private final Recorder recorder = Recorder.getInstance();
    private IVoiceCallback mCallback;
    private Timer angleTimer;
    private int angleInterval = 1000;
    private int index = 1;
    private boolean enableFilterData = false;
    private boolean isRecording = false;

    private int speakerId = -1;
    private boolean isSpeaking = false;

    // Singleton instance
    private static VoiceDataCollection instance;

    private VoiceDataCollectionProxyCallback mProxyCallback;

    // Private constructor for Singleton pattern
    private VoiceDataCollection() {
        AudioInfoManager.getInstance().init();
    }

    // Singleton access method
    public static synchronized VoiceDataCollection getInstance() {
        if (instance == null) {
            instance = new VoiceDataCollection();
        }
        return instance;
    }

    public void onCoreConnected() {
        //AI智能语音交互
        setFilterDataEnable();

        RobotSettingApi.getInstance().registerRobotSettingListener(new RobotSettingListener() {

            @Override
            public void onRobotSettingChanged(String key) {
                setFilterDataEnable();
            }
        }, ROBOT_SETTING_VOICE_MODE);

        //启用云端Vad
        setServerVadEnable();
        RobotSettingApi.getInstance().registerRobotSettingListener(new RobotSettingListener() {
            @Override
            public void onRobotSettingChanged(String key) {
                setServerVadEnable();
            }
        }, ROBOT_SETTING_NOT_INTERRUPT_SWITCH);
    }

    public static boolean match(Intent intent) {
        LogMsg.print(TAG, "Match intent action: " + intent.getAction());
        if (ACTION.equals(intent.getAction()) || ACTION2.equals(intent.getAction())) {
            LogMsg.print(TAG, "Match intent action: true");
            return true;
        }else {
            LogMsg.print(TAG, "Match intent action: false");
            return false;
        }
    }

    public void onAsrStart() {
        DelayTask.cancel(this);
        DelayTask.submit(this, new Runnable() {
            @Override
            public void run() {
                LogMsg.print(TAG, "OnAsrStart set filter data enable : " + enableFilterData);
                ASRController asrController = ASRManager.getInstance().getASRController();
                if (asrController != null) {
                    asrController.setFilterDataEnable(enableFilterData);
                }
            }
        }, 1000, 5000);
    }

    public void setProxyCallback(VoiceDataCollectionProxyCallback callback) {
        mProxyCallback = callback;
    }

    public void onSpeechBegin(String sid) {
        LogMsg.print(TAG, "On speech begin : " + sid + " ,instance : " + instance + " ,process : " + getProcessName());
        isSpeaking = true;
        speakerId = -1;
        SoundInfo soundInfo = SoundInfoTask.getInstance().getSoundAngle(index);
        LogMsg.print(TAG, "Get sound info on speechbegin, soundInfo :" + soundInfo + " ,mCallback : " + this.mCallback);
        if (soundInfo != null) {
            LogMsg.print(TAG, "Get sound info on vadbegin, index :" + soundInfo.getIndex() + " vad : " + soundInfo.getActive() + " , angle : " + soundInfo.getAudio_angle() + " , envAngle : " + soundInfo.getEnv_angle());
            if (this.mCallback != null) {
                try {
                    this.mCallback.onAngleUpdate(soundInfo.getActive(), soundInfo.getAudio_angle(), soundInfo.getEnv_angle());
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        }

        if (this.mCallback != null) {
            try {
                this.mCallback.onVadBegin(sid);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    public void onSpeechEnd(String sid) {
        LogMsg.print(TAG, "On speech end : " + " ,instance : " + instance);
        isSpeaking = false;
        reportSpeakerId();
        if (mCallback != null) {
            try {
                mCallback.onVadEnd(sid);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

//    @Subscriber(tag = EventBusTag.SPEECH_FINAL_RESULT)
    public void onSpeechResult(String sid, String userText) {
        if (mCallback == null) {
            return;
        }
        try {
//            String sid = bundle.getString("sid", "");
//            String userText = bundle.getString("userText", "");
            mCallback.onSpeechResult(sid, userText, "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    @Subscriber(tag = EventBusTag.SPEECH_FINAL_ERROR)
    public void onSpeechError(String sid, int errorCode, String errorInfo) {
        if (mCallback == null) {
            return;
        }
//        int errorCode = bundle.getInt(AsrException.EXCEPTION_ID, 0);
//        String errorInfo = bundle.getString(AsrException.EXCEPTION_KEY, "");
//        String sid = bundle.getString(AsrException.EXCEPTION_SID, "");

        try {
            mCallback.onSpeechResult(sid, "Error: " + errorCode + ", Info: " + errorInfo, "");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }


    @Override
    public void setAngleInterval(int time) throws RemoteException {
        angleInterval = time;
        if (mCallback != null) {
            startAngleReport();
        }
    }

    @Override
    public void startRecord(String path) throws RemoteException {
        KLog.INSTANCE.d("Start record path: " + path, TAG);
        LogMsg.print(TAG, "Start record");
        long identity = Binder.clearCallingIdentity();
        try {
            isRecording = true;
            recorder.limitFileSize(false);
            recorder.startRecord(path, new RecorderCallback() {
                @Override
                public void onError(int errcode, String info) {
                    KLog.INSTANCE.d("Start record failed, error : " + errcode + ", info : " + info, TAG);
                }

                @Override
                public void onFilesSaveEnded(String[] dumpFileNames) {
                    KLog.INSTANCE.d("Start record succeed", TAG);
                }
            });
            startAngleReport();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            Binder.restoreCallingIdentity(identity);
        }
    }

    @Override
    public void stopRecord() throws RemoteException {
        long identity = Binder.clearCallingIdentity();
        try {
            isRecording = false;
            recorder.limitFileSize(true);
            recorder.stopRecord();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            Binder.restoreCallingIdentity(identity);
        }
    }

    @Override
    public void setCallback(IVoiceCallback callback) throws RemoteException {
        LogMsg.print(TAG, "Set callback: " +callback + " ,instance : " + instance + " ,process : " + getProcessName());
        this.mCallback = callback;
        LogMsg.print(TAG, "Set callback this.mCallback: " +this.mCallback);
        startAngleReport();
        mCallback.onFilterVadDataEnable(enableFilterData);
    }

    private String getProcessName() {
        int pid = Process.myPid();
        ActivityManager am = (ActivityManager) MainApp.getInstance().getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);

        if (am != null) {
            for (ActivityManager.RunningAppProcessInfo processInfo : am.getRunningAppProcesses()) {
                if (processInfo.pid == pid) {
                    return processInfo.processName;
                }
            }
        }
        return null;
    }

    @Override
    public Map<String, String> getVoiceAngle() throws RemoteException {
        return Collections.emptyMap();
    }

    private void setFilterDataEnable() {
        boolean enable = DeviceOS.INSTANCE.getEnableWakeUpFreeBySetting();
        Log.d(TAG, "Set filter data enable : " + enable);
        enableFilterData = enable;
        setServerVadEnable();
        ASRController asrController = ASRManager.getInstance().getASRController();
        if (asrController != null) {
            asrController.setFilterDataEnable(enable);
        }
        startAngleReport();

        if (mCallback != null) {
            try {
                mCallback.onFilterVadDataEnable(enable);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    private void setServerVadEnable() {
        boolean enable = RobotSettingApi.getInstance()
                .getRobotInt(ROBOT_SETTING_NOT_INTERRUPT_SWITCH) == 1;
        Log.d(TAG, "Set server vad enable : " + enable);
        AsrExpendInfo.setServerVad(enable && enableFilterData);  //云端vad目前只配合免唤醒使用
    }

    @Override
    public void filterVadData(String sid, boolean filter, int speakerId) throws RemoteException {
        LogMsg.print(TAG, "Filter data, sid: " + sid + ", filter: " + filter + ", speakId: " + speakerId);
        AsrExpendInfo.setSpeakerId(speakerId);
        if (mProxyCallback != null) {
            mProxyCallback.filterVadData(sid, filter, speakerId);
        }
//        ASRController asrController = ASRManager.getInstance().getASRController();
//        if (asrController != null) {
//            asrController.filterVadData(sid, filter);
//        }
        this.speakerId = speakerId;
        reportSpeakerId();
    }

    private void reportSpeakerId() {
        JSONObject json = new JSONObject();
        try {
            json.put("speakerId", speakerId);
            json.put("speaking", isSpeaking);
            RobotCore.sendStatusReport(RobotOS.SPEECH_SERVICE, Definition.STATUS_SPEAKER, json.toString());
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }

    private void startAngleReport() {
        stopAngleReport();
        angleTimer = new Timer();
        angleTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!isRecording && !enableFilterData) {
                    stopAngleReport();
                }

                SoundInfo soundInfo = SoundInfoTask.getInstance().getSoundAngle(index);
                if (soundInfo != null) {
                    LogMsg.print(TAG, "Get sound info, index :" + soundInfo.getIndex() + " vad : " + soundInfo.getActive() + " , angle : " + soundInfo.getAudio_angle() + " , envAngle : " + soundInfo.getEnv_angle());
                    if (mCallback != null) {
                        try {
                            mCallback.onAngleUpdate(soundInfo.getActive(), soundInfo.getAudio_angle(), soundInfo.getEnv_angle());
                        } catch (RemoteException e) {
                            e.printStackTrace();
                            onBinderDied();
                        }
                    }
                }
            }
        }, 0, angleInterval);
    }

    private void stopAngleReport() {
        if (angleTimer != null) {
            angleTimer.cancel();
            angleTimer = null;
        }
    }

    private void onBinderDied() {
        stopAngleReport();
        try {
            stopRecord();
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }
}
