package com.ainirobot.speechasrservice.multiplemode;

import android.util.Log;


import com.ainirobot.speechasrservice.kratos.cofig.ClientConfig;
import com.ainirobot.speechasrservice.skill.SkillManager;
import com.ainirobot.speechasrservice.socket.SocketResponseEntity;
import com.ainirobot.speechasrservice.utils.Constants;
import com.ainirobot.speechasrservice.utils.LogMsg;
import com.ainirobot.speechasrservice.utils.Singleton;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class SoundInfoTask {

    private static final String TAG = SoundInfoTask.class.getSimpleName();
    public static final int INVALID_ANGLE = -1;

    private static Singleton<SoundInfoTask> sSingleton = new Singleton<SoundInfoTask>() {
        @Override
        protected SoundInfoTask create() {
            return new SoundInfoTask();
        }
    };


    private SoundInfoTask() {
    }

    public static SoundInfoTask getInstance() {
        return sSingleton.get();
    }

    public void init() {

    }

    public SoundInfo getSoundAngle(int index) {
        if (index != 0) {
            SocketResponseEntity socketResponseEntity = SkillManager.getInstance()
                    .writeData2Socket(ClientConfig.REQTYPE_BEAM_INFO, new byte[0]);
            if (null != socketResponseEntity && socketResponseEntity.getErrorNum() == 0) {
                byte[] recognizerAngle = socketResponseEntity.getBodyDatas();
                ByteBuffer byteBuffer = ByteBuffer.wrap(recognizerAngle);
                byteBuffer.order(ByteOrder.LITTLE_ENDIAN);
                try {
                    //这个获取值必须写，也可以按照指定位置直接获取拾音角度
                    int active = byteBuffer.getInt();
                    int audio_angle = byteBuffer.getInt();
                    int beam_angle = byteBuffer.getInt();
                    int env_angle = byteBuffer.getInt();
                    LogMsg.print(TAG, "active:" + active + ",audio_angle:" + audio_angle
                            + ",beam_angle:" + beam_angle);
                    SoundInfo soundInfo = new SoundInfo(index, active, System.currentTimeMillis());
                    if (audio_angle >= 0 && audio_angle <= 360) {
                        audio_angle = (audio_angle + Constants.WAKEUP_OFFSET) % 360;
                        soundInfo.setAudio_angle(audio_angle);
                    } else {
                        soundInfo.setAudio_angle(INVALID_ANGLE);
                    }
                    if (beam_angle >= 0 && beam_angle <= 360) {
                        beam_angle = (beam_angle + Constants.WAKEUP_OFFSET) % 360;
                        soundInfo.setBeam_angle(beam_angle);
                    } else {
                        soundInfo.setBeam_angle(INVALID_ANGLE);
                    }

                    if (beam_angle >= 0 && beam_angle <= 360) {
                        env_angle = (env_angle + Constants.WAKEUP_OFFSET) % 360;
                        soundInfo.setEnv_angle(env_angle);
                    } else {
                        soundInfo.setEnv_angle(INVALID_ANGLE);
                    }
                    return soundInfo;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                LogMsg.print(TAG, "socketResponseEntity : " + socketResponseEntity);
                if (socketResponseEntity != null) {
                    LogMsg.print(TAG, "socketResponseEntity : " + socketResponseEntity.getErrorNum());
                }
            }
        } else {
            LogMsg.print(TAG, "index is 0");
        }
        return null;
    }
}