package com.ainirobot.speechasrservice.multiplemode;

public class SoundInfo {

    private int index;
    private int active;
    private int audio_angle;
    private int beam_angle;
    private int env_angle;
    private long time;

    public SoundInfo(int index, int active, long time) {
        this.index = index;
        this.active = active;
        this.time = time;
    }

    SoundInfo(int index, int active, int audio_angle, int beam_angle, long time) {
        this.index = index;
        this.active = active;
        this.audio_angle = audio_angle;
        this.beam_angle = beam_angle;
        this.time = time;
    }

    public void setAudio_angle(int audio_angle) {
        this.audio_angle = audio_angle;
    }

    public void setBeam_angle(int beam_angle) {
        this.beam_angle = beam_angle;
    }

    public int getIndex() {
        return index;
    }

    public int getActive() {
        return active;
    }

    public int getAudio_angle() {
        return audio_angle;
    }

    public int getBeam_angle() {
        return beam_angle;
    }

    public long getTime() {
        return time;
    }

    @Override
    public String toString() {
        return "SoundInfo{" +
                "index=" + index +
                ", active=" + active +
                ", audio_angle=" + audio_angle +
                ", beam_angle=" + beam_angle +
                ", time=" + time +
                '}';
    }

    public int getEnv_angle() {
        return env_angle;
    }

    public void setEnv_angle(int env_angle) {
        this.env_angle = env_angle;
    }
}
