package com.ainirobot.speechasrservice

import android.os.Handler
import android.os.Looper
import com.ainirobot.coreservice.ISkill
import com.ainirobot.coreservice.ISkillCallback
import com.ainirobot.coreservice.ISkillServerCheckListener
import com.ainirobot.coreservice.config.SceneEntity
import com.ainirobot.coreservice.listener.IMusicListener
import com.ainirobot.coreservice.listener.ITextListener
import com.ainirobot.coreservice.listener.IToneListener

/**
 * SpeechService的代理类
 */
class SpeechDelegate : ISpeechDelegate {

    /**
     * AgentService的Binder
     */
    @Volatile
    var impl: ISkill? = null
        set(value) {
            field = value?.also {
                it.registerCallBack(callbackBinder)
                it.registerServerCheck(serverCheckBinder)
            }
        }

    private val callbackBinder = SkillCallbackBinder()
    private val serverCheckBinder = SkillServerCheckBinder()

    private val handler = Handler(Looper.getMainLooper())

    override fun registerCallBack(cb: ISkillCallback?) {
        callbackBinder.callback = cb
    }

    override fun unregisterCallBack(cb: ISkillCallback?) {
        callbackBinder.callback = null
    }

    override fun playText(text: String?, listener: ITextListener?) {
        impl?.playText(text, PlayTextBinder(text, handler, listener))
    }

    override fun playTone(type: String?, listener: IToneListener?) {
        impl?.playTone(type, PlayToneBinder(handler, listener))
    }

    override fun stopTTS() {
        impl?.stopTTS()
    }

    override fun setRecognizeMode(isContinue: Boolean) {
        impl?.setRecognizeMode(isContinue)
    }

    override fun setASREnabled(enable: Boolean) {
        impl?.setASREnabled(enable)
    }

    override fun setRecognizable(enable: Boolean) {
        impl?.isRecognizable = enable
    }

    override fun queryByText(text: String?) {
        impl?.queryByText(text)
    }

    override fun getActiveAsk(properType: String?, robotProperJson: String?) {
        impl?.getActiveAsk(properType, robotProperJson)
    }

    override fun cancleAudioOperation() {
        impl?.cancleAudioOperation()
    }

    override fun setWakeupHintClosed(isWakeupHintClosed: Boolean) {
        impl?.setWakeupHintClosed(isWakeupHintClosed)
    }

    override fun setAngleCenterRange(angle_center: Float, angle_range: Float) {
        impl?.setAngleCenterRange(angle_center, angle_range)
    }

    override fun setTTSParams(ttsType: String?, value: Int) {
        impl?.setTTSParams(ttsType, value)
    }

    override fun setMultipleModeEnable(enale: Boolean) {
        impl?.setMultipleModeEnable(enale)
    }

    override fun setCustomizeWakeUpWord(
        wakeUpWordChinese: String?,
        wakeUpWordSpell: String?,
        separator: String?
    ): Int {
        return impl?.setCustomizeWakeUpWord(wakeUpWordChinese, wakeUpWordSpell, separator) ?: 0
    }

    override fun closeCustomizeWakeUpWord(): Int {
        return impl?.closeCustomizeWakeUpWord() ?: 0
    }

    override fun getPinYinScore(pinyin: String?, separator: String?): Int {
        return impl?.getPinYinScore(pinyin, separator) ?: 0
    }

    override fun queryPinYinFromChinese(chineseWord: String?): String {
        return impl?.queryPinYinFromChinese(chineseWord) ?: ""
    }

    override fun queryPinYinMappingTable(pinyin: String?): String {
        return impl?.queryPinYinMappingTable(pinyin) ?: ""
    }

    override fun queryUserSetWakeUpWord(): String {
        return impl?.queryUserSetWakeUpWord() ?: ""
    }

    override fun setLangRec(autoLangJson: String?) {
        impl?.setLangRec(autoLangJson)
    }

    class SkillServerCheckBinder : ISkillServerCheckListener.Stub() {

        var listener: ISkillServerCheckListener? = null

        override fun onDelay(timeMilliSeconds: Int) {
            listener?.onDelay(timeMilliSeconds)
        }

        override fun onError(message: String?) {
            listener?.onError(message)
        }

        override fun onSuccess(timeMilliSeconds: Int) {
            listener?.onSuccess(timeMilliSeconds)
        }
    }

    override fun registerServerCheck(Listener: ISkillServerCheckListener?) {
        serverCheckBinder.listener = Listener
    }

    override fun unregisterServerCheck() {
        serverCheckBinder.listener = null
    }

    override fun playToneByLocalPath(localPath: String?, listener: IToneListener?) {
        impl?.playToneByLocalPath(localPath, PlayToneBinder(handler, listener))
    }

    override fun stopTone() {
        impl?.stopTone()
    }

    override fun switchScene(scene: SceneEntity?) {
        impl?.switchScene(scene)
    }

    override fun setAsrExtendProperty(propertyJson: String?): Boolean {
        return impl?.setAsrExtendProperty(propertyJson) ?: false
    }

    override fun setASRParams(asrType: String?, value: String?) {
        impl?.setASRParams(asrType, value)
    }

    override fun onCreate(app_id: String?) {
        impl?.onCreate(app_id)
    }

    override fun onForeground(app_id: String?) {
        impl?.onForeground(app_id)
    }

    override fun onBackground(app_id: String?) {
        impl?.onBackground(app_id)
    }

    override fun onDestroy(app_id: String?) {
        impl?.onDestroy(app_id)
    }

    override fun setVersion(app_id: String?, app_version: String?) {
        impl?.setVersion(app_id, app_version)
    }

    override fun setPath(app_id: String?, path: String?) {
        impl?.setPath(app_id, path)
    }

    override fun sendAgentMessage(type: String?, code: Int, message: String?) {
        impl?.sendAgentMessage(type, code, message)
    }

    override fun setSyncReportCustomNlpData(app_id: String?, data: String?) {
        impl?.setSyncReportCustomNlpData(app_id, data)
    }

    override fun setAsyncReportCustomNlpData(app_id: String?, data: String?) {
        impl?.setAsyncReportCustomNlpData(app_id, data)
    }

    override fun resetNlpState() {
        impl?.resetNlpState()
    }

    override fun setServerApp(appList: MutableList<String>?) {
        impl?.setServerApp(appList)
    }

    override fun setDebug(value: Boolean) {
        impl?.setDebug(value)
    }

    override fun setSyncCustomNlpData(map: MutableMap<Any?, Any?>?) {
        impl?.setSyncCustomNlpData(map)
    }

    override fun setAsyncCustomNlpData(opt: String?, data: String?): String {
        return impl?.setAsyncCustomNlpData(opt, data) ?: ""
    }

    override fun getTtsPlayStatus(): Int {
        return impl?.ttsPlayStatus ?: 0
    }

    override fun downloadTtsAudio(ttsEntitiesJson: String?) {
        impl?.downloadTtsAudio(ttsEntitiesJson)
    }

    override fun getSpokemanListByLanguage(lang: String?): String {
        return impl?.getSpokemanListByLanguage(lang) ?: ""
    }

    override fun playMusicByLocalPath(
        localPath: String?,
        looping: Boolean,
        enableAudioFocus: Boolean,
        listener: IMusicListener?
    ) {
        impl?.playMusicByLocalPath(localPath, looping, enableAudioFocus, PlayMusicBinder(handler, listener))
    }

    override fun stopMusicPlay() {
        impl?.stopMusicPlay()
    }

    override fun closeStreamDataReceived(statusJson: String?) {
        impl?.closeStreamDataReceived(statusJson)
    }

    override fun isRecognizeContinue(): Boolean {
        return impl?.isRecognizeContinue ?: false
    }

    override fun isRecognizable(): Boolean {
        return impl?.isRecognizable ?: false
    }

    override fun setRecognizeModeForce(isContinue: Boolean) {
        impl?.setRecognizeModeForce(isContinue)
    }

    override fun setRecognizeModeNew(isContinue: Boolean, isCloseStreamData: Boolean) {
        impl?.setRecognizeModeNew(isContinue, isCloseStreamData)
    }

    override fun onAgentActionFinish(action: String, code: Int, message: String?) {
        impl?.onAgentActionFinish(action, code, message)
    }

    override fun onAgentActionState(action: String, state: Int, data: String?) {
        impl?.onAgentActionState(action, state, data)
    }

    override fun queryByTextWithThinking(text: String?, isShowThinking: Boolean) {
        impl?.queryByTextWithThinking(text, isShowThinking)
    }
}