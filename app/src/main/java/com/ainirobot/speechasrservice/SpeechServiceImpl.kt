package com.ainirobot.speechasrservice

import com.ainirobot.coreservice.ISkill
import com.ainirobot.coreservice.ISkillCallback
import com.ainirobot.coreservice.ISkillServerCheckListener
import com.ainirobot.coreservice.config.SceneEntity
import com.ainirobot.coreservice.listener.IMusicListener
import com.ainirobot.coreservice.listener.ITextListener
import com.ainirobot.coreservice.listener.IToneListener

class SpeechServiceImpl(private val delegate: SpeechDelegate) : ISkill.Stub(), ISpeechDelegate {

    override fun registerCallBack(cb: ISkillCallback?) {
        delegate.registerCallBack(cb)
    }

    override fun unregisterCallBack(cb: ISkillCallback?) {
        delegate.unregisterCallBack(cb)
    }

    override fun playText(text: String?, listener: ITextListener?) {
        delegate.playText(text, listener)
    }

    override fun playTone(type: String?, listener: IToneListener?) {
        delegate.playTone(type, listener)
    }

    override fun stopTTS() {
        delegate.stopTTS()
    }

    override fun setRecognizeMode(isContinue: Boolean) {
        delegate.setRecognizeMode(isContinue)
    }

    override fun setASREnabled(enable: Boolean) {
        delegate.setASREnabled(enable)
    }

    override fun setRecognizable(enable: Boolean) {
        delegate.setRecognizable(enable)
    }

    override fun queryByText(text: String?) {
        delegate.queryByText(text)
    }

    override fun getActiveAsk(properType: String?, robotProperJson: String?) {
        delegate.getActiveAsk(properType, robotProperJson)
    }

    override fun cancleAudioOperation() {
        delegate.cancleAudioOperation()
    }

    override fun setWakeupHintClosed(isWakeupHintClosed: Boolean) {
        delegate.setWakeupHintClosed(isWakeupHintClosed)
    }

    override fun setAngleCenterRange(angle_center: Float, angle_range: Float) {
        delegate.setAngleCenterRange(angle_center, angle_range)
    }

    override fun setTTSParams(ttsType: String?, value: Int) {
        delegate.setTTSParams(ttsType, value)
    }

    override fun setMultipleModeEnable(enale: Boolean) {
        delegate.setMultipleModeEnable(enale)
    }

    override fun setCustomizeWakeUpWord(
        wakeUpWordChinese: String?,
        wakeUpWordSpell: String?,
        separator: String?
    ): Int {
        return delegate.setCustomizeWakeUpWord(wakeUpWordChinese, wakeUpWordSpell, separator)
    }

    override fun closeCustomizeWakeUpWord(): Int {
        return delegate.closeCustomizeWakeUpWord()
    }

    override fun getPinYinScore(pinyin: String?, separator: String?): Int {
        return delegate.getPinYinScore(pinyin, separator)
    }

    override fun queryPinYinFromChinese(chineseWord: String?): String {
        return delegate.queryPinYinFromChinese(chineseWord)
    }

    override fun queryPinYinMappingTable(pinyin: String?): String {
        return delegate.queryPinYinMappingTable(pinyin)
    }

    override fun queryUserSetWakeUpWord(): String {
        return delegate.queryUserSetWakeUpWord()
    }

    override fun setLangRec(autoLangJson: String?) {
        delegate.setLangRec(autoLangJson)
    }

    override fun registerServerCheck(Listener: ISkillServerCheckListener?) {
        delegate.registerServerCheck(Listener)
    }

    override fun unregisterServerCheck() {
        delegate.unregisterServerCheck()
    }

    override fun playToneByLocalPath(localPath: String?, listener: IToneListener?) {
        delegate.playToneByLocalPath(localPath, listener)
    }

    override fun stopTone() {
        delegate.stopTone()
    }

    override fun switchScene(scene: SceneEntity?) {
        delegate.switchScene(scene)
    }

    override fun setAsrExtendProperty(propertyJson: String?): Boolean {
        return delegate.setAsrExtendProperty(propertyJson)
    }

    override fun setASRParams(asrType: String?, value: String?) {
        delegate.setASRParams(asrType, value)
    }

    override fun onCreate(app_id: String?) {
        delegate.onCreate(app_id)
    }

    override fun onForeground(app_id: String?) {
        delegate.onForeground(app_id)
    }

    override fun onBackground(app_id: String?) {
        delegate.onBackground(app_id)
    }

    override fun onDestroy(app_id: String?) {
        delegate.onDestroy(app_id)
    }

    override fun setVersion(app_id: String?, app_version: String?) {
        delegate.setVersion(app_id, app_version)
    }

    override fun setPath(app_id: String?, path: String?) {
        delegate.setPath(app_id, path)
    }

    override fun sendAgentMessage(type: String?, code: Int, message: String?) {
        delegate.sendAgentMessage(type, code, message)
    }

    override fun setSyncReportCustomNlpData(app_id: String?, data: String?) {
        delegate.setSyncReportCustomNlpData(app_id, data)
    }

    override fun setAsyncReportCustomNlpData(app_id: String?, data: String?) {
        delegate.setAsyncReportCustomNlpData(app_id, data)
    }

    override fun resetNlpState() {
        delegate.resetNlpState()
    }

    override fun setServerApp(appList: MutableList<String>?) {
        delegate.setServerApp(appList)
    }

    override fun setDebug(value: Boolean) {
        delegate.setDebug(value)
    }

    override fun setSyncCustomNlpData(map: MutableMap<Any?, Any?>?) {
        delegate.setSyncCustomNlpData(map)
    }

    override fun setAsyncCustomNlpData(opt: String?, data: String?): String {
        return delegate.setAsyncCustomNlpData(opt, data)
    }

    override fun getTtsPlayStatus(): Int {
        return delegate.getTtsPlayStatus()
    }

    override fun downloadTtsAudio(ttsEntitiesJson: String?) {
        delegate.downloadTtsAudio(ttsEntitiesJson)
    }

    override fun getSpokemanListByLanguage(lang: String?): String {
        return delegate.getSpokemanListByLanguage(lang)
    }

    override fun playMusicByLocalPath(
        localPath: String?,
        looping: Boolean,
        enableAudioFocus: Boolean,
        listener: IMusicListener?
    ) {
        delegate.playMusicByLocalPath(localPath, looping, enableAudioFocus, listener)
    }

    override fun stopMusicPlay() {
        delegate.stopMusicPlay()
    }

    override fun closeStreamDataReceived(statusJson: String?) {
        delegate.closeStreamDataReceived(statusJson)
    }

    override fun isRecognizeContinue(): Boolean {
        return delegate.isRecognizeContinue()
    }

    override fun isRecognizable(): Boolean {
        return delegate.isRecognizable()
    }

    override fun setRecognizeModeForce(isContinue: Boolean) {
        delegate.setRecognizeModeForce(isContinue)
    }

    override fun setRecognizeModeNew(isContinue: Boolean, isCloseStreamData: Boolean) {
        delegate.setRecognizeModeNew(isContinue, isCloseStreamData)
    }

    override fun onAgentActionFinish(action: String, code: Int, message: String?) {
        delegate.onAgentActionFinish(action, code, message)
    }

    override fun onAgentActionState(action: String, state: Int, data: String?) {
        delegate.onAgentActionState(action, state, data)
    }

    override fun queryByTextWithThinking(text: String?, isShowThinking: Boolean) {
        delegate.queryByTextWithThinking(text, isShowThinking)
    }
}