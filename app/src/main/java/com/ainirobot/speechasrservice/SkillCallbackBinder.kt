package com.ainirobot.speechasrservice

import com.ainirobot.coreservice.ISkillCallback

class SkillCallbackBinder : ISkillCallback.Stub() {

    var callback: ISkillCallback? = null

    override fun onSpeechParResult(text: String?) {
        callback?.onSpeechParResult(text)
    }

    override fun onStart() {
        callback?.onStart()
    }

    override fun onStop() {
        callback?.onStop()
    }

    override fun onVolumeChange(volume: Int) {
        callback?.onVolumeChange(volume)
    }

    override fun onQueryEnded(queryEndStatus: Int) {
        callback?.onQueryEnded(queryEndStatus)
    }

    override fun onGetMultipleModeInfos(index: Int): String {
        return callback?.onGetMultipleModeInfos(index) ?: ""
    }

    override fun onQueryAsrResult(asrResult: String?) {
        callback?.onQueryAsrResult(asrResult)
    }

    override fun onError(sid: String?, code: Int, message: String?) {
        callback?.onError(sid, code, message)
    }

    override fun onVadMuteTime(vadMuteTime: Int) {
        callback?.onVadMuteTime(vadMuteTime)
    }

    override fun onSpeechStreamData(data: String?) {
        callback?.onSpeechStreamData(data)
    }
}