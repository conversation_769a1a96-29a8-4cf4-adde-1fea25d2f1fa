package com.ainirobot.speechasrservice.kratos.cofig;

import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;

/**
 * 语音识别扩展参数
 */
public class AsrExpendInfo {

    //讲话人id（免唤醒算法）
    private static final String SPEAKER_ID = "speaker_id";
    //是否开启云端vad
    private static final String SERVER_VAD = "enable_server_vad";

    private static final HashMap<String, Object> expendInfoMap = new HashMap<String, Object>() {{
        put(SPEAKER_ID, -1);
        put(SERVER_VAD, true);
    }};

    public static void set(String key, Object value) {
        expendInfoMap.put(key, value);
        update();
    }

    public static void setSpeakerId(int speakerId) {
        set(SPEAKER_ID, speakerId);
    }

    public static void setServerVad(boolean serverVad) {
        set(SERVER_VAD, serverVad);
        if (!serverVad) {
            setSpeakerId(-1);
        }
    }

    public static void addAll(HashMap<String, Object> map) {
        expendInfoMap.putAll(map);
        update();
    }

    public static void remove(String key) {
        expendInfoMap.remove(key);
        update();
    }

    public static void clear() {
        expendInfoMap.clear();
        update();
    }

    private static void update() {
        String extendInfo = JSONObject.toJSONString(expendInfoMap);
//        OrionASRParams.getInstance().setPermanentExtendInfo(extendInfo);
    }
}
