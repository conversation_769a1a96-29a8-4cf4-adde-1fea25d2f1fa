/*
 * Copyright (C) 2017 Orion Technology Co., Ltd. All Rights Reserved.
 */
package com.ainirobot.speechasrservice.kratos.entity;

import java.util.List;


public class ASRResult {

    private AsrContent asr_content;
    private AsrParam asr_param;
    private TtsParam tts_param;
    private Voiceprint voiceprint;

    public AsrContent getAsr_content() {
        return asr_content;
    }

    public void setAsr_content(AsrContent asr_content) {
        this.asr_content = asr_content;
    }

    public AsrParam getAsr_param() {
        return asr_param;
    }

    public void setAsr_param(AsrParam asr_param) {
        this.asr_param = asr_param;
    }

    public TtsParam getTts_param() {
        return tts_param;
    }

    public void setTts_param(TtsParam tts_param) {
        this.tts_param = tts_param;
    }

    public Voiceprint getVoiceprint() {
        return voiceprint;
    }

    public void setVoiceprint(Voiceprint voiceprint) {
        this.voiceprint = voiceprint;
    }

    public static class AsrContent {
        private String backend;
        private String resource;
        private List<String> nbest;

        public String getBackend() {
            return backend;
        }

        public void setBackend(String backend) {
            this.backend = backend;
        }

        public String getResource() {
            return resource;
        }

        public void setResource(String resource) {
            this.resource = resource;
        }

        public List<String> getNbest() {
            return nbest;
        }

        public void setNbest(List<String> nbest) {
            this.nbest = nbest;
        }
    }

    public static class AsrParam {
        private long cpid;
        private int decoder_time_cost;
        private int dialog_time_cost;
        private int end_flag;
        private int err_no;
        private int idx;
        private String pkg_type;
        private String sid;
        private int single_round_end_flag;
        private String single_round_sn;
        private int status;
        private int time_cost;
        private int tts_time_cost;

        public long getCpid() {
            return cpid;
        }

        public void setCpid(long cpid) {
            this.cpid = cpid;
        }

        public int getDecoder_time_cost() {
            return decoder_time_cost;
        }

        public void setDecoder_time_cost(int decoder_time_cost) {
            this.decoder_time_cost = decoder_time_cost;
        }

        public int getDialog_time_cost() {
            return dialog_time_cost;
        }

        public void setDialog_time_cost(int dialog_time_cost) {
            this.dialog_time_cost = dialog_time_cost;
        }

        public int getEnd_flag() {
            return end_flag;
        }

        public void setEnd_flag(int end_flag) {
            this.end_flag = end_flag;
        }

        public int getErr_no() {
            return err_no;
        }

        public void setErr_no(int err_no) {
            this.err_no = err_no;
        }

        public int getIdx() {
            return idx;
        }

        public void setIdx(int idx) {
            this.idx = idx;
        }

        public String getPkg_type() {
            return pkg_type;
        }

        public void setPkg_type(String pkg_type) {
            this.pkg_type = pkg_type;
        }

        public String getSid() {
            return sid;
        }

        public void setSid(String sid) {
            this.sid = sid;
        }

        public int getSingle_round_end_flag() {
            return single_round_end_flag;
        }

        public void setSingle_round_end_flag(int single_round_end_flag) {
            this.single_round_end_flag = single_round_end_flag;
        }

        public String getSingle_round_sn() {
            return single_round_sn;
        }

        public void setSingle_round_sn(String single_round_sn) {
            this.single_round_sn = single_round_sn;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getTime_cost() {
            return time_cost;
        }

        public void setTime_cost(int time_cost) {
            this.time_cost = time_cost;
        }

        public int getTts_time_cost() {
            return tts_time_cost;
        }

        public void setTts_time_cost(int tts_time_cost) {
            this.tts_time_cost = tts_time_cost;
        }

        @Override
        public String toString() {
            return "AsrParam{" +
                    "cpid=" + cpid +
                    ", decoder_time_cost=" + decoder_time_cost +
                    ", dialog_time_cost=" + dialog_time_cost +
                    ", end_flag=" + end_flag +
                    ", err_no=" + err_no +
                    ", idx=" + idx +
                    ", pkg_type='" + pkg_type + '\'' +
                    ", sid='" + sid + '\'' +
                    ", single_round_end_flag=" + single_round_end_flag +
                    ", single_round_sn='" + single_round_sn + '\'' +
                    ", status=" + status +
                    ", time_cost=" + time_cost +
                    ", tts_time_cost=" + tts_time_cost +
                    '}';
        }
    }

    public static class TtsParam {
        private int err_no;
        private String tts_body;
        private int tts_idx;
        private int tts_percent;
        private String tts_sn;

        public int getErr_no() {
            return err_no;
        }

        public void setErr_no(int err_no) {
            this.err_no = err_no;
        }

        public String getTts_body() {
            return tts_body;
        }

        public void setTts_body(String tts_body) {
            this.tts_body = tts_body;
        }

        public int getTts_idx() {
            return tts_idx;
        }

        public void setTts_idx(int tts_idx) {
            this.tts_idx = tts_idx;
        }

        public int getTts_percent() {
            return tts_percent;
        }

        public void setTts_percent(int tts_percent) {
            this.tts_percent = tts_percent;
        }

        public String getTts_sn() {
            return tts_sn;
        }

        public void setTts_sn(String tts_sn) {
            this.tts_sn = tts_sn;
        }

        @Override
        public String toString() {
            return "TtsParam{" +
                    "err_no=" + err_no +
                    ", tts_body='" + tts_body + '\'' +
                    ", tts_idx=" + tts_idx +
                    ", tts_percent=" + tts_percent +
                    ", tts_sn='" + tts_sn + '\'' +
                    '}';
        }
    }

    public static class Voiceprint {
        private String aid;
        private int context;
        private String all_score;
        private float score;
        private int error;
        private String err_desc;

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public int getContext() {
            return context;
        }

        public void setContext(int context) {
            this.context = context;
        }

        public String getAll_score() {
            return all_score;
        }

        public void setAll_score(String all_score) {
            this.all_score = all_score;
        }

        public float getScore() {
            return score;
        }

        public void setScore(float score) {
            this.score = score;
        }

        public int getError() {
            return error;
        }

        public void setError(int error) {
            this.error = error;
        }

        public String getErr_desc() {
            return err_desc;
        }

        public void setErr_desc(String err_desc) {
            this.err_desc = err_desc;
        }

        @Override
        public String toString() {
            return "Voiceprint{" +
                    "aid='" + aid + '\'' +
                    ", context=" + context +
                    ", all_score='" + all_score + '\'' +
                    ", score='" + score + '\'' +
                    ", error='" + error + '\'' +
                    ", err_desc='" + err_desc + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "ASRResult{" +
                "asr_content=" + asr_content +
                ", asr_param=" + asr_param +
                ", tts_param=" + tts_param +
                ", voiceprint=" + voiceprint +
                '}';
    }
}
