package com.ainirobot.speechasrservice.kratos.auth;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.ProductInfo;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.speechasrservice.kratos.auth.bean.AccessTokenResultBean;
import com.ainirobot.speechasrservice.kratos.auth.bean.RefreshTokenResultBean;
import com.ainirobot.speechasrservice.kratos.auth.net.ORRetrofitAdapter;
import com.ainirobot.speechasrservice.kratos.auth.utils.NameValueParam;
import com.ainirobot.speechasrservice.kratos.auth.utils.SignUtil;
import com.ainirobot.speechasrservice.kratos.cofig.ClientConfig;
import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig;
import com.ainirobot.speechasrservice.skill.SkillManager;
import com.ainirobot.speechasrservice.utils.SharedPreferencesUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;


class AccessTokenRequest {
    private final String TAG = AccessTokenRequest.class.getSimpleName();


    public void getSpeakerToken(String aRefreshToken, final AuthenticationListener listener) {
        Log.d(TAG, "getSpeakerToken:" + aRefreshToken);
        if (!TextUtils.isEmpty(aRefreshToken)) {
            refreshSpeakerToken(aRefreshToken, listener);
        } else {
            fetchSpeakerToken(listener);
        }
    }

    private void refreshSpeakerToken(String aRefreshToken, final AuthenticationListener listener) {
        String clientId = EnvironmentConfig.OtherEnvironment.getClientId();
        String clientSecret = EnvironmentConfig.OtherEnvironment.getClientSecret();
        String timestamp = getSystemTimeString();
        String granType = "refresh_token";
        Log.d(TAG, "refreshSpeakerToken token");
        ORRetrofitAdapter.refreshSpeakerToken(clientId, granType, aRefreshToken, clientSecret,
                timestamp).enqueue(new Callback<RefreshTokenResultBean>() {
            @Override
            public void onResponse(Call<RefreshTokenResultBean> call,
                                   Response<RefreshTokenResultBean> response) {
                RefreshTokenResultBean bean;
                if (response == null || (bean = response.body()) == null
                        || response.code() != AuthenticationListener.CODE_HTTP_OK) {
                    Log.d(TAG, "refreshSpeakerToken response.code():" + response.code() + ", msg:" + response.message());
                    getSpeakerToken(null, listener);
                    if (listener != null) {
                        listener.onFailed(AuthenticationListener.CODE_HTTP_ERROR,
                                " error= " + "response is null");
                    }
                } else {
                    Log.i(TAG, "refreshSpeakerToken| bean :" + bean);
                    saveToken(bean.getAccess_token(), bean.getRefresh_token(), bean.getExpires_in());
                    if (listener != null) {
                        listener.onSucceed();
                    }
                }

            }

            @Override
            public void onFailure(Call<RefreshTokenResultBean> call, Throwable t) {
                Log.d(TAG, "refreshSpeakerToken onFailure:"+(t != null ? t.getMessage():""));
                if (listener != null) {
                    listener.onFailed(AuthenticationListener.CODE_HTTP_ERROR, " error=" + t);
                }
            }
        });
    }

    private void fetchSpeakerToken(final AuthenticationListener listener) {
        final String timestamp = getSystemTimeString();
        List<NameValueParam> params = new ArrayList<>();
        String identifier = RobotSettings.getSystemSn();
        params.add(new NameValueParam("client_id", EnvironmentConfig.OtherEnvironment
                .getClientId()));
        params.add(new NameValueParam("identifier", identifier));
        params.add(new NameValueParam("timestamp", timestamp));
        Collections.sort(params);
        Log.d(TAG,"start fetch");
        ORRetrofitAdapter.fetchSpeakerToken(EnvironmentConfig.OtherEnvironment.getClientId(),
                identifier, timestamp,
                SignUtil.createSign(EnvironmentConfig.OtherEnvironment.getClientSecret(), params))
                .enqueue(new Callback<AccessTokenResultBean>() {
                    @Override
                    public void onResponse(Call<AccessTokenResultBean> call,
                                           Response<AccessTokenResultBean> response) {
                        AccessTokenResultBean bean;
                        if (response == null || (bean = response.body()) == null
                                || response.code() != AuthenticationListener.CODE_HTTP_OK) {
                            if (listener != null) {
                                listener.onFailed(AuthenticationListener.CODE_HTTP_ERROR,
                                        " error= " + "response is null");
                            }
                        } else {
                            Log.i(TAG, "fetchSpeakerToken| bean :" + bean);
                            saveToken(bean.getAccess_token(), bean.getRefresh_token(),
                                    bean.getExpires_in());
                            if (listener != null) {
                                listener.onSucceed();
                            }
                        }

                    }

                    @Override
                    public void onFailure(Call<AccessTokenResultBean> call, Throwable t) {
                        Log.d(TAG, "fetchSpeakerToken onFailure:"+(t != null ? t.getMessage():""));
                        if (listener != null) {
                            listener.onFailed(AuthenticationListener.CODE_HTTP_ERROR,
                                    " error=" + t);
                        }
                    }
                });
    }

    private void saveToken(String accessToken, String refreshToken, int expires_in) {
        Log.d(TAG, " new refreshToken : " + refreshToken + " new accessToken : " + accessToken
                + " expires_in :" + expires_in);
        ClientConfig.setACCESSTOKEN(accessToken);
        SharedPreferencesUtil.setAuthRefreshToken(refreshToken, accessToken);
//        SkillManager.getInstance().refreshUserParams();
    }

    private String getSystemTimeString() {
        return String.valueOf(System.currentTimeMillis() / 1000);
    }
}
