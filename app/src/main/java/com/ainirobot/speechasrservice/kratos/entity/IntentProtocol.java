package com.ainirobot.speechasrservice.kratos.entity;

import com.alibaba.fastjson.JSONObject;

public class IntentProtocol {
    private static final String TAG = IntentProtocol.class.getSimpleName();
    private int code;
    private String domain;
    private String english_domain;
    private String intent;
    private String sid;
    private String text;
    private Answer answer;
    private JSONObject skillData;
    private JSONObject slots;
    private JSONObject display;
    private JSONObject card;
    private String action_type;
    private String voice_type;
    private JSONObject nlpData;

    public IntentProtocol() {
    }

    public JSONObject getNlpData() {
        return this.nlpData;
    }

    public void setNlpData(JSONObject var1) {
        this.nlpData = var1;
    }

    public Answer getAnswer() {
        return this.answer;
    }

    public void setAnswer(Answer var1) {
        this.answer = var1;
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int var1) {
        this.code = var1;
    }

    public String getDomain() {
        return this.domain;
    }

    public void setDomain(String var1) {
        this.domain = var1;
    }

    public String getEnglish_domain() {
        return this.english_domain;
    }

    public void setEnglish_domain(String var1) {
        this.english_domain = var1;
    }

    public String getIntent() {
        return this.intent;
    }

    public void setIntent(String var1) {
        this.intent = var1;
    }

    public String getSid() {
        return this.sid;
    }

    public void setSid(String var1) {
        this.sid = var1;
    }

    public JSONObject getSkillData() {
        return this.skillData;
    }

    public void setSkillData(JSONObject var1) {
        this.skillData = var1;
    }

    public JSONObject getSlots() {
        return this.slots;
    }

    public void setSlots(JSONObject var1) {
        this.slots = var1;
    }

    public String getText() {
        return this.text;
    }

    public void setText(String var1) {
        this.text = var1;
    }

    public JSONObject getDisplay() {
        return this.display;
    }

    public void setDisplay(JSONObject var1) {
        this.display = var1;
    }

    public JSONObject getCard() {
        return this.card;
    }

    public void setCard(JSONObject var1) {
        this.card = var1;
    }

    public String getAction_type() {
        return this.action_type;
    }

    public void setAction_type(String var1) {
        this.action_type = var1;
    }

    public String getVoice_type() {
        return this.voice_type;
    }

    public void setVoice_type(String var1) {
        this.voice_type = var1;
    }

    @Override
    public String toString() {
        return "IntentProtocol{" +
                "code=" + code +
                ", domain='" + domain + '\'' +
                ", english_domain='" + english_domain + '\'' +
                ", intent='" + intent + '\'' +
                ", sid='" + sid + '\'' +
                ", text='" + text + '\'' +
                ", answer=" + answer +
                ", skillData=" + skillData +
                ", slots=" + slots +
                ", display=" + display +
                ", card=" + card +
                ", action_type='" + action_type + '\'' +
                ", voice_type='" + voice_type + '\'' +
                ", nlpData=" + nlpData +
                '}';
    }

    public static class Answer {
        private String text;
        private String audio;
        private String type;
        private boolean textPlay = true;
        private String textBGM;

        public Answer() {
        }

        public String getText() {
            return this.text;
        }

        public void setText(String var1) {
            this.text = var1;
        }

        public String getAudio() {
            return this.audio;
        }

        public void setAudio(String var1) {
            this.audio = var1;
        }

        public String getType() {
            return this.type;
        }

        public void setType(String var1) {
            this.type = var1;
        }

        public boolean isTextPlay() {
            return this.textPlay;
        }

        public void setTextPlay(boolean var1) {
            this.textPlay = var1;
        }

        public String getTextBGM() {
            return this.textBGM;
        }

        public void setTextBGM(String var1) {
            this.textBGM = var1;
        }
    }
}
