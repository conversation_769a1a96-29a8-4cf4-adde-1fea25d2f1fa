package com.ainirobot.speechasrservice.kratos.cofig;

import android.os.Environment;

import com.ainirobot.common.utils.DeviceOS;
import com.alibaba.fastjson.JSONObject;

public class ClientConfig {

    public static final String PROTOCOL_RESOURCE = "307";
    public static final String PROTOCOL_AUDIO_AUTH = "202";
    public static final String PROTOCOL_AUDIO_AUTH_QUERY = "302";
    public static final String SOCKETADDRESS = "/data/misc/audioserver/asr_bf_dir";
    public static final int REPEAT_TIME = 1000 * 30 * 60;//刷新token时间间隔
    public static final int VADTIMEOUT = 7000;//vad.begin后用户说话最长时间
    public static final int SILENTTIME = 6000;//唤醒识别模式下静音时长
    public static final String DEVICE_TYPE = "2";
    public static final String OS_TYPE = "android";
    public static final String OVS_SDK_OS = "android";
    private static String ACCESSTOKEN = "";
    private static String LAT = "";
    private static String LNG = "";
    private static int VOICE_REPORT = 0;
    public static final int OPEN_SSL = 1;
    public static final int CLOSE_SSL = 0;
    private static String wakeup_id = "";
    private static JSONObject custom_param = null;
    private static String immediate = "";
    public static final String BASE_LOCAL_PATH = Environment.getExternalStorageDirectory()
            .getAbsolutePath() + "/Kratos";

    public static String getACCESSTOKEN() {
        return ACCESSTOKEN;
    }

    public static void setACCESSTOKEN(String ACCESSTOKEN) {
        ClientConfig.ACCESSTOKEN = ACCESSTOKEN;
    }

    public static String getEnterpriseId() {
        return DeviceOS.INSTANCE.getEnterpriseId();
    }

    public static final short REQTYPE_WAKEUP_ANGLE = 1;
    public static final short REQTYPE_CENTER_RANGE = 2;
    public static final short REQTYPE_BEAM_INFO = 3;
    public static final short REQTYPE_NLP_LEVEL = 4;

    public static final short REQTYPE_DUMP_AUDIO_DATA = 0x104;
    public static final short REQTYPE_WAKEUP_SET_KEYWORD = 0x201;
    public static final short REQTYPE_WAKEUP_DEL_KEYWORD = 0x202;
    public static final short REQTYPE_WAKEUP_DEL_ALL_KEYWORD = 0x203;

    public static String getWakeup_id() {
        return wakeup_id;
    }

    public static void setWakeup_id(String wakeup_id) {
        ClientConfig.wakeup_id = wakeup_id;
    }

    public static void setCustom_param(JSONObject custom_param) {
        ClientConfig.custom_param = custom_param;
    }

    public static JSONObject getCustom_param() {
        return custom_param;
    }

    public static void setImmediate(String mImmediate) {
        immediate = mImmediate;
    }

    public static String getImmediate() {
        return immediate;
    }
}
