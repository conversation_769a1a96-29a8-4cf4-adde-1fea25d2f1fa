package com.ainirobot.speechasrservice.kratos.entity;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.ArrayList;

public class SpokermanResult {
    private String sn;
    private ArrayList<Spokerman> voices;

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public ArrayList<Spokerman> getVoices() {
        return voices;
    }

    public void setVoices(ArrayList<Spokerman> voices) {
        this.voices = voices;
    }

    public static class Spokerman {
        private String lan;
        private String type;
        @JSONField(name = "default")
        private int is_default;
        private ArrayList<Audio> audio;
        @JSONField(name = "Desc")
        private String desc;

        public String getLan() {
            return lan;
        }

        public void setLan(String lan) {
            this.lan = lan;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public int getIs_default() {
            return is_default;
        }

        public void setIs_default(int is_default) {
            this.is_default = is_default;
        }

        public ArrayList<Audio> getAudio() {
            return audio;
        }

        public void setAudio(ArrayList<Audio> audio) {
            this.audio = audio;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public static class Audio {
            @JSONField(name = "tex")
            private String text;
            private String filename;

            public String getText() {
                return text;
            }

            public void setText(String text) {
                this.text = text;
            }

            public String getFilename() {
                return filename;
            }

            public void setFilename(String filename) {
                this.filename = filename;
            }

            @Override
            public String toString() {
                return "Audio{" +
                        "text='" + text + '\'' +
                        ", filename='" + filename + '\'' +
                        '}';
            }
        }

        @Override
        public String toString() {
            return "Spokerman{" +
                    "lan='" + lan + '\'' +
                    ", type='" + type + '\'' +
                    ", is_default=" + is_default +
                    ", audio=" + audio +
                    ", desc='" + desc + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "SpokermanResult{" +
                "sn='" + sn + '\'' +
                ", voices=" + voices +
                '}';
    }
}
