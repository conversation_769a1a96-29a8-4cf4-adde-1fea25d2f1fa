package com.ainirobot.speechasrservice.kratos.auth.net;

import com.ainirobot.speechasrservice.kratos.auth.bean.AccessTokenResultBean;
import com.ainirobot.speechasrservice.kratos.auth.bean.RefreshTokenResultBean;

import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;
import retrofit2.http.Streaming;
import retrofit2.http.Url;

public interface AssistantService {
    @FormUrlEncoded
    @POST()
    @Headers({"Content-Type:application/x-www-form-urlencoded"})
    Call<AccessTokenResultBean> fetchSpeakerToken(
            @Url String url,
            @Field("client_id") String client_id,
            @Field("identifier") String identifier,
            @Field("timestamp") String timestamp,
            @Field("sign") String sign
    );

    @FormUrlEncoded
    @POST()
    Call<RefreshTokenResultBean> refreshSpeakerToken(
            @Url String url,
            @Field("client_id") String client_id,
            @Field("grant_type") String grant_type,
            @Field("refresh_token") String refresh_token,
            @Field("client_secret") String client_secret,
            @Field("timestamp") String timestamp
    );

    /**
     * http://xb.asr.ainirobot.com:8003/text-asr/error错误码定义
     * getAsrText
     *
     * @param body
     * @return
     */
    @POST
    Call<Object> getAsrText(@Url String url, @Body RequestBody body);

    /**
     * 服务器心跳
     *
     * @param body
     * @return
     */
    @POST()
    Call<String> getEcho(@Url String url, @Body RequestBody body);

    /**
     * @param url   发音人列表地址
     * @param pdt   语音pid
     * @param trans 转换类型 0-通用（指定per和eper来指定多发音人，弃用方式）；1-映射（通过type字段来指定发音人，新方式）；2-自定义（自定义发音人时）
     * @return
     */
    @GET()
    Call<ResponseBody> getSpokermenList(
            @Url String url,
            @Query("pdt") String pdt,
            @Query("trans") int trans,
            @Query("client_id") String client_id,
            @Query("enterprise_id") String enterprise_id,
            @Query("cuid") String cuid,
            @Query("device_id") String device_id
    );

    /**
     * @param url   发音人列表地址
     * @param pdt   语音pid
     * @param trans 转换类型 0-通用（指定per和eper来指定多发音人，弃用方式）；1-映射（通过type字段来指定发音人，新方式）；2-自定义（自定义发音人时）
     * @return
     */
    @GET()
    Call<ResponseBody> getSpokermenListByLanguage(
            @Url String url,
            @Query("pdt") String pdt,
            @Query("trans") int trans,
            @Query("client_id") String client_id,
            @Query("enterprise_id") String enterprise_id,
            @Query("cuid") String cuid,
            @Query("device_id") String device_id,
            @Query("lan") String lan
    );

    /**
     *
     * @param url 获取唤醒应答音频地址
     * @param lan 语音
     * @param type 发音人type
     * @param tex 发音人文本
     * @return
     */
    @Streaming
    @GET()
    Call<ResponseBody> getWakeupAudio(
            @Url String url,
            @Query("lan") String lan,
            @Query("type") String type,
            @Query("tex") String tex,
            @Query("pdt") String pdt,
            @Query("client_id") String client_id,
            @Query("enterprise_id") String enterprise_id,
            @Query("cuid") String cuid,
            @Query("device_id") String device_id
    );
}
