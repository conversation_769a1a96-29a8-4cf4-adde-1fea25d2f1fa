package com.ainirobot.speechasrservice.kratos.entity;

public final class NLPConstants {
    public static final String SID = "sid";
    public static final String WAKEUPANGLEINFO = "wakeUpAngleInfo";
    public static final String ORIGIN_RESULT = "origin_result";
    public static final int RESOURCE_SUCCESS = 200;
    public static final int RESOURCE_TOKEN_FAILT = 920;
    public static final int RESOURCE_TOKEN_INVALID = 1111;
    public static final int RESOURCE_TOKEN_EXPIRE = 1112;
    public static final String VOLUME = "volume";
    public static final String VADMUTETIME = "vadMuteTime";
    public static final String MT_RANGE = "range";
    public static final String SYS_LANG = "sys_lang";
    public static final int ERROR_NOICE_VOICE = -2020004;
    public static final int ERROR_ROBOT_VOICE = -2020070;
}
