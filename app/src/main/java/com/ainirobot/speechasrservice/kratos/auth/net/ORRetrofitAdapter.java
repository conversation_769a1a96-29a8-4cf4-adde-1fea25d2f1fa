package com.ainirobot.speechasrservice.kratos.auth.net;


import android.util.Log;

import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.speechasrservice.kratos.auth.bean.AccessTokenResultBean;
import com.ainirobot.speechasrservice.kratos.auth.bean.RefreshTokenResultBean;
import com.ainirobot.speechasrservice.kratos.cofig.ClientConfig;
import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig;
import com.ainirobot.speechasrservice.net.OkHttpClientMgr;

import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;

public class ORRetrofitAdapter {

    private static final String TAG = ORRetrofitAdapter.class.getSimpleName();

    public static retrofit2.Call<AccessTokenResultBean> fetchSpeakerToken(
            String client_id, String identifier, String timestamp, String sign) {
        AssistantService assistantService = (AssistantService) OkHttpClientMgr
                .create(AssistantService.class, OkHttpClientMgr.CLIENT_SSL);
        return assistantService.fetchSpeakerToken(EnvironmentConfig.TokenConfig.getFetchTokenURL(),
                client_id, identifier, timestamp, sign);
    }

    public static retrofit2.Call<RefreshTokenResultBean> refreshSpeakerToken(
            String client_id, String grant_type, String refresh_token, String client_secret,
            String timestamp) {
        AssistantService assistantService = (AssistantService) OkHttpClientMgr
                .create(AssistantService.class,
                        OkHttpClientMgr.CLIENT_DEFAULT);
        String refreshTokenURL = EnvironmentConfig.TokenConfig.getRefreshTokenURL();
        Log.d("AccessTokenRequest", "refreshTokenURL :" + refreshTokenURL);
        return assistantService.refreshSpeakerToken(refreshTokenURL, client_id, grant_type, refresh_token,
                client_secret, timestamp);
    }

    public static Call<Object> requestResByText(RequestBody body) {
        AssistantService assistantService = (AssistantService) OkHttpClientMgr
                .create(AssistantService.class,
                        OkHttpClientMgr.CLIENT_DEFAULT);
        return assistantService.getAsrText(EnvironmentConfig.AsrConfig.getTextQueryUrl(), body);
    }

    public static Call<String> requestEcho(RequestBody body) {
        AssistantService assistantService = (AssistantService) OkHttpClientMgr
                .createString(AssistantService.class,
                        OkHttpClientMgr.CLIENT_DEFAULT);
        return assistantService.getEcho(EnvironmentConfig.AsrConfig.getEchoUrl(), body);
    }

    public static Call<ResponseBody> getSpokermanList(String pdt) {
        AssistantService assistantService = (AssistantService) OkHttpClientMgr
                .create(AssistantService.class,
                        OkHttpClientMgr.CLIENT_DEFAULT);
        String client_id = EnvironmentConfig.OtherEnvironment.getClientId();
        String enterprise_id = ClientConfig.getEnterpriseId();
        String device_id = RobotSettings.getSystemSn();

        Log.d(TAG, "getSpokermanList :" + EnvironmentConfig.TtsConfig.getSpokermanURL() +
                "?pdt=" + pdt +
                "&trans=1" +
                "&client_id=" + client_id +
                "&enterprise_id=" + enterprise_id +
                "&cuid=" + device_id +
                "&device_id=" + device_id
        );

        return assistantService.getSpokermenList(EnvironmentConfig.TtsConfig.getSpokermanURL(),
                pdt, 1, client_id, enterprise_id, device_id, device_id);
    }

    public static Call<ResponseBody> getSpokermenListByLanguage(String pdt) {
        AssistantService assistantService = (AssistantService) OkHttpClientMgr
                .create(AssistantService.class,
                        OkHttpClientMgr.CLIENT_DEFAULT);
        String client_id = EnvironmentConfig.OtherEnvironment.getClientId();
        String enterprise_id = ClientConfig.getEnterpriseId();
        String device_id = RobotSettings.getSystemSn();

        Log.d(TAG, "getSpokermanURL :" + EnvironmentConfig.TtsConfig.getSpokermanURL() +
                "?pdt=" + pdt +
                "&trans=1" +
                "&client_id=" + client_id +
                "&enterprise_id=" + enterprise_id +
                "&cuid=" + device_id +
                "&device_id=" + device_id +
                "&lan=" + EnvironmentConfig.AsrConfig.getAsrLanguageName()
        );
        return assistantService.getSpokermenListByLanguage(
                EnvironmentConfig.TtsConfig.getSpokermanURL(),
                pdt, 1, client_id, enterprise_id, device_id, device_id, EnvironmentConfig.AsrConfig.getAsrLanguageName());
    }

    public static Call<ResponseBody> getWakeupAudio(String lan, String type, String tex) {
        AssistantService assistantService = (AssistantService) OkHttpClientMgr
                .createString(AssistantService.class,
                        OkHttpClientMgr.CLIENT_DEFAULT);
        String pdt = EnvironmentConfig.OtherEnvironment.getPid();
        String client_id = EnvironmentConfig.OtherEnvironment.getClientId();
        String enterprise_id = ClientConfig.getEnterpriseId();
        String device_id = RobotSettings.getSystemSn();

        return assistantService.getWakeupAudio(EnvironmentConfig.TtsConfig.getWakeupAudioURL(),
                lan, type, tex, pdt, client_id, enterprise_id, device_id, device_id);
    }
}