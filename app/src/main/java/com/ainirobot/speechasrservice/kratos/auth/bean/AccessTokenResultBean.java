package com.ainirobot.speechasrservice.kratos.auth.bean;


public class AccessTokenResultBean {
    private String access_token;
    private String refresh_token;
    private int expires_in;
    private String open_id;
    private String user_id;

    public String getAccess_token() {
        return access_token;
    }

    public String getRefresh_token() {
        return refresh_token;
    }

    public int getExpires_in() {
        return expires_in;
    }

    public String getOpen_id() {
        return open_id;
    }

    public String getUser_id() {
        return user_id;
    }

    @Override
    public String toString() {
        return "AccessTokenResultBean{" +
                "access_token='" + access_token + '\'' +
                ", refresh_token='" + refresh_token + '\'' +
                ", expires_in=" + expires_in +
                ", open_id='" + open_id + '\'' +
                ", user_id='" + user_id + '\'' +
                '}';
    }
}
