/*
 * Copyright (C) 2017 Orion Technology Co., Ltd. All Rights Reserved.
 */
package com.ainirobot.speechasrservice.kratos.cofig;

import android.support.annotation.Keep;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.common.DataStore;
import com.ainirobot.common.debug.Env;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotSettings;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.coreservice.client.speech.entity.LangParamsEnum;
import com.ainirobot.speechasrservice.utils.ConfigFileUtil;
import com.ainirobot.speechasrservice.utils.FileUtil;
import com.ainirobot.speechasrservice.utils.SharedPreferencesUtil;
import com.ainirobot.speechasrservice.utils.SpeechConfig;
//import com.cm.speech.tts.TTSPlayer;

import java.io.File;
import java.util.Locale;

public class EnvironmentConfig {
    private static final String TAG = EnvironmentConfig.class.getSimpleName();
    public static final int RELEASE = 0;//正式环境
    public static final int DEVELOP = 1;//测试环境
    public static final int PRE_RELEASE = 2;//灰度环境

    public static final String NLU_ONLINE = "online";
    public static final String NLU_PROD_SANDBOX = "prod_sandbox";
    public static final String NLU_SANDBOX = "sandbox";

    private static volatile int mWhichRelease = RELEASE;

    // 刷新token 地址
    private static String RELEASE_REFRESH_TOKEN_URL = "https://passport.ainirobot.com:8888/oauth2/token";
    private static String DEVELOP_REFRESH_TOKEN_URL = "http://passporttest.ainirobot.com:8888/oauth2/token";
    private static String PRE_REFRESH_TOKEN_URL = "https://passport.ainirobot.com:8888/oauth2/token";
    // 获取token 地址
    private static String RELEASE_FETCH_TOKEN_URL = "https://robot-passport.ainirobot.com:8866/account/bind";
    private static String DEVELOP_FETCH_TOKEN_URL = "https://passporttest.ainirobot.com:8866/account/bind";
    private static String PRE_RELEASE_FETCH_TOKEN_URL = "https://robot-passport.ainirobot.com:8866/account/bind";
    // h2 地址
    private static String RELEASE_H2_URL = "https://speech-bxm.ainirobot.com:443/h2/streaming-asr";
    private static String DEVELOP_H2_URL = "https://speech-test.ainirobot.com:443/h2/streaming-asr";
    private static String PRE_RELEASE_H2_URL = "https://speech-bxm-pre.ainirobot.com:443/h2/streaming-asr";
    //文本query 地址
    private static String RELEASE_TEXT_QUERY_URL = "https://speech-bxm.ainirobot.com/text-asr/v1";
    private static String DEVELOP_TEXT_QUERY_URL = "http://speech-test.ainirobot.com/text-asr/v1";
    private static String PRE_RELEASE_TEXT_QUERY_URL = "http://asr.ovspre.ainirobot.com:8003/text-asr/v1";
    // TTS地址
    private static String RELEASE_TTS_URL = "https://speech-bxm.ainirobot.com/tts/v1/text2audio";
    private static String DEVELOP_TTS_URL = "https://speech-test.ainirobot.com/tts/v1/text2audio";
    private static String PRE_RELEASE_TTS_URL = "https://speech-bxm-pre.ainirobot.com/tts/v1/text2audio";
    // TTS发音人地址
    private static String RELEASE_SPOKERMAN_URL = "https://speech-bxm.ainirobot.com/tts/v1/voices";
    private static String DEVELOP_SPOKERMAN_URL = "https://speech-test.ainirobot.com/tts/v1/voices";
    private static String PRE_RELEASE_SPOKERMAN_URL = "https://speech-bxm.ainirobot.com/tts/v1/voices";
    // TTS唤醒应答音频下载地址
    private static String RELEASE_WAKEUP_AUDIO_URL = "https://speech-bxm.ainirobot.com/tts/v1/operate/audio";
    private static String DEVELOP_WAKEUP_AUDIO_URL = "https://speech-test.ainirobot.com/tts/v1/operate/audio";
    private static String PRE_RELEASE_WAKEUP_AUDIO_URL = "https://speech-bxm.ainirobot.com/tts/v1/operate/audio";
    //心跳 地址
    private static String RELEASE_ECHO_URL = "https://speech-bxm.ainirobot.com/echo";
    private static String DEVELOP_ECHO_URL = "https://speech-test.ainirobot.com/echo";
    private static String PRE_RELEASE_ECHO_URL = "https://speech-bxm-pre.ainirobot.com/echo";

    // nlp 结果流式返回,流式的连接地址
    private static String RELEASE_STREAM_WS_URL = "wss://speech.orionstar.com/speech-bridge/ws/streaming";
    private static String DEVELOP_STREAM_WS_URL = "wss://speech-test.orionstar.com/speech-bridge/ws/streaming";
    private static String PRE_RELEASE_STREAM_WS_URL = "wss://speech.orionstar.com/speech-bridge/ws/streaming";

    // nlp 结果流式返回,流式的SSE地址
    private static String RELEASE_STREAM_SSE_URL = "https://speech.orionstar.com/speech-bridge/events";
    private static String DEVELOP_STREAM_SSE_URL = "https://speech-test.orionstar.com/speech-bridge/events";
    private static String PRE_RELEASE_STREAM_SSE_URL = "https://speech-test.orionstar.com/speech-bridge/events";

    private static final String FILENAME = "kratosConfig.ini";
    private static final String CONFIGURLPATH = "/" + FILENAME;

    private static String Config_test = "test";
    private static String Config_release = "release";
    private static String Config_pre_release = "prerelease";

    private static ConfigFileUtil mConifgFile = new ConfigFileUtil();

    /**
     * 从配置文件中读取相关服务地址
     */
    public synchronized static void initUrlFromAssert() {
        File aConfigFile = initConfigFile();
        if (aConfigFile.exists()) {
            mConifgFile.load(aConfigFile);
            initFetchTokenUrl();
            initRefreshTokenUrl();
            initH2Url();
            initTextQueryUrl();
            initTtsUrl();
            initHeartUrl();
            initSpokermanUrl();
            initWakeupAudioUrl();
            initStreamWsUrl();
            initStreamSSEurl();
        }
    }

    /**
     * 读取服务器地址
     */
    public synchronized static void init(SpeechConfig mSpeechConfig) {
        if (null != mSpeechConfig) {
            mWhichRelease = Env.INSTANCE.getAgentEnv();
            Log.d(TAG, "build type is " + mWhichRelease);
            initUrlFromConfig(mSpeechConfig);
            initOtherEnviroment(mSpeechConfig);
        } else {
            mWhichRelease = SharedPreferencesUtil.getBuildType();
            Log.d(TAG, "build type is " + mWhichRelease);
            initUrlFromAssert();
        }
    }

    private static void initOtherEnviroment(SpeechConfig mSpeechConfig) {
        String clientID = mSpeechConfig.getClientID();
        if (!TextUtils.isEmpty(clientID)) {
            OtherEnvironment.setClientId(clientID);
        }
        String clientSecret = mSpeechConfig.getClientSecret();
        if (!TextUtils.isEmpty(clientSecret)) {
            OtherEnvironment.setClientSecret(clientSecret);
        }
        String pid = mSpeechConfig.getPid();
        if (!TextUtils.isEmpty(pid)) {
            OtherEnvironment.setPid(pid);
        }
        String configTts = mSpeechConfig.getConfigTts();
        if (!TextUtils.isEmpty(configTts)) {
            TtsConfig.setTtsMode(configTts);
        }
        boolean isDisableAsr = mSpeechConfig.isDisableAsr();
        AsrConfig.disableAsr(isDisableAsr);
        boolean audioInfoEnable = mSpeechConfig.isAudioInfoEnable();
        AsrConfig.setAudioInfoEnable(audioInfoEnable);
        String audioDataFrom = mSpeechConfig.getAudioDataFrom();
        if (!TextUtils.isEmpty(audioDataFrom)) {
            AsrConfig.setAsrAudioDataFrom(audioDataFrom);
        }
        String wakeupAlgorithm = mSpeechConfig.getWakeupAlgorithm();
        if (!TextUtils.isEmpty(wakeupAlgorithm)) {
            WakeupConfig.setWakeupAlgorithm(wakeupAlgorithm);
        }
        String vadAlgorithm = mSpeechConfig.getVadAlgorithm();
        if (!TextUtils.isEmpty(vadAlgorithm)) {
            AsrConfig.setVadAlgorithm(vadAlgorithm);
        }
        int logLevel = mSpeechConfig.getLogLevel();
        OtherEnvironment.setLogLevel(logLevel);
    }

    private static void initUrlFromConfig(SpeechConfig mSpeechConfig) {
        if (mWhichRelease == RELEASE) {
            SpeechConfig.SpeechServiceUrl releaseUrl = mSpeechConfig.getReleaseUrl();
            if (null != releaseUrl) {
                Log.d(TAG, "release url is " + releaseUrl);
                if (!TextUtils.isEmpty(releaseUrl.getFetchToken())) {
                    RELEASE_FETCH_TOKEN_URL = releaseUrl.getFetchToken();
                }
                if (!TextUtils.isEmpty(releaseUrl.getRefreshToken())) {
                    RELEASE_REFRESH_TOKEN_URL = releaseUrl.getRefreshToken();
                }
                if (!TextUtils.isEmpty(releaseUrl.getASR())) {
                    RELEASE_H2_URL = releaseUrl.getASR();
                }
                if (!TextUtils.isEmpty(releaseUrl.getQueryByText())) {
                    RELEASE_TEXT_QUERY_URL = releaseUrl.getQueryByText();
                }
                if (!TextUtils.isEmpty(releaseUrl.getTTS())) {
                    RELEASE_TTS_URL = releaseUrl.getTTS();
                }
                if (!TextUtils.isEmpty(releaseUrl.getSpokerman())) {
                    RELEASE_SPOKERMAN_URL = releaseUrl.getSpokerman();
                }
                if (!TextUtils.isEmpty(releaseUrl.getWakeupAudio())) {
                    RELEASE_WAKEUP_AUDIO_URL = releaseUrl.getWakeupAudio();
                }
                if (!TextUtils.isEmpty(releaseUrl.getHeart())) {
                    RELEASE_ECHO_URL = releaseUrl.getHeart();
                }
                if (!TextUtils.isEmpty(releaseUrl.getStreamWsUrl())) {
                    RELEASE_STREAM_WS_URL = releaseUrl.getStreamWsUrl();
                }
                if (!TextUtils.isEmpty(releaseUrl.getStreamSseUrl())) {
                    RELEASE_STREAM_SSE_URL = releaseUrl.getStreamSseUrl();
                }
            } else {
                Log.e(TAG, "release url is null");
            }

        } else if (mWhichRelease == DEVELOP) {
            SpeechConfig.SpeechServiceUrl testUrl = mSpeechConfig.getTestUrl();
            if (null != testUrl) {
                Log.d(TAG, "test url is " + testUrl);
                if (!TextUtils.isEmpty(testUrl.getFetchToken())) {
                    DEVELOP_FETCH_TOKEN_URL = testUrl.getFetchToken();
                }
                if (!TextUtils.isEmpty(testUrl.getRefreshToken())) {
                    DEVELOP_REFRESH_TOKEN_URL = testUrl.getRefreshToken();
                }
                if (!TextUtils.isEmpty(testUrl.getASR())) {
                    DEVELOP_H2_URL = testUrl.getASR();
                }
                if (!TextUtils.isEmpty(testUrl.getQueryByText())) {
                    DEVELOP_TEXT_QUERY_URL = testUrl.getQueryByText();
                }
                if (!TextUtils.isEmpty(testUrl.getTTS())) {
                    DEVELOP_TTS_URL = testUrl.getTTS();
                }
                if (!TextUtils.isEmpty(testUrl.getSpokerman())) {
                    DEVELOP_SPOKERMAN_URL = testUrl.getSpokerman();
                }
                if (!TextUtils.isEmpty(testUrl.getWakeupAudio())) {
                    DEVELOP_WAKEUP_AUDIO_URL = testUrl.getWakeupAudio();
                }
                if (!TextUtils.isEmpty(testUrl.getHeart())) {
                    DEVELOP_ECHO_URL = testUrl.getHeart();
                }
                if (!TextUtils.isEmpty(testUrl.getStreamWsUrl())) {
                    DEVELOP_STREAM_WS_URL = testUrl.getStreamWsUrl();
                }
                if (!TextUtils.isEmpty(testUrl.getStreamSseUrl())) {
                    DEVELOP_STREAM_SSE_URL = testUrl.getStreamSseUrl();
                }
            } else {
                Log.e(TAG, "test url is null");
            }
        } else if (mWhichRelease == PRE_RELEASE) {
            SpeechConfig.SpeechServiceUrl preReleaseUrl = mSpeechConfig.getPreReleaseUrl();
            if (null != preReleaseUrl) {
                Log.d(TAG, "preReleaseUrl url is " + preReleaseUrl);
                if (!TextUtils.isEmpty(preReleaseUrl.getFetchToken())) {
                    PRE_RELEASE_FETCH_TOKEN_URL = preReleaseUrl.getFetchToken();
                }
                if (!TextUtils.isEmpty(preReleaseUrl.getRefreshToken())) {
                    PRE_REFRESH_TOKEN_URL = preReleaseUrl.getRefreshToken();
                }
                if (!TextUtils.isEmpty(preReleaseUrl.getASR())) {
                    PRE_RELEASE_H2_URL = preReleaseUrl.getASR();
                }
                if (!TextUtils.isEmpty(preReleaseUrl.getQueryByText())) {
                    PRE_RELEASE_TEXT_QUERY_URL = preReleaseUrl.getQueryByText();
                }
                if (!TextUtils.isEmpty(preReleaseUrl.getTTS())) {
                    PRE_RELEASE_TTS_URL = preReleaseUrl.getTTS();
                }
                if (!TextUtils.isEmpty(preReleaseUrl.getSpokerman())) {
                    PRE_RELEASE_SPOKERMAN_URL = preReleaseUrl.getSpokerman();
                }
                if (!TextUtils.isEmpty(preReleaseUrl.getWakeupAudio())) {
                    PRE_RELEASE_WAKEUP_AUDIO_URL = preReleaseUrl.getWakeupAudio();
                }
                if (!TextUtils.isEmpty(preReleaseUrl.getHeart())) {
                    PRE_RELEASE_ECHO_URL = preReleaseUrl.getHeart();
                }
                if (!TextUtils.isEmpty(preReleaseUrl.getStreamWsUrl())) {
                    PRE_RELEASE_STREAM_WS_URL = preReleaseUrl.getStreamWsUrl();
                }
                if (!TextUtils.isEmpty(preReleaseUrl.getStreamSseUrl())) {
                    PRE_RELEASE_STREAM_SSE_URL = preReleaseUrl.getStreamSseUrl();
                }
            } else {
                Log.e(TAG, "preRelease url is null");
            }
        } else {
            Log.e(TAG, "mWhichRelease is error value " + mWhichRelease);
        }
    }

    private static File initConfigFile() {
        String configPath = ClientConfig.BASE_LOCAL_PATH + "/Config/";
        File file = new File(configPath);
        File aConfigFile = new File(
                configPath + CONFIGURLPATH);
        if (!file.exists()) {
            boolean mkdirs = file.mkdirs();
            Log.d(TAG, "create file " + mkdirs + ", the path is " + configPath);
        }
        String releasenum = RobotSettings.getVersion();
        String lastReleaseNum = SharedPreferencesUtil.getLastReleaseNum();
        boolean exists = aConfigFile.exists();
        Log.d(TAG, "releasenum:" + releasenum + ",lastReleaseNum:" + lastReleaseNum
                + "exists:" + exists);
        //先判断版本号是否一致，如果不一致有可能上次复制文件失败
        if (!TextUtils.equals(releasenum, lastReleaseNum)
                || !exists) {
            boolean isSuccess = FileUtil.copyAssetsFile(FILENAME,
                    configPath);
            //复制成功才会设置版本号
            if (isSuccess) {
                SharedPreferencesUtil.setReleaseNum(releasenum);
            }
        }
        return aConfigFile;
    }


    /**
     * 从配置文件读取 获取token的地址
     */
    private static void initFetchTokenUrl() {
        String config_FetchToken = "FetchTokenUrl";
        String aTemp = (String) mConifgFile.get(config_FetchToken, Config_release);
        if (!TextUtils.isEmpty(aTemp)) {
            RELEASE_FETCH_TOKEN_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(config_FetchToken, Config_test);
        if (!TextUtils.isEmpty(aTemp)) {
            DEVELOP_FETCH_TOKEN_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(config_FetchToken, Config_pre_release);
        if (!TextUtils.isEmpty(aTemp)) {
            PRE_RELEASE_FETCH_TOKEN_URL = aTemp;
        }
    }

    /**
     * 从配置文件读取 刷新token的地址
     */
    private static void initRefreshTokenUrl() {
        String config_Access = "RefreshTokenUrl";
        String aTemp = (String) mConifgFile.get(config_Access, Config_release);
        if (!TextUtils.isEmpty(aTemp)) {
            RELEASE_REFRESH_TOKEN_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(config_Access, Config_test);
        if (!TextUtils.isEmpty(aTemp)) {
            DEVELOP_REFRESH_TOKEN_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(config_Access, Config_pre_release);
        if (!TextUtils.isEmpty(aTemp)) {
            PRE_REFRESH_TOKEN_URL = aTemp;
        }
    }

    /**
     * 从配置文件读取 h2的地址
     */
    private static void initH2Url() {
        String H2_Url = "H2Url";
        String aTemp = (String) mConifgFile.get(H2_Url, Config_release);
        if (!TextUtils.isEmpty(aTemp)) {
            RELEASE_H2_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(H2_Url, Config_test);
        if (!TextUtils.isEmpty(aTemp)) {
            DEVELOP_H2_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(H2_Url, Config_pre_release);
        if (!TextUtils.isEmpty(aTemp)) {
            PRE_RELEASE_H2_URL = aTemp;
        }
    }

    /**
     * 从配置文件读取 testQuery的地址
     */
    private static void initTextQueryUrl() {
        String Text_Query_Url = "TextQueryUrl";
        String aTemp = (String) mConifgFile.get(Text_Query_Url, Config_release);
        if (!TextUtils.isEmpty(aTemp)) {
            RELEASE_TEXT_QUERY_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(Text_Query_Url, Config_test);
        if (!TextUtils.isEmpty(aTemp)) {
            DEVELOP_TEXT_QUERY_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(Text_Query_Url, Config_pre_release);
        if (!TextUtils.isEmpty(aTemp)) {
            PRE_RELEASE_TEXT_QUERY_URL = aTemp;
        }
    }

    /**
     * 从配置文件读取 Tts的地址
     */
    private static void initTtsUrl() {
        String Tts_Url = "TtsUrl";
        String aTemp = (String) mConifgFile.get(Tts_Url, Config_release);
        if (!TextUtils.isEmpty(aTemp)) {
            RELEASE_TTS_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(Tts_Url, Config_test);
        if (!TextUtils.isEmpty(aTemp)) {
            DEVELOP_TTS_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(Tts_Url, Config_pre_release);
        if (!TextUtils.isEmpty(aTemp)) {
            PRE_RELEASE_TTS_URL = aTemp;
        }
    }

    /**
     * 从配置文件读取 心跳的地址
     */
    private static void initHeartUrl() {
        String Heart_Url = "HeartUrl";
        String aTemp = (String) mConifgFile.get(Heart_Url, Config_release);
        if (!TextUtils.isEmpty(aTemp)) {
            RELEASE_ECHO_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(Heart_Url, Config_test);
        if (!TextUtils.isEmpty(aTemp)) {
            DEVELOP_ECHO_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(Heart_Url, Config_pre_release);
        if (!TextUtils.isEmpty(aTemp)) {
            PRE_RELEASE_ECHO_URL = aTemp;
        }
    }

    /**
     * 从配置文件读取 发音人列表的地址
     */
    private static void initSpokermanUrl() {
        String Spokerman_Url = "SpokermanUrl";
        String aTemp = (String) mConifgFile.get(Spokerman_Url, Config_release);
        if (!TextUtils.isEmpty(aTemp)) {
            RELEASE_SPOKERMAN_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(Spokerman_Url, Config_test);
        if (!TextUtils.isEmpty(aTemp)) {
            DEVELOP_SPOKERMAN_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(Spokerman_Url, Config_pre_release);
        if (!TextUtils.isEmpty(aTemp)) {
            PRE_RELEASE_SPOKERMAN_URL = aTemp;
        }
    }

    /**
     * 从配置文件读取 唤醒应答音频的地址
     */
    private static void initWakeupAudioUrl() {
        String WakeupAudioUrl = "WakeupAudioUrl";
        String aTemp = (String) mConifgFile.get(WakeupAudioUrl, Config_release);
        if (!TextUtils.isEmpty(aTemp)) {
            RELEASE_WAKEUP_AUDIO_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(WakeupAudioUrl, Config_test);
        if (!TextUtils.isEmpty(aTemp)) {
            DEVELOP_WAKEUP_AUDIO_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(WakeupAudioUrl, Config_pre_release);
        if (!TextUtils.isEmpty(aTemp)) {
            PRE_RELEASE_WAKEUP_AUDIO_URL = aTemp;
        }
    }

    /**
     * 从配置文件读取 WS的地址
     */
    private static void initStreamWsUrl() {
        String StreamUrl = "StreamUrl";
        String aTemp = (String) mConifgFile.get(StreamUrl, Config_release);
        if (!TextUtils.isEmpty(aTemp)) {
            RELEASE_STREAM_WS_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(StreamUrl, Config_test);
        if (!TextUtils.isEmpty(aTemp)) {
            DEVELOP_STREAM_WS_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(StreamUrl, Config_pre_release);
        if (!TextUtils.isEmpty(aTemp)) {
            PRE_RELEASE_STREAM_WS_URL = aTemp;
        }
        Log.d(TAG,"initStreamWs aTemp:"+aTemp);
    }

    /**
     * 从配置文件读取 SSE的地址
     */
    private static void initStreamSSEurl() {
        String StreamUrl = "SseUrl";
        String aTemp = (String) mConifgFile.get(StreamUrl, Config_release);
        if (!TextUtils.isEmpty(aTemp)) {
            RELEASE_STREAM_SSE_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(StreamUrl, Config_test);
        if (!TextUtils.isEmpty(aTemp)) {
            DEVELOP_STREAM_SSE_URL = aTemp;
        }
        aTemp = (String) mConifgFile.get(StreamUrl, Config_pre_release);
        if (!TextUtils.isEmpty(aTemp)) {
            PRE_RELEASE_STREAM_SSE_URL = aTemp;
        }
        Log.d(TAG,"initStreamSSE aTemp:"+aTemp);
    }

    /**
     * 获取token相关配置
     */
    @Keep
    public static final class TokenConfig {
        public static String getFetchTokenURL() {
            switch (mWhichRelease) {
                case RELEASE:
                    Log.d(TAG, "FetchTokenConfig: " + mWhichRelease
                            + ". fetchToken_url: " + RELEASE_FETCH_TOKEN_URL);
                    return RELEASE_FETCH_TOKEN_URL;
                case DEVELOP:
                    Log.d(TAG, "FetchTokenConfig: " + mWhichRelease
                            + ". fetchToken_url: " + DEVELOP_FETCH_TOKEN_URL);
                    return DEVELOP_FETCH_TOKEN_URL;
                case PRE_RELEASE:
                    Log.d(TAG, "FetchTokenConfig: " + mWhichRelease
                            + ". fetchToken_url: " + PRE_RELEASE_FETCH_TOKEN_URL);
                    return PRE_RELEASE_FETCH_TOKEN_URL;
                default:
                    break;
            }
            return RELEASE_FETCH_TOKEN_URL;
        }

        public static String getRefreshTokenURL() {
            switch (mWhichRelease) {
                case RELEASE:
                    Log.d(TAG, "RefreshTokenConfig: " + mWhichRelease + ". refreshToken_url: "
                            + RELEASE_REFRESH_TOKEN_URL);
                    return RELEASE_REFRESH_TOKEN_URL;
                case DEVELOP:
                    Log.d(TAG, "RefreshTokenConfig: " + mWhichRelease + ". refreshToken_url: "
                            + DEVELOP_REFRESH_TOKEN_URL);
                    return DEVELOP_REFRESH_TOKEN_URL;
                case PRE_RELEASE:
                    Log.d(TAG, "RefreshTokenConfig: " + mWhichRelease + ". refreshToken_url: "
                            + PRE_REFRESH_TOKEN_URL);
                    return PRE_REFRESH_TOKEN_URL;
                default:
                    break;
            }
            return RELEASE_REFRESH_TOKEN_URL;
        }
    }

    /**
     * Asr相关配置
     */
    @Keep
    public static final class AsrConfig {
        private static LangParamsEnum asrLanguage = LangParamsEnum.ZH_CN;
        private static boolean isAudioDataFromOut;
        private static boolean isDisableAsr;
        private static boolean isAudioInfoEnable;
        private static String vadAlgorithm = SpeechConfig.CONFIG_VAD_1;

        public static String getH2Url() {
            switch (mWhichRelease) {
                case RELEASE:
                    Log.i(TAG, "getH2Url: " + mWhichRelease + ". h2_url: " + RELEASE_H2_URL);
                    return RELEASE_H2_URL;
                case DEVELOP:
                    Log.i(TAG, "getH2Url: " + mWhichRelease + ". h2_url: " + DEVELOP_H2_URL);
                    return DEVELOP_H2_URL;
                case PRE_RELEASE:
                    Log.i(TAG, "getH2Url: " + mWhichRelease + ". h2_url: "
                            + PRE_RELEASE_H2_URL);
                    return PRE_RELEASE_H2_URL;
                default:
                    break;
            }
            return RELEASE_H2_URL;
        }

        public static String getTextQueryUrl() {
            switch (mWhichRelease) {
                case RELEASE:
                    Log.d(TAG, "getTextQueryUrl: " + mWhichRelease + ". text_query_url: "
                            + RELEASE_TEXT_QUERY_URL);
                    return RELEASE_TEXT_QUERY_URL;
                case DEVELOP:
                    Log.d(TAG, "getTextQueryUrl: " + mWhichRelease + ". text_query_url: "
                            + DEVELOP_TEXT_QUERY_URL);
                    return DEVELOP_TEXT_QUERY_URL;
                case PRE_RELEASE:
                    Log.d(TAG, "getTextQueryUrl: " + mWhichRelease + ". text_query_url: "
                            + PRE_RELEASE_TEXT_QUERY_URL);
                    return PRE_RELEASE_TEXT_QUERY_URL;
                default:
                    break;
            }
            return RELEASE_TEXT_QUERY_URL;
        }

        public static String getEchoUrl() {
            switch (mWhichRelease) {
                case RELEASE:
                    return RELEASE_ECHO_URL;
                case DEVELOP:
                    return DEVELOP_ECHO_URL;
                case PRE_RELEASE:
                    return PRE_RELEASE_ECHO_URL;
                default:
                    break;
            }
            return RELEASE_ECHO_URL;
        }

        public static String getStreamWsUrl() {
            switch (mWhichRelease) {
                case RELEASE:
                    return RELEASE_STREAM_WS_URL;
                case DEVELOP:
                    return DEVELOP_STREAM_WS_URL;
                case PRE_RELEASE:
                    return PRE_RELEASE_STREAM_WS_URL;
                default:
                    break;
            }
            return RELEASE_STREAM_WS_URL;
        }

        public static String getStreamSseUrl() {
            switch (mWhichRelease) {
                case RELEASE:
                    return RELEASE_STREAM_SSE_URL;
                case DEVELOP:
                    return DEVELOP_STREAM_SSE_URL;
                case PRE_RELEASE:
                    return PRE_RELEASE_STREAM_SSE_URL;
                default:
                    break;
            }
            return RELEASE_STREAM_SSE_URL;
        }

        public static int getAsrLanguage() {
            return AsrConfig.asrLanguage.codeValue;
        }

        public static void setAsrLanguage(LangParamsEnum langParamsEnum) {
            AsrConfig.asrLanguage = langParamsEnum;
        }

        public static String getAsrLanguageName(){
            return AsrConfig.asrLanguage.codeName;
        }

        public static int environment(){
            return EnvironmentConfig.mWhichRelease;
        }

        private static void setAsrAudioDataFrom(String audioDataFrom) {
            if (TextUtils.equals(audioDataFrom, SpeechConfig.CONFIG_AUDIO_DATA_FROM_OUT)) {
                AsrConfig.isAudioDataFromOut = true;
            } else if (TextUtils.equals(audioDataFrom, SpeechConfig.CONFIG_AUDIO_DATA_FROM_INNER)) {
                AsrConfig.isAudioDataFromOut = false;
            }
        }

        public static boolean isAudioDataFromOut() {
            return isAudioDataFromOut;
        }

        public static void setAudioInfoEnable(boolean isAudioInfoEnable) {
            AsrConfig.isAudioInfoEnable = isAudioInfoEnable;
        }

        public static boolean isAudioInfoEnable() {
            return isAudioInfoEnable;
        }

        public static void disableAsr(boolean isDisableAsr) {
            AsrConfig.isDisableAsr = isDisableAsr;
        }

        public static boolean isDisableAsr() {
            return isDisableAsr;
        }

        public static void setVadAlgorithm(String vadAlgorithm) {
            AsrConfig.vadAlgorithm = vadAlgorithm;
        }

        public static String getVadAlgorithm() {
            return vadAlgorithm;
        }
    }

    /**
     * tts相关配置
     */
    @Keep
    public static final class TtsConfig {
        private static boolean ttsIsOnline = true;
//        private static int ttsMode = TTSPlayer.MODE_MIX;
        private static LangParamsEnum ttsLanguage;

        /**
         * 获取对应的URL
         */
        public static String getTTSURL() {
            switch (mWhichRelease) {
                case RELEASE:
                    Log.d(TAG, "release: " + mWhichRelease + ". tts_url: " + RELEASE_TTS_URL);
                    return RELEASE_TTS_URL;
                case DEVELOP:
                    Log.d(TAG, "release: " + mWhichRelease + ". tts_url: " + DEVELOP_TTS_URL);
                    return DEVELOP_TTS_URL;
                case PRE_RELEASE:
                    Log.d(TAG, "release: " + mWhichRelease + ". tts_url: "
                            + PRE_RELEASE_TTS_URL);
                    return PRE_RELEASE_TTS_URL;
                default:
                    break;
            }
            return RELEASE_TTS_URL;
        }

        /**
         * 获取发音人列表的URL
         */
        public static String getSpokermanURL() {
            switch (mWhichRelease) {
                case RELEASE:
                    Log.d(TAG, "release: " + mWhichRelease + ". spokerman_url: "
                            + RELEASE_SPOKERMAN_URL);
                    return RELEASE_SPOKERMAN_URL;
                case DEVELOP:
                    Log.d(TAG, "release: " + mWhichRelease + ". spokerman_url: "
                            + DEVELOP_SPOKERMAN_URL);
                    return DEVELOP_SPOKERMAN_URL;
                case PRE_RELEASE:
                    Log.d(TAG, "release: " + mWhichRelease + ". spokerman_url: "
                            + PRE_RELEASE_SPOKERMAN_URL);
                    return PRE_RELEASE_SPOKERMAN_URL;
                default:
                    break;
            }
            return RELEASE_SPOKERMAN_URL;
        }

        /**
         * 获取唤醒应答音频的URL
         */
        public static String getWakeupAudioURL() {
            switch (mWhichRelease) {
                case RELEASE:
                    Log.d(TAG, "release: " + mWhichRelease + ". wakeup_audio_url: "
                            + RELEASE_WAKEUP_AUDIO_URL);
                    return RELEASE_WAKEUP_AUDIO_URL;
                case DEVELOP:
                    Log.d(TAG, "release: " + mWhichRelease + ". wakeup_audio_url: "
                            + DEVELOP_WAKEUP_AUDIO_URL);
                    return DEVELOP_WAKEUP_AUDIO_URL;
                case PRE_RELEASE:
                    Log.d(TAG, "release: " + mWhichRelease + ". wakeup_audio_url: "
                            + PRE_RELEASE_WAKEUP_AUDIO_URL);
                    return PRE_RELEASE_WAKEUP_AUDIO_URL;
                default:
                    break;
            }
            return RELEASE_WAKEUP_AUDIO_URL;
        }

        private static void setTtsMode(String configTts) {
//            Log.d(TAG, "configTts is " + configTts);
//            switch (configTts) {
//                case SpeechConfig.CONFIG_TTS_OFFLINE:
//                    ttsIsOnline = false;
//                    ttsMode = TTSPlayer.MODE_OFFLINE;
//                    break;
//                case SpeechConfig.CONFIG_TTS_ONLINE:
//                    ttsIsOnline = true;
//                    ttsMode = TTSPlayer.MODE_ONLINE;
//                    break;
//                case SpeechConfig.CONFIG_TTS_MIX:
//                    ttsIsOnline = true;
//                    ttsMode = TTSPlayer.MODE_MIX;
//                    break;
//                default:
//                    break;
//            }
        }

        public static boolean isTtsIsOnline() {
            return ttsIsOnline;
        }

//        public static int getTtsMode() {
//            return ttsMode;
//        }

        public static void settTtsLanguage(LangParamsEnum langParamsEnum) {
            TtsConfig.ttsLanguage = langParamsEnum;
        }

        public static LangParamsEnum getLangParams() {
            if (ttsLanguage == null) {
                ttsLanguage = LangParamsEnum.getLangEnumByCodeName(
                        Locale.getDefault().getLanguage() + "_" + Locale.getDefault().getCountry()
                );
            }
            return TtsConfig.ttsLanguage;
        }

        public static String getTtsLanguage() {
            return getLangParams().codeName;
        }
    }

    /**
     * wakeup相关配置
     */
    @Keep
    public static final class WakeupConfig {
        private static String wakeupAlgorithm = SpeechConfig.CONFIG_WAKUP_SINGLE_WAVE_NET;

        public static String getWakeupAlgorithm() {
            return wakeupAlgorithm;
        }

        public static void setWakeupAlgorithm(String wakeupAlgorithm) {
            WakeupConfig.wakeupAlgorithm = wakeupAlgorithm;
        }
    }

    /**
     * 环境相关配置
     */
    @Keep
    public static final class OtherEnvironment {
        private static String clientId = "orion.ovs.client.1514259512471";
        private static String clientSecret = "07E12AF3E6952125C770B0B0C743342D";

        private static int logLevel = Log.WARN;

        public static String getClientId() {
            return clientId;
        }

        private static void setClientId(String clientId) {
            OtherEnvironment.clientId = clientId;
        }

        public static String getPid() {
            return DataStore.INSTANCE.getProductId();
        }

        public static void setPid(String pid) {
            DataStore.INSTANCE.setProductId(pid);
        }

        public static void setClientSecret(String clientSecret) {
            OtherEnvironment.clientSecret = clientSecret;
        }

        public static String getClientSecret() {
            return clientSecret;
        }

        public static void setLogLevel(int logLevel) {
            OtherEnvironment.logLevel = logLevel;
        }

        public static int getLogLevel() {
            return logLevel;
        }
    }
}
