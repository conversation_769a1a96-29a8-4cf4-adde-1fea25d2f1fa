package com.ainirobot.speechasrservice.kratos.auth;

import android.support.annotation.Keep;

/**
 * Created by 'ling<PERSON><PERSON><PERSON>' at 2017-07-31 14:35
 * 猎户鉴权逻辑的回调接口。
 */
@Keep
public interface AuthenticationListener {
    /**
     * HTTP错误
     */
    int CODE_HTTP_ERROR = 110;
    /**
     * 其它错误
     */
    int CODE_UNKNOW_ERROR = 120;
    int CODE_TEXT_EMPTY = 50;

    int CODE_HTTP_OK = 200;

    //
    // 700 段的状态码为猎户服务端返回的
    //
    int INVALID_REQUEST = 701;

    int UNAUTHORIZED_CLIENT = 702;

    int ACCESS_DENIED = 703;

    int UNSUPPORTED_RESPONSE_TYPE = 704;

    int INVALID_AUTH_SCOPE = 705;

    int SERVER_ERROR = 706;

    int TEMPORARILY_UNAVAILABLE = 707;

    int INVALID_CLIENT = 708;

    int INVALID_GRANT = 709;

    int UNSUPPORTED_GRANT_TYPE = 710;

    int INVALID_VERIFY_CODE_EXCEPTION = 711;

    int INVALID_REFRESH_TOKEN = 721;

    int INVALID_REDIRECT_URI = 712;

    int INVALID_AUTH_CODE = 713;

    int EXPIRE_ACCESS_TOKEN = 720;

    int FRAME_INTERNAL_EXCEPTION = 715;

    int ERROR_REFRESH_ = 500;

    int EXPIRE_REFRESH_TOKEN = 1000;

    /**
     * 权限校验成功
     */
    void onSucceed();

    /**
     * 权限校验失败
     *
     * @param code 错误码
     * @param msg  错误描述
     */
    void onFailed(int code, String msg);
}
