package com.ainirobot.speechasrservice.kratos.auth;


import android.os.Handler;
import android.util.Log;

import com.ainirobot.speechasrservice.kratos.cofig.ClientConfig;
import com.ainirobot.speechasrservice.utils.Executors;
import com.ainirobot.speechasrservice.utils.SharedPreferencesUtil;
import com.ainirobot.speechasrservice.utils.Singleton;


public class AccessTokenManger {
    public static final String TAG = AccessTokenManger.class.getSimpleName();
    private RefreshRunnable mRefreshRunnable;
    private Handler mMainHandler;
    private static Singleton<AccessTokenManger> mSingleton = new Singleton<AccessTokenManger>() {
        @Override
        protected AccessTokenManger create() {
            return new AccessTokenManger();
        }
    };

    private AccessTokenManger() {
        mRefreshRunnable = new RefreshRunnable();
        mMainHandler = Executors.getUiHandler();

    }

    public static AccessTokenManger getInstance() {
        return mSingleton.get();
    }

    private class RefreshRunnable implements Runnable {

        @Override
        public void run() {
            Log.i(TAG, "requestRefreshToken|run");
            requestRefreshToken();
        }
    }


    public void refreshToken() {
        Log.i(TAG, "getSpeakerToken");
        mMainHandler.removeCallbacks(mRefreshRunnable);
        mMainHandler.post(mRefreshRunnable);
    }


    private void requestRefreshToken() {
        AccessTokenRequest request = new AccessTokenRequest();
        String aRefreshToken = SharedPreferencesUtil.getAuthRefreshToken();
        request.getSpeakerToken(aRefreshToken, new AuthenticationListener() {
            @Override
            public void onSucceed() {
                mMainHandler.removeCallbacks(mRefreshRunnable);
                mMainHandler.postDelayed(mRefreshRunnable, ClientConfig.REPEAT_TIME);
            }

            @Override
            public void onFailed(int code, String msg) {
                mMainHandler.removeCallbacks(mRefreshRunnable);
                mMainHandler.postDelayed(mRefreshRunnable, ClientConfig.REPEAT_TIME);
            }
        });
    }

}
