package com.ainirobot.speechasrservice.kratos.auth.bean;

public class RefreshTokenResultBean {
    private String access_token;
    private String refresh_token;
    private String open_id;
    private int expires_in;

    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }

    public void setRefresh_token(String refresh_token) {
        this.refresh_token = refresh_token;
    }

    public void setOpen_id(String open_id) {
        this.open_id = open_id;
    }

    public void setExpires_in(int expires_in) {
        this.expires_in = expires_in;
    }

    public String getAccess_token() {
        return access_token;
    }

    public String getRefresh_token() {
        return refresh_token;
    }

    public String getOpen_id() {
        return open_id;
    }

    public int getExpires_in() {
        return expires_in;
    }

    @Override
    public String toString() {
        return "RefreshTokenResultBean{" +
                "access_token='" + access_token + '\'' +
                ", refresh_token='" + refresh_token + '\'' +
                ", open_id='" + open_id + '\'' +
                ", expires_in=" + expires_in +
                '}';
    }
}
