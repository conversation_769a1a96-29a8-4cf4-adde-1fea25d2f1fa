package com.ainirobot.speechasrservice.kratos.auth.utils;

import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.speechasrservice.utils.Md5Util;

import java.util.List;
import java.util.Objects;


/**
 * Created by 'lingyunxiao' at 2017-07-28 16:54
 */
public class SignUtil {

    public static String createSign(String secret, List<NameValueParam> list) {
        try {
            return getAppSign(list, secret);
        } catch (Exception e) {
            Log.e("Authman", "error", e);
        }
        return "";
    }

    private static String getAppSign(List<NameValueParam> params, String clientSecret)
            throws Exception {
        StringBuilder paramsStr = new StringBuilder();
        for (int i = 0; i < params.size(); i++) {
            NameValueParam param = params.get(i);
            if (0 != i) {
                paramsStr.append("&");
            }
            paramsStr.append(param.getName());
            paramsStr.append("=");
            paramsStr.append(param.getValue().toString());
        }

        // 拼接 appSecret
        if (!TextUtils.isEmpty(clientSecret)) {
            paramsStr.append("&client_secret=");
            paramsStr.append(clientSecret);
        }
        Log.i("Authman", "paramsStr : " + paramsStr);

        // base64
        String base64Sign = new String(Base64.getEncoder().encode(paramsStr.toString()
                .getBytes("UTF-8")), "UTF-8");
        Log.i("Authman", "base64Sign : " + base64Sign);

        // MD5 摘要并转小写
        String sign = Objects.requireNonNull(Md5Util.getStringMd5(base64Sign)).toLowerCase();
        Log.i("Authman", "generate sign : " + sign);
        Log.i("Authman", "generate sign length : " + sign.length());
        return sign;
    }
}
