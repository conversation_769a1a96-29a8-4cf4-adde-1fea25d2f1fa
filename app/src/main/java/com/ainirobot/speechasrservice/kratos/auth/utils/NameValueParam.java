package com.ainirobot.speechasrservice.kratos.auth.utils;

import android.support.annotation.NonNull;

import java.util.Objects;

/**
 * 用于封装请求参数 ，按字典序排序的对象
 */
public class NameValueParam implements Comparable<NameValueParam> {
    private String name;
    private Object value;

    public NameValueParam(String name, Object value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }


    public Object getValue() {
        return value;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "NameValueParam{" +
                "name='" + name + '\'' +
                ", value=" + value +
                '}';
    }

    @Override
    public int compareTo(@NonNull NameValueParam o) {
        if (null == name) {
            return 0;
        }
        return name.compareTo(o.getName());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof NameValueParam)) return false;
        NameValueParam that = (NameValueParam) o;
        return Objects.equals(getName(), that.getName()) &&
                Objects.equals(getValue(), that.getValue());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getName(), getValue());
    }
}
