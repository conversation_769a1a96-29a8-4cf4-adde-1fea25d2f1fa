/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.speechasrservice.data.spokesman;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.RobotOS;
import com.ainirobot.coreservice.client.hardware.RobotCore;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.speechasrservice.data.bean.SpokesmanBean;
import com.ainirobot.speechasrservice.data.bean.SpokesmanVersion;
import com.ainirobot.speechasrservice.data.bean.WakeupWordInfo;
import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Created by Orion on 2021/3/16.
 */
public class SpokesmanManager {
    private static final String TAG = SpokesmanManager.class.getSimpleName();
    private Gson mGson;
    private SpokesmanListHelper mSpokesmanListHelper;
    private SpokesmanVersionHelper mSpokesmanVersionHelper;
    private WakeupWordListHelper mWakeupWordListHelper;

    public SpokesmanManager(Context context) {
        this.mGson = new Gson();
        this.mSpokesmanListHelper = new SpokesmanListHelper(context);
        this.mSpokesmanVersionHelper = new SpokesmanVersionHelper(context);
        this.mWakeupWordListHelper = new WakeupWordListHelper(context);
        initDbObserver();
    }

    private void initDbObserver() {
        mSpokesmanVersionHelper.registerListener(
                SpokesmanVersionHelper.VersionField.SPOKESMAN_LIST_UUID,
                new ContentObserver(null) {
                    @Override
                    public void onChange(boolean selfChange, Uri uri) {
                        Log.i(TAG, "SPOKESMAN_LIST_UUID-onChange: uri=" + uri);
                        List<SpokesmanBean> spokesmanBeans = getAllSpokesmanList();
                        if (spokesmanBeans != null && spokesmanBeans.size() > 0) {
                            notifySupportSpokesmanChange(spokesmanBeans);
                        }
                    }
                });
    }

    private void notifySupportSpokesmanChange(List<SpokesmanBean> spokesmanBeans) {
        Log.i(TAG, "notifySupportSpokesmanChange: spokesmanBeans=" + spokesmanBeans.toString());
        RobotCore.sendStatusReport(RobotOS.SPEECH_SERVICE,
                Definition.STATUS_SUPPORT_SPOKESMAN_CHANGE,
                mGson.toJson(spokesmanBeans));
    }

    public List<SpokesmanBean> getAllSpokesmanList() {
        return mSpokesmanListHelper.getAllSpokesmanList();
    }

    public List<SpokesmanBean> getSpokesmanListByLanguage(String langCode) {
        return mSpokesmanListHelper.getSpokesmanListByLanguage(langCode);
    }

    public SpokesmanBean getSpokesman(String langCode, String type) {
        final List<SpokesmanBean> spokesmanBeans = getSpokesmanListByLanguage(langCode);
        if (null == spokesmanBeans || spokesmanBeans.isEmpty()) {
            return null;
        }
        for (SpokesmanBean spokesmanBean : spokesmanBeans) {
            if (spokesmanBean.getType().equals(type)) {
                return spokesmanBean;
            }
        }
        return null;
    }

    /**
     * 发言人-更新服务端支持的列表信息
     *
     * @param spokesmanBeans 为保证uuid的有效性，这里需要只传服务端字段：langCode,type,nickname,is_default
     * @return
     */
    public boolean setSpokesmanList(List<SpokesmanBean> spokesmanBeans) {
        Log.i(TAG, "setSpokesmanList: params=" + spokesmanBeans);
        if (null == spokesmanBeans || spokesmanBeans.size() == 0) {
            Log.e(TAG, "setSpokesmanList: Params null error!");
            return false;
        }
        String serverUuid = createSpokesDataUUID(mGson.toJson(spokesmanBeans));
        String localUuid = mSpokesmanVersionHelper.getSpokesmanListUUID();
        Log.i(TAG, "setSpokesmanList: serverUuid=" + serverUuid + " localUuid=" + localUuid);
        if (serverUuid.equals(localUuid)) {
            Log.e(TAG, "setSpokesmanList: No data change!");
            return true;
        }
        boolean result = insertSpokesmanList(spokesmanBeans);
        Log.i(TAG, "setSpokesmanList: result=" + result);
        if (result) {
            if (TextUtils.isEmpty(localUuid)) {
                SpokesmanVersion spokesmanVersion = new SpokesmanVersion();
                spokesmanVersion.setSpokesmanListUuid(serverUuid);
                mSpokesmanVersionHelper.insert(spokesmanVersion);
            } else {
                mSpokesmanVersionHelper.updateSpokesmanListUUID(localUuid, serverUuid);
            }
            printAllSpokesmanData();
        }
        return result;
    }

    /**
     * 发言人-插入新数据 & 恢复本地数据
     *
     * @param serverList
     * @return
     */
    private boolean insertSpokesmanList(List<SpokesmanBean> serverList) {
        if (serverList == null || serverList.size() <= 0) {
            Log.e(TAG, "setSpokesmanList: Server list null error!");
            return false;
        }
        List<SpokesmanBean> localList = mSpokesmanListHelper.getAllSpokesmanList();
        mSpokesmanListHelper.deleteAll();
        List<SpokesmanBean> newList = new ArrayList<>();
        for (SpokesmanBean serverBean : serverList) {
            serverBean.setIsCurrent(SpokesmanBean.UseState.NOT_CURRENT);
            if (localList.size() == 0) {
                if (serverBean.getLangCode().equals(EnvironmentConfig.TtsConfig.getTtsLanguage())
                        && serverBean.getType().equals(RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTING_SPEAKER_ROLE))) {
                    serverBean.setIsCurrent(SpokesmanBean.UseState.CURRENT);
                }
            } else {
                for (SpokesmanBean localBean : localList) {
                    if (localBean.equals(serverBean) && localBean.isCurrentSpokesman()) {
                        serverBean.setIsCurrent(SpokesmanBean.UseState.CURRENT);
                    }
                }
            }
            newList.add(serverBean);
        }
        mSpokesmanListHelper.bulkInsert(newList);
        return true;
    }

    /**
     * 设置当前使用的发言人
     *
     * @param langCode
     * @param type
     */
    public boolean setCurrentSpokesman(String langCode, String type) {
        Log.i(TAG, "setCurrentSpokesman: langCode=" + langCode + " type=" + type);
        String currentType = mSpokesmanListHelper.getUserDefaultSpokesman(langCode);
        if (!TextUtils.isEmpty(currentType) && currentType.equals(type)) {
            Log.d(TAG, "setCurrentSpokesman: Current is just " + type);
            return true;
        }
        mSpokesmanListHelper.updateSpecialLangCurrentState(langCode,
                SpokesmanBean.UseState.NOT_CURRENT);
        mSpokesmanListHelper.updateSpecialTypeCurrentState(langCode, type,
                SpokesmanBean.UseState.CURRENT);
        printAllSpokesmanData();
        return true;
    }

    public List<WakeupWordInfo> getAllWakeupWordList() {
        return mWakeupWordListHelper.getAllWakeupWordList();
    }

    public List<WakeupWordInfo> getUnDownloadWakeupWordList() {
        return mWakeupWordListHelper.getUnDownloadWakeupWordList();
    }

    public List<String> getWakeupAudioByCurrentType(String langCode, String type) {
        return mWakeupWordListHelper.getWakeupAudioByCurrentType(langCode, type);
    }

    /**
     * 设置唤醒语本地存储路径和状态值
     *
     * @param langCode
     * @param type
     * @param fileName
     * @param path
     * @param state
     * @return
     */
    public int updateVoiceLocalPathAndState(String langCode, String type, String fileName, String path, int state) {
        return mWakeupWordListHelper.updateVoiceLocalPathAndState(langCode, type, fileName, path, state);
    }

    /**
     * 获取当前语言下默认发音人
     *
     * @param langCode 语言码
     * @return
     */
    public String getDefaultSpokesman(String langCode) {
        Log.d(TAG, "getDefaultSpokesman: langCode=" + langCode);
        String currentSpokesman = mSpokesmanListHelper.getUserDefaultSpokesman(langCode);
        Log.d(TAG, "getUserDefaultSpokesman: currentSpokesman=" + currentSpokesman);
        if (TextUtils.isEmpty(currentSpokesman)) {
            currentSpokesman = mSpokesmanListHelper.getServerDefaultSpokesman(langCode);
            Log.d(TAG, "getServerDefaultSpokesman: currentSpokesman=" + currentSpokesman);
        }
        return currentSpokesman;
    }

    /**
     * 唤醒语-更新服务端支持的列表信息
     *
     * @param wakeupWordInfos 为保证uuid的有效性，这里需要只传服务端字段：langCode,type,text,serverFileName
     * @return
     */
    public boolean setWakeupWordList(List<WakeupWordInfo> wakeupWordInfos) {
        Log.i(TAG, "setWakeupWordList: params=" + wakeupWordInfos);
        if (null == wakeupWordInfos || wakeupWordInfos.size() == 0) {
            Log.e(TAG, "setWakeupWordList: Params null error!");
            return false;
        }
        String serverUuid = createSpokesDataUUID(mGson.toJson(wakeupWordInfos));
        SpokesmanVersion versionInfo = mSpokesmanVersionHelper.getVersionInfo();
        String localWakeupListUuid = "";
        String localSpokesmanListUuid = "";
        if (null != versionInfo) {
            localWakeupListUuid = versionInfo.getWakeupListUuid();
            localSpokesmanListUuid = versionInfo.getSpokesmanListUuid();
        }
        Log.i(TAG, "setWakeupWordList: serverUuid=" + serverUuid + " localWakeupListUuid=" + localWakeupListUuid);
        if (serverUuid.equals(localWakeupListUuid)) {
            Log.e(TAG, "setWakeupWordList: No data change!");
            return false;
        }
        boolean result = insertWakeupWordList(wakeupWordInfos);
        Log.i(TAG, "setWakeupWordList: result=" + result);
        //该表设计spokesman_list_uuid一定会比wakeup_list_uuid先插入，且该表仅有一条数据
        if (result) {
            mSpokesmanVersionHelper.updateWakeupListUUID(localSpokesmanListUuid, serverUuid);
            printAllWakeupWordData();
        }
        return result;
    }

    /**
     * 唤醒语-插入新数据 & 恢复本地数据
     *
     * @param serverList
     * @return
     */
    private boolean insertWakeupWordList(List<WakeupWordInfo> serverList) {
        if (serverList == null || serverList.size() <= 0) {
            Log.e(TAG, "setSpokesmanList: Server list null error!");
            return false;
        }
        List<WakeupWordInfo> localList = getAllWakeupWordList();
        mWakeupWordListHelper.deleteAll();
        List<WakeupWordInfo> newList = new ArrayList<>();
        for (WakeupWordInfo serverBean : serverList) {
            serverBean.setSyncState(WakeupWordInfo.SyncState.NOT_DOWNLOAD);
            serverBean.setLocalPath("");
            for (WakeupWordInfo localBean : localList) {
                if (localBean.equals(serverBean)) {
                    serverBean.setSyncState(localBean.getSyncState());
                    serverBean.setLocalPath(localBean.getLocalPath());
                    break;
                }
            }
            newList.add(serverBean);
        }
        Log.d(TAG, "insertWakeupWordList: " + newList.toString());
        mWakeupWordListHelper.bulkInsert(newList);
        return true;
    }

    private void printAllWakeupWordData() {
        List<WakeupWordInfo> all = getAllWakeupWordList();
        if (all == null || all.size() <= 0) {
            Log.i(TAG, "printAllWakeupWordData: NULL");
        } else {
            Log.i(TAG, "printAllWakeupWordData: ---start");
            for (WakeupWordInfo bean : all) {
                Log.d(TAG, "printAllWakeupWordData: " + bean.toString());
            }
            Log.i(TAG, "printAllWakeupWordData: ---end");
        }
    }

    private void printAllSpokesmanData() {
        List<SpokesmanBean> all = mSpokesmanListHelper.getAllSpokesmanList();
        if (all == null || all.size() <= 0) {
            Log.i(TAG, "printAllSpokesmanData: NULL");
        } else {
            Log.i(TAG, "printAllSpokesmanData: ---start");
            for (SpokesmanBean bean : all) {
                Log.d(TAG, "printAllSpokesmanData: " + bean.toString());
            }
            Log.i(TAG, "printAllSpokesmanData: ---end");
        }
    }

    private String createSpokesDataUUID(String data) {
        return UUID.nameUUIDFromBytes(data.getBytes())
                .toString()
                .replace("-", "");
    }

}
