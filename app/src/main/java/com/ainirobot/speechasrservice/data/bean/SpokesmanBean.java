/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.speechasrservice.data.bean;

/**
 * 发言人信息
 */
public class SpokesmanBean {

    private String langCode;
    private String nickname;
    private String type;
    /**
     * 是否是云端配置的默认发言人
     */
    private int isDefault = DefaultState.NOT_DEFAULT;
    /**
     * 是否是本地设置的当前发言人
     */
    private int isCurrent = UseState.NOT_CURRENT;

    public static class DefaultState {
        public static final int NOT_DEFAULT = 0;
        public static final int DEFAULT = 1; //云端默认发言人
    }

    public static class UseState {
        public static final int NOT_CURRENT = 0;
        public static final int CURRENT = 1; //本地当前发言人
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public int getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(int isDefault) {
        this.isDefault = isDefault;
    }

    public boolean isDefaultSpokesman() {
        return this.isDefault == DefaultState.DEFAULT;
    }

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public int getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(int isCurrent) {
        this.isCurrent = isCurrent;
    }

    public boolean isCurrentSpokesman() {
        return this.isCurrent == UseState.CURRENT;
    }

    public boolean equals(SpokesmanBean bean) {
        return bean.getLangCode().equals(this.langCode) && bean.getType().equals(this.type);
    }

    @Override
    public String toString() {
        return "SpokesmanBean{" +
                "langCode='" + langCode + '\'' +
                ", nickname='" + nickname + '\'' +
                ", type='" + type + '\'' +
                ", isDefault=" + isDefault +
                ", isCurrent=" + isCurrent +
                '}';
    }
}
