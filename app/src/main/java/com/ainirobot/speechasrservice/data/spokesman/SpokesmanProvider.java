package com.ainirobot.speechasrservice.data.spokesman;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.ainirobot.coreservice.data.provider.BaseProvider;
import com.ainirobot.coreservice.data.provider.SQLiteTable;

import java.util.ArrayList;
import java.util.List;

/**
 * 发言人
 *
 * <p>
 * 当前发言人：{@link com.ainirobot.coreservice.client.Definition#ROBOT_SETTING_SPEAKER_ROLE}</p>
 */
public class SpokesmanProvider extends BaseProvider {
    private static final String TAG = "SpokesmanProvider";

    public static final String AUTHORITY = "com.ainirobot.agentservice.SpokesmanProvider";

    private static final List<SQLiteTable> TABLES = new ArrayList<SQLiteTable>() {{
        add(SpokesmanListHelper.TABLE);
        add(SpokesmanVersionHelper.TABLE);
        add(WakeupWordListHelper.TABLE);
    }};

    public SpokesmanProvider() {
        super();
        Log.d(TAG, "SpokesmanProvider constructor " + this);
    }

    @Override
    public List<SQLiteTable> getTables() {
        Log.d(TAG, "SpokesmanProvider getTables " + this);
        for (SQLiteTable table: TABLES) {
            Log.d(TAG, "SpokesmanProvider getTables table=" + table.getTableName() + " " +  table.getContentUri());
        }
        return TABLES;
    }

    @Override
    public SQLiteOpenHelper getDBHelper() {
        return new DBHelper(getContext());
    }

    static class DBHelper extends SQLiteOpenHelper {
        private static final String DB_NAME = "spokesman.db";
        private static final int VERSION = 2;

        private Context mContext;

        private DBHelper(Context context) {
            super(context, DB_NAME, null, VERSION);
            mContext = context;
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            Log.d(TAG, "onCreate version: " + VERSION);
            for (SQLiteTable table : TABLES) {
                table.create(db);
                table.importData(mContext, db);
            }
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            Log.d(TAG, "onUpgrade oldVersion: " + oldVersion + ", newVersion: " + newVersion);
            for (SQLiteTable table : TABLES) {
                table.delete(db);
                table.create(db);
                table.importData(mContext, db);
            }
        }

        @Override
        public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            for (SQLiteTable table : TABLES) {
                table.delete(db);
                table.create(db);
                table.importData(mContext, db);
            }
        }
    }
}
