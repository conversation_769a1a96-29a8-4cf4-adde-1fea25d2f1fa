/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.speechasrservice.data.spokesman;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.Context;
import android.database.ContentObserver;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.BaseColumns;
import android.util.Log;

import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.coreservice.data.provider.Column;
import com.ainirobot.coreservice.data.provider.SQLiteTable;
import com.ainirobot.speechasrservice.data.bean.SpokesmanVersion;

/**
 * 发言人&唤醒词，本地数据版本信息
 */
public class SpokesmanVersionHelper extends BaseDataHelper {
    private static final String TAG = "SpokesmanVersionHelper";

    private Context mContext;

    public static final SQLiteTable TABLE = new SQLiteTable(VersionField.TABLE_NAME,
            SpokesmanProvider.AUTHORITY, true) {
        @Override
        public void importData(Context context, SQLiteDatabase db) {
        }

        @Override
        public void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion) {
        }
    };

    static {
        TABLE.addColumn(VersionField.VERSION, Column.DataType.TEXT)
                .addColumn(VersionField.SPOKESMAN_LIST_UUID, Column.DataType.TEXT)
                .addColumn(VersionField.WAKEUP_LIST_UUID, Column.DataType.TEXT);
    }

    public SpokesmanVersionHelper(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    public Uri getContentUri() {
        return TABLE.getContentUri();
    }

    private ContentValues getContentValues(SpokesmanVersion info) {
        ContentValues values = new ContentValues();
        values.put(VersionField.VERSION, info.getVersion());
        values.put(VersionField.SPOKESMAN_LIST_UUID, info.getSpokesmanListUuid());
        values.put(VersionField.WAKEUP_LIST_UUID, info.getWakeupListUuid());
        return values;
    }

    public String getSpokesmanListUUID() {
        SpokesmanVersion version = getVersionInfo();
        return version != null ? version.getSpokesmanListUuid() : "";
    }

    public String getWakeupListUUID() {
        SpokesmanVersion version = getVersionInfo();
        return version != null ? version.getWakeupListUuid() : "";
    }

    @SuppressLint("Range")
    public SpokesmanVersion getVersionInfo() {
        SpokesmanVersion version = null;
        Cursor cursor = query(null, null);
        if (cursor == null) {
            return version;
        }
        while (cursor.moveToNext()) {
            version = new SpokesmanVersion();
            int spokesmanListUuidIndex = cursor.getColumnIndex(VersionField.SPOKESMAN_LIST_UUID);
            int wakeupListUuidIndex = cursor.getColumnIndex(VersionField.WAKEUP_LIST_UUID);
            int versionIndex = cursor.getColumnIndex(VersionField.VERSION);
            if (spokesmanListUuidIndex >= 0) version.setSpokesmanListUuid(cursor.getString(spokesmanListUuidIndex));
            if (wakeupListUuidIndex >= 0) version.setWakeupListUuid(cursor.getString(wakeupListUuidIndex));
            if (versionIndex >= 0) version.setVersion(cursor.getString(versionIndex));
        }
        cursor.close();
        return version;
    }

    public void updateSpokesmanListUUID(String oldUuid, String newUuid) {
        ContentValues values = new ContentValues();
        values.put(VersionField.SPOKESMAN_LIST_UUID, newUuid);
        int updateRows = update(values, VersionField.SPOKESMAN_LIST_UUID + "=?",
                new String[]{oldUuid});
        Log.i(TAG, "updateSpokesmanListUUID: updateRows=" + updateRows);
    }

    public void updateWakeupListUUID(String spokesmanListUuid, String wakeupListUuid) {
        ContentValues values = new ContentValues();
        values.put(VersionField.WAKEUP_LIST_UUID, wakeupListUuid);
        int updateRows = update(values, VersionField.SPOKESMAN_LIST_UUID + "=?",
                new String[]{spokesmanListUuid});
        Log.i(TAG, "updateWakeupListUUID: updateRows=" + updateRows);
    }

    public void insert(SpokesmanVersion info) {
        ContentValues values = getContentValues(info);
        insert(values);
    }

    public void deleteAll() {
        int deleteRows = delete(null, null);
        Log.i(TAG, "deleteAll: deleteRows=" + deleteRows);
    }

    public void registerListener(ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                getContentUri(), true, contentObserver);
    }

    public void registerListener(String key, ContentObserver contentObserver) {
        final Uri uri = Uri.withAppendedPath(getContentUri(), key);
        mContext.getContentResolver().registerContentObserver(
                uri, false, contentObserver);
    }

    public static final class VersionField implements BaseColumns {
        static final String TABLE_NAME = "spokesman_version";

        static final String VERSION = "version";
        static final String SPOKESMAN_LIST_UUID = "spokesman_list_uuid";
        static final String WAKEUP_LIST_UUID = "wakeup_list_uuid";
    }

}
