package com.ainirobot.speechasrservice.data.bean;

/**
 * 唤醒词信息
 */
public class WakeupWordInfo {

    private String langCode;
    private String type;
    private String text;
    private String serverFileName;
    private String localPath;
    private int syncState = SyncState.NOT_DOWNLOAD;

    public static class SyncState {
        public static final int NOT_DOWNLOAD = 0;
        public static final int DOWNLOADED = 1;
    }

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getServerFileName() {
        return serverFileName;
    }

    public void setServerFileName(String serverFileName) {
        this.serverFileName = serverFileName;
    }

    public String getLocalPath() {
        return localPath;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    public int getSyncState() {
        return syncState;
    }

    public void setSyncState(int syncState) {
        this.syncState = syncState;
    }

    public boolean equals(WakeupWordInfo info) {
        return info.getLangCode().equals(this.langCode) && info.getType().equals(this.type)
                && info.getText().equals(this.text)
                && info.getServerFileName().equals(this.serverFileName);
    }

    @Override
    public String toString() {
        return "WakeupInfo{" +
                "langCode='" + langCode + '\'' +
                ", type='" + type + '\'' +
                ", text='" + text + '\'' +
                ", serverFileName='" + serverFileName + '\'' +
                ", localPath='" + localPath + '\'' +
                ", syncState=" + syncState +
                '}';
    }
}
