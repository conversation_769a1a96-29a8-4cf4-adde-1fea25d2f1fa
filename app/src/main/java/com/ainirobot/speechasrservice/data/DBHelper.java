package com.ainirobot.speechasrservice.data;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.database.sqlite.SQLiteStatement;
import android.util.Log;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;

public class DBHelper extends SQLiteOpenHelper {

    private static final String TAG = "DBHelper";

    private static final int DB_VERSION = 2;

    private static final String DB_NAME = "module_db.db";

    private Context context;

    private static final String SQL_TABLE_WAKEUP_WORD = "CREATE TABLE IF NOT EXISTS "
            + ConstantDB.WordWakeUp.TABLE_NAME + "("
            + ConstantDB.WordWakeUp.FILED_ID + " integer PRIMARY KEY AUTOINCREMENT, "
            + ConstantDB.WordWakeUp.FIELD_WORD + " TEXT, "
            + ConstantDB.WordWakeUp.FIELD_SPELL + " TEXT"
            + ")";

    private static final String SQL_TABLE_WORD_MAPPING = "CREATE TABLE IF NOT EXISTS "
            + ConstantDB.WordMapping.TABLE_NAME + "("
            + ConstantDB.WordMapping.FILED_ID + " integer PRIMARY KEY AUTOINCREMENT, "
            + ConstantDB.WordMapping.FIELD_WORD + " TEXT, "
            + ConstantDB.WordMapping.FIELD_MAPPING + " TEXT"
            + ")";

    private static final String SQL_TABLE_WORD_USER = "CREATE TABLE IF NOT EXISTS "
            + ConstantDB.WordUser.TABLE_NAME + "("
            + ConstantDB.WordUser.FILED_ID + " integer PRIMARY KEY AUTOINCREMENT, "
            + ConstantDB.WordUser.FIELD_WORD_CHINESE + " TEXT, "
            + ConstantDB.WordUser.FIELD_WORD_SPELL + " TEXT, "
            + ConstantDB.WordUser.FIELD_WORD_MULTI + " TEXT"
            + ")";

    private static final String SQL_TABLE_LOCAL_MUSIC = "CREATE TABLE IF NOT EXISTS "
            + ConstantDB.LocalMusic.TABLE_NAME + "("
            + ConstantDB.LocalMusic.FILED_ID + " integer PRIMARY KEY AUTOINCREMENT, "
            + ConstantDB.LocalMusic.FIELD_SID + " TEXT, "
            + ConstantDB.LocalMusic.FIELD_SPEECH_TEXT + " TEXT, "
            + ConstantDB.LocalMusic.FIELD_FILE_PATH + " TEXT, "
            + ConstantDB.LocalMusic.FIELD_SPEAKE_ROLE + " TEXT, "
            + ConstantDB.LocalMusic.FIELD_SPEAKE_RATE + " TEXT, "
            + ConstantDB.LocalMusic.FIELD_SPEECH_VOLUME + " TEXT, "
            + ConstantDB.LocalMusic.FIELD_SPEECH_PIT + " TEXT, "
            + ConstantDB.LocalMusic.FIELD_SPEECH_SPEED + " TEXT, "
            + ConstantDB.LocalMusic.FIELD_SPEECH_LANGUAGE + " TEXT"
            + ")";


    public DBHelper(Context context) {
        super(context, DB_NAME, null, DB_VERSION);
        this.context = context;
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        Log.i(TAG, "onCreate");
        db.execSQL(SQL_TABLE_WAKEUP_WORD);
        db.execSQL(SQL_TABLE_WORD_MAPPING);
        db.execSQL(SQL_TABLE_WORD_USER);
        db.execSQL(SQL_TABLE_LOCAL_MUSIC);

        readWakeupCodeFromAsset(db);
        readWordMappingFromAsset(db);
    }


    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.i(TAG, "Upgrading database from version " + oldVersion
                + " to " + newVersion + ".");
        resetLocalMusicTable(db);
    }

    @Override
    public void onDowngrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.i(TAG, "Downgrading database from version " + oldVersion
                + " to " + newVersion + ".");
        resetLocalMusicTable(db);
    }

    private void resetLocalMusicTable(SQLiteDatabase db) {
        delete(db, ConstantDB.LocalMusic.TABLE_NAME);
        db.execSQL(SQL_TABLE_LOCAL_MUSIC);
    }

    private void readWakeupCodeFromAsset(SQLiteDatabase db) {
        Log.d(TAG, "readWakeupCodeFromAsset");
        ArrayList<String> wakeUpWordStringList;
        try {
            wakeUpWordStringList = readDataFromAsset("chinese_spell_chart");
        } catch (IOException e) {
            e.printStackTrace();
            Log.d(TAG, "readWakeupCodeFromAssetFail:" + e.getMessage());
            return;
        }

        String sql = "insert into " + ConstantDB.WordWakeUp.TABLE_NAME + "("
                + ConstantDB.WordWakeUp.FIELD_WORD + ","
                + ConstantDB.WordWakeUp.FIELD_SPELL + ")" + " values( ?, ?)";

        db.beginTransaction();
        try {
            for (String wakeUpWord : wakeUpWordStringList) {
                SQLiteStatement sqLiteStatement = db.compileStatement(sql);
                sqLiteStatement.bindString(1, String.valueOf(wakeUpWord.charAt(0)));
                sqLiteStatement.bindString(2, wakeUpWord.substring(1, wakeUpWord.length()).trim());
                sqLiteStatement.executeInsert();
            }
            db.setTransactionSuccessful();
        } finally {
            db.endTransaction();
        }
    }

    private void readWordMappingFromAsset(SQLiteDatabase db) {
        Log.d(TAG, "readWordMappingFromAsset");
        ArrayList<String> wakeUpWordStringList;
        try {
            wakeUpWordStringList = readDataFromAsset("spell_word_mapping");
        } catch (IOException e) {
            e.printStackTrace();
            Log.d(TAG, "readWordMappingFromAssetFail:" + e.getMessage());
            return;
        }

        String sql = "insert into " + ConstantDB.WordMapping.TABLE_NAME + "("
                + ConstantDB.WordMapping.FIELD_WORD + ","
                + ConstantDB.WordMapping.FIELD_MAPPING + ")" + " values( ?, ?)";

        db.beginTransaction();
        try {
            for (String wakeUpWord : wakeUpWordStringList) {
                String[] split = wakeUpWord.split(" ");
                if (split.length >= 2) {
                    SQLiteStatement sqLiteStatement = db.compileStatement(sql);
                    sqLiteStatement.bindString(1, split[0]);
                    sqLiteStatement.bindString(2, split[1].replace(":", ""));
                    sqLiteStatement.executeInsert();
                }
            }
            db.setTransactionSuccessful();
        } catch (Exception e) {
            Log.d(TAG, "insertToDbOfWordMappingTableFail:" + e.getMessage());
        } finally {
            db.endTransaction();
        }
    }

    private ArrayList<String> readDataFromAsset(String assetPath) throws IOException {
        InputStream inputStream = context.getAssets().open(assetPath);
        InputStreamReader isr = new InputStreamReader(inputStream, "UTF-8");
        BufferedReader br = new BufferedReader(isr);

        String line = "";
        ArrayList<String> wakeUpWordStringList = new ArrayList<>();
        while ((line = br.readLine()) != null) {
            wakeUpWordStringList.add(line);
        }
        br.close();
        isr.close();
        inputStream.close();

        return wakeUpWordStringList;
    }

    private void delete(SQLiteDatabase db, String tableName) {
        db.execSQL("DROP TABLE IF EXISTS " + tableName);
    }

}
