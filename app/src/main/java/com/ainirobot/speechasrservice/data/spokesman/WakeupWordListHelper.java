/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.speechasrservice.data.spokesman;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.Context;
import android.database.ContentObserver;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.BaseColumns;
import android.util.Log;

import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.coreservice.data.provider.Column.DataType;
import com.ainirobot.coreservice.data.provider.SQLiteTable;
import com.ainirobot.speechasrservice.data.bean.WakeupWordInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 唤醒词信息
 * <p>
 * 数据更新依赖语音服务器返回，不需要本地数据初始化
 */
public class WakeupWordListHelper extends BaseDataHelper {
    private static final String TAG = "WakeupWordListHelper";

    private Context mContext;

    public static final SQLiteTable TABLE = new SQLiteTable(WakeupWordField.TABLE_NAME,
            SpokesmanProvider.AUTHORITY, true) {
        @Override
        public void importData(Context context, SQLiteDatabase db) {
        }

        @Override
        public void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion) {
        }
    };

    static {
        TABLE.addColumn(WakeupWordField.LANG_CODE, DataType.TEXT)
                .addColumn(WakeupWordField.TYPE, DataType.TEXT)
                .addColumn(WakeupWordField.TEXT, DataType.TEXT)
                .addColumn(WakeupWordField.SERVER_FILE_NAME, DataType.TEXT)
                .addColumn(WakeupWordField.LOCAL_PATH, DataType.TEXT)
                .addColumn(WakeupWordField.SYNC_STATE, DataType.INTEGER);
    }

    public WakeupWordListHelper(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    public Uri getContentUri() {
        return TABLE.getContentUri();
    }

    private ContentValues getContentValues(WakeupWordInfo info) {
        ContentValues values = new ContentValues();
        values.put(WakeupWordField.LANG_CODE, info.getLangCode());
        values.put(WakeupWordField.TYPE, info.getType());
        values.put(WakeupWordField.TEXT, info.getText());
        values.put(WakeupWordField.SERVER_FILE_NAME, info.getServerFileName());
        values.put(WakeupWordField.LOCAL_PATH, info.getLocalPath());
        values.put(WakeupWordField.SYNC_STATE, info.getSyncState());
        return values;
    }

    public void bulkInsert(List<WakeupWordInfo> infos) {
        ArrayList<ContentValues> contentValues = new ArrayList<>();
        for (WakeupWordInfo data : infos) {
            ContentValues values = getContentValues(data);
            contentValues.add(values);
        }

        ContentValues[] valueArray = new ContentValues[contentValues.size()];
        int insertRows = bulkInsert(contentValues.toArray(valueArray));
        Log.i(TAG, "bulkInsert: insertRows=" + insertRows);
    }

    public void insert(WakeupWordInfo info) {
        ContentValues values = getContentValues(info);
        insert(values);
    }

    public void deleteAll() {
        int deleteRows = delete(null, null);
        Log.i(TAG, "deleteAll: deleteRows=" + deleteRows);
    }

    @SuppressLint("Range")
    public List<WakeupWordInfo> getAllWakeupWordList() {
        List<WakeupWordInfo> list = new ArrayList<>();
        Cursor cursor = query(null, null, null, null);
        if (cursor == null) {
            return list;
        }
        while (cursor.moveToNext()) {
            WakeupWordInfo info = new WakeupWordInfo();
            int langCodeIndex = cursor.getColumnIndex(WakeupWordField.LANG_CODE);
            int typeIndex = cursor.getColumnIndex(WakeupWordField.TYPE);
            int textIndex = cursor.getColumnIndex(WakeupWordField.TEXT);
            int serverFileNameIndex = cursor.getColumnIndex(WakeupWordField.SERVER_FILE_NAME);
            int localPathIndex = cursor.getColumnIndex(WakeupWordField.LOCAL_PATH);
            int syncStateIndex = cursor.getColumnIndex(WakeupWordField.SYNC_STATE);
            if (langCodeIndex >= 0) info.setLangCode(cursor.getString(langCodeIndex));
            if (typeIndex >= 0) info.setType(cursor.getString(typeIndex));
            if (textIndex >= 0) info.setText(cursor.getString(textIndex));
            if (serverFileNameIndex >= 0) info.setServerFileName(cursor.getString(serverFileNameIndex));
            if (localPathIndex >= 0) info.setLocalPath(cursor.getString(localPathIndex));
            if (syncStateIndex >= 0) info.setSyncState(cursor.getInt(syncStateIndex));
            list.add(info);
        }
        cursor.close();
        Log.i(TAG, "getAllWakeupWordList: list=" + list.toString());
        return list;
    }

    @SuppressLint("Range")
    public List<WakeupWordInfo> getUnDownloadWakeupWordList() {
        List<WakeupWordInfo> list = new ArrayList<>();

        Cursor cursor = query(WakeupWordField.SYNC_STATE + "=?",
                new String[]{String.valueOf(WakeupWordInfo.SyncState.NOT_DOWNLOAD)});

        if (cursor == null) {
            return list;
        }
        while (cursor.moveToNext()) {
            WakeupWordInfo info = new WakeupWordInfo();
            int langCodeIndex = cursor.getColumnIndex(WakeupWordField.LANG_CODE);
            int typeIndex = cursor.getColumnIndex(WakeupWordField.TYPE);
            int textIndex = cursor.getColumnIndex(WakeupWordField.TEXT);
            int serverFileNameIndex = cursor.getColumnIndex(WakeupWordField.SERVER_FILE_NAME);
            int localPathIndex = cursor.getColumnIndex(WakeupWordField.LOCAL_PATH);
            int syncStateIndex = cursor.getColumnIndex(WakeupWordField.SYNC_STATE);
            if (langCodeIndex >= 0) info.setLangCode(cursor.getString(langCodeIndex));
            if (typeIndex >= 0) info.setType(cursor.getString(typeIndex));
            if (textIndex >= 0) info.setText(cursor.getString(textIndex));
            if (serverFileNameIndex >= 0) info.setServerFileName(cursor.getString(serverFileNameIndex));
            if (localPathIndex >= 0) info.setLocalPath(cursor.getString(localPathIndex));
            if (syncStateIndex >= 0) info.setSyncState(cursor.getInt(syncStateIndex));
            list.add(info);
        }
        cursor.close();
        Log.i(TAG, "getAllWakeupWordList: list=" + list.toString());
        return list;
    }

    @SuppressLint("Range")
    public List<String> getWakeupAudioByCurrentType(String langCode, String type) {
        List<String> list = new ArrayList<>();

        Cursor cursor = query(WakeupWordField.SYNC_STATE + "=? and "
                        + WakeupWordField.LANG_CODE + "=? and " + WakeupWordField.TYPE + "=?",
                new String[]{String.valueOf(WakeupWordInfo.SyncState.DOWNLOADED), langCode, type});

        if (cursor == null) {
            return list;
        }
        while (cursor.moveToNext()) {
            int localPathIndex = cursor.getColumnIndex(WakeupWordField.LOCAL_PATH);
            if (localPathIndex >= 0) list.add(cursor.getString(localPathIndex));
        }
        cursor.close();
        Log.i(TAG, "getAllWakeupWordList: list=" + list.toString());
        return list;
    }

    public void registerListener(ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                getContentUri(), true, contentObserver);
    }

    public int updateVoiceLocalPathAndState(String langCode, String type, String fileName, String path, int state) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(WakeupWordField.LOCAL_PATH, path);
        contentValues.put(WakeupWordField.SYNC_STATE, state);
        return update(contentValues,
                WakeupWordField.LANG_CODE + "=? and " + WakeupWordField.TYPE + "=? and " + WakeupWordField.SERVER_FILE_NAME + "=?",
                new String[]{langCode, type, fileName});
    }

    public static final class WakeupWordField implements BaseColumns {
        static final String TABLE_NAME = "wakeup_word_info";

        static final String LANG_CODE = "langCode";
        static final String TYPE = "type";
        static final String TEXT = "text";
        static final String SERVER_FILE_NAME = "server_file_name";
        static final String LOCAL_PATH = "local_path";
        static final String SYNC_STATE = "sync_state";
    }

}
