package com.ainirobot.speechasrservice.data;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.util.Log;

import com.ainirobot.agent.R;
import com.ainirobot.coreservice.client.speech.entity.TTSEntity;
import com.ainirobot.coreservice.client.speech.entity.TTSParams;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

public class DBOperater {

    private static final String TAG = "DBOperater";

    private static final String URI_WAKEUP_WORD = "content://" + DBProvider.authority + "/" + ConstantDB.WordWakeUp.TABLE_NAME;
    private static final String URI_WORD_MAPPING = "content://" + DBProvider.authority + "/" + ConstantDB.WordMapping.TABLE_NAME;
    private static final String URI_WORD_USER = "content://" + DBProvider.authority + "/" + ConstantDB.WordUser.TABLE_NAME;
    private static final String URI_LOCAL_MUSIC = "content://" + DBProvider.authority + "/" + ConstantDB.LocalMusic.TABLE_NAME;


    public static String queryWakeupWordSpellFormDb(Context context, String word) {
        Cursor cursor = context.getContentResolver().query(Uri.parse(URI_WAKEUP_WORD), null,
                ConstantDB.WordWakeUp.FIELD_WORD + "=?", new String[]{word}, null);

        String spells = null;
        if (cursor != null) {
            while (cursor.moveToNext()) {
                spells = getStringSafely(cursor, ConstantDB.WordWakeUp.FIELD_SPELL);
            }
            cursor.close();
        }
        return spells;
    }

    public static String queryWordMappingFormDb(Context context, String spell) {
        Cursor cursor = context.getContentResolver().query(Uri.parse(URI_WORD_MAPPING), null,
                ConstantDB.WordMapping.FIELD_WORD + "=?", new String[]{spell}, null);

        String mappingSpell = null;
        if (cursor != null) {
            while (cursor.moveToNext()) {
                mappingSpell = getStringSafely(cursor, ConstantDB.WordMapping.FIELD_MAPPING);
            }
            cursor.close();
        }
        return mappingSpell;
    }


    public static void saveUserSetWord(Context context, String chinese, String spell) {
        ContentResolver contentResolver = context.getContentResolver();
        context.getContentResolver().delete(Uri.parse(URI_WORD_USER), null, null);

        StringBuilder sbWordMulti = new StringBuilder();
        String[] split = spell.split(" ");

        if (chinese.length() != split.length) {
            throw new IllegalArgumentException(context.getString(R.string.tip_wakeup_word_pinyin_unmatch));
        }

        for (int i = 0; i < chinese.length(); i++) {
            sbWordMulti.append(chinese.charAt(i));
            String mappingSpell = queryWordMappingFormDb(context, split[i]);
            Log.d(TAG, "mappingSpell: " + mappingSpell + ", split[i]:" + split[i]);
            String showSpell = mappingSpell != null ? mappingSpell : (split[i] != null ? split[i].replace(":", "") : split[i]);
            sbWordMulti.append("(" + showSpell + ")");
        }
        Log.d(TAG, "sbWordMulti: " + sbWordMulti.toString());

        ContentValues contentValues = new ContentValues();
        contentValues.put(ConstantDB.WordUser.FIELD_WORD_CHINESE, chinese);
        contentValues.put(ConstantDB.WordUser.FIELD_WORD_SPELL, spell);
        contentValues.put(ConstantDB.WordUser.FIELD_WORD_MULTI, sbWordMulti.toString());

        contentResolver.insert(Uri.parse(URI_WORD_USER), contentValues);
    }

    public static boolean deleteUserSetWord(Context context) {
        int delete = context.getContentResolver().delete(Uri.parse(URI_WORD_USER), null, null);
        return delete != 0;
    }


    public static String queryUserSetWord(Context context, String field) {
        Cursor cursor = context.getContentResolver().query(Uri.parse(URI_WORD_USER), null, null, null, null);

        String wordUserSet = null;
        if (cursor != null) {
            while (cursor.moveToNext()) {
                wordUserSet = getStringSafely(cursor, field);
            }
            cursor.close();
        }
        return wordUserSet;
    }

    public static void saveLocalMusic(Context context, String sid, String speech_text,
                                      String file_path, String speaker_role, String speaker_rate,
                                      String speech_volume, String speech_pit, String speech_speed,
                                      String speech_language) {
        ContentResolver contentResolver = context.getContentResolver();

        ContentValues contentValues = new ContentValues();
        contentValues.put(ConstantDB.LocalMusic.FIELD_SID, sid);
        contentValues.put(ConstantDB.LocalMusic.FIELD_SPEECH_TEXT, speech_text);
        contentValues.put(ConstantDB.LocalMusic.FIELD_FILE_PATH, file_path);
        contentValues.put(ConstantDB.LocalMusic.FIELD_SPEAKE_ROLE, speaker_role);
        contentValues.put(ConstantDB.LocalMusic.FIELD_SPEAKE_RATE, speaker_rate);
        contentValues.put(ConstantDB.LocalMusic.FIELD_SPEECH_VOLUME, speech_volume);
        contentValues.put(ConstantDB.LocalMusic.FIELD_SPEECH_PIT, speech_pit);
        contentValues.put(ConstantDB.LocalMusic.FIELD_SPEECH_SPEED, speech_speed);
        contentValues.put(ConstantDB.LocalMusic.FIELD_SPEECH_LANGUAGE, speech_language);

        contentResolver.insert(Uri.parse(URI_LOCAL_MUSIC), contentValues);
    }

    public static void updateLocalMusic(Context context, String sid, String file_path) {
        ContentResolver contentResolver = context.getContentResolver();

        ContentValues contentValues = new ContentValues();
        contentValues.put(ConstantDB.LocalMusic.FIELD_FILE_PATH, file_path);
        contentResolver.update(Uri.parse(URI_LOCAL_MUSIC), contentValues,
                ConstantDB.LocalMusic.FIELD_SID + "=?", new String[]{sid});
    }

    public static void deleteLocalMusic(Context context, String sid) {
        ContentResolver contentResolver = context.getContentResolver();
        String selection = ConstantDB.LocalMusic.FIELD_SID + "=?";
        String[] selectionArgs = { sid };
        int delete = contentResolver.delete(Uri.parse(URI_LOCAL_MUSIC), selection, selectionArgs);
        Log.d(TAG, "deleteLocalMusic: sid = " + sid + " , rows deleted = " + delete);
    }

    public static String queryLocalMusicFilePathFormDb(Context context, String speech_text,
                                                       String speaker, String rate, String volume,
                                                       String pit, String speed, String language) {
        Cursor cursor = context.getContentResolver().query(Uri.parse(URI_LOCAL_MUSIC), null,
                ConstantDB.LocalMusic.FIELD_SPEECH_TEXT + "=? and "
                        + ConstantDB.LocalMusic.FIELD_SPEAKE_ROLE + "=? and "
                        + ConstantDB.LocalMusic.FIELD_SPEAKE_RATE + "=? and "
                        + ConstantDB.LocalMusic.FIELD_SPEECH_VOLUME + "=? and "
                        + ConstantDB.LocalMusic.FIELD_SPEECH_PIT + "=? and "
                        + ConstantDB.LocalMusic.FIELD_SPEECH_SPEED + "=? and "
                        + ConstantDB.LocalMusic.FIELD_SPEECH_LANGUAGE + "=?",
                new String[]{speech_text, speaker, rate, volume, pit, speed, language}, null);
        Log.d(TAG, "queryLocalMusicFilePathFormDb:speech_text: " + speech_text + ", speaker: " + speaker
                + ", rate: " + rate + ", volume: " + volume + ", pit: " + pit + ", speed: " + speed
                + ", language: " + language);
        String filePath = null;
        if (cursor != null) {
            while (cursor.moveToNext()) {
                filePath = getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_FILE_PATH);
                Log.d(TAG, "queryLocalMusicFilePathFormDb:filePath: " + filePath);
            }
            cursor.close();
        }
        return filePath;
    }

    public static List<TTSEntity> queryNotCurrentRoleFormDb(Context context, String lastSpeaker,
                                                            String descSpeaker) {
        String sql = "select * from " + ConstantDB.LocalMusic.TABLE_NAME + " where "
                + ConstantDB.LocalMusic.FIELD_SPEAKE_ROLE + " =? and " + ConstantDB.LocalMusic.FIELD_SPEECH_TEXT
                + " not in (select " + ConstantDB.LocalMusic.FIELD_SPEECH_TEXT
                + " from " + ConstantDB.LocalMusic.TABLE_NAME + " where "
                + ConstantDB.LocalMusic.FIELD_SPEAKE_ROLE + " =?)";
        Cursor cursor = context.getContentResolver().query(Uri.parse(URI_LOCAL_MUSIC), null,
                sql, new String[]{lastSpeaker, descSpeaker}, DBProvider.RAWQUERY);

        List<TTSEntity> ttsEntities = null;
        if (cursor != null) {
            ttsEntities = new ArrayList<>();
            while (cursor.moveToNext()) {
                HashMap<String, String> ttsEntityMap = new HashMap<>();
                String speech_text = getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEECH_TEXT);
                ttsEntityMap.put(TTSParams.SPEAKERROLE, descSpeaker);

                String rate = getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEAKE_RATE);
                ttsEntityMap.put(TTSParams.SPEECHRATE, rate);

                String volume = getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEECH_VOLUME);
                ttsEntityMap.put(TTSParams.SPEECHVOLUME, volume);

                String pit = getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEECH_PIT);
                ttsEntityMap.put(TTSParams.SPEECHPIT, pit);

                String speed = getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEECH_SPEED);
                ttsEntityMap.put(TTSParams.SPEECHSPEED, speed);

                String language = getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEECH_LANGUAGE);
                ttsEntityMap.put(TTSParams.SPEECHLANGUAGE, language);

                String sid = UUID.randomUUID().toString();
                TTSEntity ttsEntity = new TTSEntity(sid, speech_text,
                        ttsEntityMap);
                saveLocalMusic(context, sid, speech_text, null, descSpeaker,
                        rate, volume, pit, speed, language);
                ttsEntities.add(ttsEntity);
            }
            cursor.close();
        }
        return ttsEntities;
    }

    public static List<TTSEntity> queryLocalMusicByPathFormDb(Context context) {
        Cursor cursor = context.getContentResolver().query(Uri.parse(URI_LOCAL_MUSIC), null,
                ConstantDB.LocalMusic.FIELD_FILE_PATH + " is null",
                null, null);

        List<TTSEntity> ttsEntities = null;
        if (cursor != null) {
            ttsEntities = new ArrayList<>();
            while (cursor.moveToNext()) {
                HashMap<String, String> ttsEntityMap = new HashMap<>();
                String sid = getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SID);
                String speech_text = getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEECH_TEXT);
                ttsEntityMap.put(TTSParams.SPEAKERROLE, getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEAKE_ROLE));
                ttsEntityMap.put(TTSParams.SPEECHRATE, getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEAKE_RATE));
                ttsEntityMap.put(TTSParams.SPEECHVOLUME, getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEECH_VOLUME));
                ttsEntityMap.put(TTSParams.SPEECHPIT, getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEECH_PIT));
                ttsEntityMap.put(TTSParams.SPEECHSPEED, getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEECH_SPEED));
                ttsEntityMap.put(TTSParams.SPEECHLANGUAGE, getStringSafely(cursor, ConstantDB.LocalMusic.FIELD_SPEECH_LANGUAGE));
                TTSEntity ttsEntity = new TTSEntity(sid, speech_text, ttsEntityMap);
                ttsEntities.add(ttsEntity);
            }
            cursor.close();
        }
        return ttsEntities;
    }

    public static boolean deleteLocalMusic(Context context) {
        int delete = context.getContentResolver().delete(Uri.parse(URI_LOCAL_MUSIC), null,
                null);
        return delete != 0;
    }

    /**
     * 安全获取String类型字段
     */
    private static String getStringSafely(Cursor cursor, String columnName) {
        int index = cursor.getColumnIndex(columnName);
        return index >= 0 ? cursor.getString(index) : null;
    }

    /**
     * 安全获取int类型字段
     */
    private static Integer getIntSafely(Cursor cursor, String columnName) {
        int index = cursor.getColumnIndex(columnName);
        return index >= 0 ? cursor.getInt(index) : null;
    }
}
