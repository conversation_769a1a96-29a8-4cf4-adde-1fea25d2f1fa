package com.ainirobot.speechasrservice.data;


public interface ConstantDB {

    interface WordWakeUp {
        String TABLE_NAME = "wakeup_word_table";

        String FILED_ID = "_id";
        String FIELD_WORD = "word";
        String FIELD_SPELL = "spell";
    }

    interface WordMapping {
        String TABLE_NAME = "mapping_table";

        String FILED_ID = "_id";
        String FIELD_WORD = "word";
        String FIELD_MAPPING = "mapping";
    }

    interface WordUser {
        String TABLE_NAME = "user_setting_table";

        String FILED_ID = "_id";
        String FIELD_WORD_CHINESE = "word";
        String FIELD_WORD_SPELL = "spell";
        String FIELD_WORD_MULTI = "multi";
    }

    interface LocalMusic {
        String TABLE_NAME = "local_music_table";

        String FILED_ID = "_id";
        String FIELD_SID = "sid";
        String FIELD_SPEECH_TEXT = "speech_text";
        String FIELD_FILE_PATH = "file_path";
        String FIELD_SPEAKE_ROLE = "speaker_role";
        String FIELD_SPEAKE_RATE = "speaker_rate";
        String FIELD_SPEECH_VOLUME = "speech_volume";
        String FIELD_SPEECH_PIT = "speech_pit";
        String FIELD_SPEECH_SPEED = "speech_speed";
        String FIELD_SPEECH_LANGUAGE = "speech_language";

    }

}
