package com.ainirobot.speechasrservice.data.spokesman;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ainirobot.agent.BuildConfig;
import com.ainirobot.common.DataStore;
import com.ainirobot.common.utils.KLog;
import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.ainirobot.speechasrservice.data.bean.SpokesmanBean;
import com.ainirobot.speechasrservice.data.bean.WakeupWordInfo;
import com.ainirobot.speechasrservice.kratos.auth.net.ORRetrofitAdapter;
import com.ainirobot.speechasrservice.kratos.cofig.ClientConfig;
import com.ainirobot.speechasrservice.kratos.cofig.EnvironmentConfig;
import com.ainirobot.speechasrservice.kratos.entity.SpokermanResult;
import com.ainirobot.speechasrservice.utils.Singleton;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SpokermanServer {

    private static final String TAG = SpokermanServer.class.getSimpleName();
    private SpokesmanManager mSpokesmanManager;
    private static final int DEFAULT_MAX_PERMIT = 10;
    private ExecutorService service = Executors.newCachedThreadPool();
    private Semaphore mSemaphore = new Semaphore(DEFAULT_MAX_PERMIT);
    private ConcurrentLinkedQueue<WakeupWordInfo> mWakeupWordInfos = new ConcurrentLinkedQueue<>();
    private static final int RESOURCE_SUCCESS = 200;
    private String wakeupAudioFilePath = ClientConfig.BASE_LOCAL_PATH + "/WakeupAudio/";

    private Context mContext;
    private static Singleton<SpokermanServer> sSingleton = new Singleton<SpokermanServer>() {
        @Override
        protected SpokermanServer create() {
            return new SpokermanServer();
        }
    };

    private SpokermanServer() {
    }

    public static SpokermanServer getInstance() {
        return sSingleton.get();
    }

    public void init(Context context) {
        this.mContext = context;
        if (null == mSpokesmanManager) {
            mSpokesmanManager = new SpokesmanManager(context);
        }
        getSpokesmenList();
    }

    private void getSpokesmenInternal(String pid) {
        if (TextUtils.isEmpty(pid)) {
            return;
        }
        ORRetrofitAdapter.getSpokermanList(pid).enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                if (response == null) {
                    Log.e(TAG, "getSpokermanList error");
                } else {
                    int code = response.code();
                    if (code == RESOURCE_SUCCESS) {
                        ResponseBody body = response.body();
                        if (null == body) {
                            Log.e(TAG, "getSpokermanList fatal error");
                        } else {
                            String string = null;
                            try {
                                string = body.string();
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                            Log.d(TAG, "response: " + string);
                            insertSpokermanData(string);
                            downloadWakeupAudio();
                        }
                    } else {
                        Log.e(TAG, "getSpokermanList error code = " + code + " , message = "
                                + response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                Log.e(TAG, "get response onFailure " + t.getMessage());
            }
        });
    }

    private void getSpokermenListByLanguage(String pid) {
        ORRetrofitAdapter.getSpokermenListByLanguage(pid).enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                if (response == null) {
                    Log.e(TAG, "getSpokermanList error");
                } else {
                    int code = response.code();
                    if (code == RESOURCE_SUCCESS) {
                        ResponseBody body = response.body();
                        if (null == body) {
                            Log.e(TAG, "getSpokermanList fatal error");
                        } else {
                            String string = null;
                            try {
                                string = body.string();
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                            Log.d(TAG, "response: " + string);
                            insertSpokermanData(string);
                            downloadWakeupAudio();
                        }
                    } else {
                        Log.e(TAG, "getSpokermanList error code = " + code + " , message = "
                                + response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                Log.e(TAG, "get response onFailure " + t.getMessage());
            }
        });
    }

    private void insertSpokermanData(String result) {
        KLog.INSTANCE.d("insertSpokermanData: " + result, TAG);
        //TODO 插入数据库涉及到排序问题，此处反序列化不能更改
        try {
            SpokermanResult spokermanResult = JSONObject.parseObject(result,
                    SpokermanResult.class);
//        Log.d(TAG, "onResponse: " + spokermanResult);
            if (spokermanResult == null) {
                Log.d(TAG, "spokermanResult is null");
                return;
            }
            List<SpokesmanBean> spokesmanBeans = new ArrayList<>();
            List<WakeupWordInfo> wakeupWordInfos = new ArrayList<>();
            ArrayList<SpokermanResult.Spokerman> voices = spokermanResult
                    .getVoices();
            for (SpokermanResult.Spokerman spokerman : voices) {
                SpokesmanBean spokesmanBean = new SpokesmanBean();
                spokesmanBean.setLangCode(spokerman.getLan());
                spokesmanBean.setType(spokerman.getType());
                spokesmanBean.setNickname(spokerman.getDesc());
                spokesmanBean.setIsDefault(spokerman.getIs_default());
                spokesmanBeans.add(spokesmanBean);
                Log.d(TAG, "voices:" + spokerman);
                ArrayList<SpokermanResult.Spokerman.Audio> audios = spokerman
                        .getAudio();
                if (null != audios) {
                    for (SpokermanResult.Spokerman.Audio audio : audios) {
                        WakeupWordInfo wakeupWordInfo = new WakeupWordInfo();
                        wakeupWordInfo.setLangCode(spokerman.getLan());
                        wakeupWordInfo.setType(spokerman.getType());
                        wakeupWordInfo.setServerFileName(audio.getFilename());
                        wakeupWordInfo.setText(audio.getText());
                        wakeupWordInfos.add(wakeupWordInfo);
                        Log.d(TAG, "audio:" + wakeupWordInfo);
                    }
                }
            }
            boolean insertSuccess = mSpokesmanManager.setSpokesmanList(spokesmanBeans);
            if (insertSuccess) {
                //数据同步成功后，更新当前发音人
                setCurrentRole(EnvironmentConfig.TtsConfig.getTtsLanguage());
            }
            mSpokesmanManager.setWakeupWordList(wakeupWordInfos);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void networkConnected() {
        KLog.INSTANCE.d("networkConnected getSpokesmenInternal", TAG);
        getSpokesmenInternal(EnvironmentConfig.OtherEnvironment.getPid());
    }

    public void getSpokesmenList() {
        KLog.INSTANCE.d("getSpokesmenList getSpokesmenInternal", TAG);
        getSpokesmenInternal(EnvironmentConfig.OtherEnvironment.getPid());
    }

    private void downloadWakeupAudio() {
        List<WakeupWordInfo> unDownloadWakeupWordList = mSpokesmanManager.getUnDownloadWakeupWordList();
        Log.d(TAG, "downloadWakeupAudio: " + unDownloadWakeupWordList.toString());
        //TODO:sd卡下的历史文件没有删除
        for (WakeupWordInfo wakeupWordInfo : unDownloadWakeupWordList) {
            mWakeupWordInfos.offer(wakeupWordInfo);
            service.execute(runnable);
        }
    }

    public List<String> getWakeupAudioByCurrentType(String langCode, String type) {
        if (mSpokesmanManager == null) {
            Log.w(TAG, "getWakeupAudioByCurrentType: mSpokesmanManager is null, returning empty list");
            return new ArrayList<>();
        }
        return mSpokesmanManager.getWakeupAudioByCurrentType(langCode, type);
    }

    public void notifyTtsLanguageChanged(String language) {
//        SettingsUtil.putString(mContext, Definition.ROBOT_SETTING_SPEAKER_ROLE,
//                mSpokesmanManager.getDefaultSpokesman(language));
        if (mSpokesmanManager != null) {
            setCurrentRole(language);
        } else {
            Log.w(TAG, "notifyTtsLanguageChanged: mSpokesmanManager is null, skipping setCurrentRole");
        }
    }

    public void notifyMultiLingualClosed(String language) {
        if (mSpokesmanManager == null) {
            Log.w(TAG, "notifyMultiLingualClosed: mSpokesmanManager is null, cannot process");
            return;
        }

        String defaultSpokesman;
        if ("zh_CN".equals(language) && mSpokesmanManager.getSpokesman(language, ZH_CN_DEFAULT_SPOKESMAN) != null) {
            defaultSpokesman = ZH_CN_DEFAULT_SPOKESMAN;
        } else {
            defaultSpokesman = mSpokesmanManager.getDefaultSpokesman(language);
        }
        if (!TextUtils.isEmpty(defaultSpokesman)) {
        RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_SETTING_SPEAKER_ROLE,
                defaultSpokesman);
        }
    }

    private static final String ZH_CN_DEFAULT_SPOKESMAN = "aliyun-loongstella";
    /**
     * 切换语言，切换语言后，需要重新获设置默认发言人
     * @param language 语言
     */
    private void setCurrentRole(String language) {
        if (mSpokesmanManager == null) {
            Log.w(TAG, "setCurrentRole: mSpokesmanManager is null, cannot set current role");
            return;
        }

        String defaultSpokesman;
        // 国内版本简体中文默认发言人强制设置为阿里云小拉
        if (BuildConfig.REGION_DOMESTIC &&  "zh_CN".equals(language)
                && !DataStore.INSTANCE.isSpokesmanSet()
                && mSpokesmanManager.getSpokesman(language, ZH_CN_DEFAULT_SPOKESMAN) != null) {
            defaultSpokesman = ZH_CN_DEFAULT_SPOKESMAN;
        } else {
            defaultSpokesman = mSpokesmanManager.getDefaultSpokesman(language);
        }
        if (!TextUtils.isEmpty(defaultSpokesman)) {
            RobotSettingApi.getInstance().setRobotString(Definition.ROBOT_SETTING_SPEAKER_ROLE,
                    defaultSpokesman);
        }
    }

    public void notifySpokesmanChanged(String role) {
        if (mSpokesmanManager == null) {
            Log.w(TAG, "notifySpokesmanChanged: mSpokesmanManager is null, cannot set spokesman");
            return;
        }

        if (!DataStore.INSTANCE.isSpokesmanSet()) {
            DataStore.INSTANCE.setSpokesmanSet(true);
        }
        mSpokesmanManager.setCurrentSpokesman(EnvironmentConfig.TtsConfig.getTtsLanguage(), role);
    }

    Runnable runnable = () -> {
        try {
            mSemaphore.acquire();
        } catch (InterruptedException e1) {
            e1.printStackTrace();
        }
        WakeupWordInfo wakeupWordInfo = getWakeupWordInfo();
        if (null != wakeupWordInfo) {
            Log.d(TAG, "run: begin getWakeupAudio " + Thread.currentThread().getName());
            try {
                ORRetrofitAdapter.getWakeupAudio(wakeupWordInfo.getLangCode(), wakeupWordInfo.getType(),
                        wakeupWordInfo.getText())
                        .enqueue(new Callback<ResponseBody>() {
                            @Override
                            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                                if (response == null) {
                                    Log.e(TAG, "getWakeupAudio error");
                                } else {
                                    int code = response.code();
                                    if (code == RESOURCE_SUCCESS) {
                                        ResponseBody body = response.body();
                                        if (null == body) {
                                            Log.e(TAG, "getWakeupAudio fatal error");
                                        } else {
                                            insertWakeupInfoData(wakeupWordInfo, body.byteStream());
                                        }
                                    } else {
                                        Log.e(TAG, "getWakeupAudio error code = " + code + " , message = "
                                                + response.message());
                                    }
                                }
                                mSemaphore.release();
                            }

                            @Override
                            public void onFailure(Call<ResponseBody> call, Throwable t) {
                                Log.e(TAG, "getWakeupAudio onFailure " + t.getMessage());
                                mSemaphore.release();
                            }
                        });
            } catch (IllegalArgumentException e) {
                mSemaphore.release();
                e.printStackTrace();
            }
        } else {
            Log.d(TAG, "getWakeupAudio is canceled");
        }

    };

    private void insertWakeupInfoData(WakeupWordInfo wakeupWordInfo, InputStream inputStream) {
        service.execute(() -> {
            if (saveWakeupAudio(wakeupWordInfo.getServerFileName(), inputStream)) {
                mSpokesmanManager.updateVoiceLocalPathAndState(wakeupWordInfo.getLangCode(),
                        wakeupWordInfo.getType(), wakeupWordInfo.getServerFileName(),
                        wakeupAudioFilePath + wakeupWordInfo.getServerFileName(),
                        WakeupWordInfo.SyncState.DOWNLOADED);
            } else {
                Log.e(TAG, "saveWakeupAudio error");
            }
        });

    }

    private boolean saveWakeupAudio(String fileName, InputStream inputStream) {
        File fileDir = new File(wakeupAudioFilePath);
        if (!fileDir.exists()) {
            boolean mkdirs = fileDir.mkdirs();
            Log.d(TAG, "create file " + mkdirs + ", the path is " + wakeupAudioFilePath);
        }
        File file = new File(
                wakeupAudioFilePath + fileName);
//        if (!file.exists()) {
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            int len;
            long total = 0;
            byte[] buf = new byte[4096];
            while ((len = inputStream.read(buf)) != -1) {
                fileOutputStream.write(buf, 0, len);
                total += len;
            }
            Log.d(TAG, "saveWakeupAudio: total = " + total);
            fileOutputStream.flush();
            fileOutputStream.close();
            inputStream.close();
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
//        }
//        return true;
    }

    public void clearTask() {
        int size = mWakeupWordInfos.size();
        if (size > 0) {
            mWakeupWordInfos.clear();
            mSemaphore.release(size);
        }
    }

    private WakeupWordInfo getWakeupWordInfo() {
        return mWakeupWordInfos.poll();
    }

    public List<SpokesmanBean> getSpokesmanListByLanguage(String langCode) {
        if (mSpokesmanManager == null) {
            Log.w(TAG, "getSpokesmanListByLanguage: mSpokesmanManager is null, returning empty list");
            return new ArrayList<>();
        }
        return mSpokesmanManager.getSpokesmanListByLanguage(langCode);
    }
}
