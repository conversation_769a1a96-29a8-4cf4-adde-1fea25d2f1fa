/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.speechasrservice.data.spokesman;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.Context;
import android.database.ContentObserver;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.BaseColumns;
import android.util.Log;

import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.coreservice.data.provider.Column.DataType;
import com.ainirobot.coreservice.data.provider.SQLiteTable;
import com.ainirobot.speechasrservice.data.bean.SpokesmanBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 发言人
 * <p>
 * 数据更新依赖语音服务器返回，不需要本地数据初始化
 */
public class SpokesmanListHelper extends BaseDataHelper {
    private static final String TAG = "SpokesmanDataHelper";

    private Context mContext;

    public static final SQLiteTable TABLE = new SQLiteTable(SpokesmanField.TABLE_NAME,
            SpokesmanProvider.AUTHORITY, true) {
        @Override
        public void importData(Context context, SQLiteDatabase db) {
        }

        @Override
        public void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion) {
        }
    };

    static {
        TABLE.addColumn(SpokesmanField.LANG_CODE, DataType.TEXT)
                .addColumn(SpokesmanField.NICKNAME, DataType.TEXT)
                .addColumn(SpokesmanField.TYPE, DataType.TEXT)
                .addColumn(SpokesmanField.IS_DEFAULT, DataType.INTEGER)
                .addColumn(SpokesmanField.IS_CURRENT, DataType.INTEGER);
    }

    public SpokesmanListHelper(Context context) {
        super(context);
        this.mContext = context;
    }

    @Override
    public Uri getContentUri() {
        return TABLE.getContentUri();
    }

    private ContentValues getContentValues(SpokesmanBean info) {
        ContentValues values = new ContentValues();
        values.put(SpokesmanField.LANG_CODE, info.getLangCode());
        values.put(SpokesmanField.NICKNAME, info.getNickname());
        values.put(SpokesmanField.TYPE, info.getType());
        values.put(SpokesmanField.IS_DEFAULT, info.getIsDefault());
        values.put(SpokesmanField.IS_CURRENT, info.getIsCurrent());
        return values;
    }

    public void bulkInsert(List<SpokesmanBean> infos) {
        ArrayList<ContentValues> contentValues = new ArrayList<>();
        for (SpokesmanBean data : infos) {
            ContentValues values = getContentValues(data);
            contentValues.add(values);
        }

        ContentValues[] valueArray = new ContentValues[contentValues.size()];
        int insertRows = bulkInsert(contentValues.toArray(valueArray));
        Log.i(TAG, "bulkInsert: insertRows=" + insertRows);
    }

    public void insert(SpokesmanBean info) {
        ContentValues values = getContentValues(info);
        insert(values);
    }

    public void deleteAll() {
        int deleteRows = delete(null, null);
        Log.i(TAG, "deleteAll: deleteRows=" + deleteRows);
    }

    @SuppressLint("Range")
    public List<SpokesmanBean> getAllSpokesmanList() {
        List<SpokesmanBean> actionInfos = new ArrayList<>();
        Cursor cursor = query(null, null, null, null);
        if (cursor == null) {
            return actionInfos;
        }
        while (cursor.moveToNext()) {
            SpokesmanBean info = new SpokesmanBean();
            int langCodeIndex = cursor.getColumnIndex(SpokesmanField.LANG_CODE);
            int nicknameIndex = cursor.getColumnIndex(SpokesmanField.NICKNAME);
            int typeIndex = cursor.getColumnIndex(SpokesmanField.TYPE);
            int isDefaultIndex = cursor.getColumnIndex(SpokesmanField.IS_DEFAULT);
            int isCurrentIndex = cursor.getColumnIndex(SpokesmanField.IS_CURRENT);
            if (langCodeIndex >= 0) info.setLangCode(cursor.getString(langCodeIndex));
            if (nicknameIndex >= 0) info.setNickname(cursor.getString(nicknameIndex));
            if (typeIndex >= 0) info.setType(cursor.getString(typeIndex));
            if (isDefaultIndex >= 0) info.setIsDefault(cursor.getInt(isDefaultIndex));
            if (isCurrentIndex >= 0) info.setIsCurrent(cursor.getInt(isCurrentIndex));
            actionInfos.add(info);
        }
        cursor.close();
        return actionInfos;
    }

    @SuppressLint("Range")
    public List<SpokesmanBean> getSpokesmanListByLanguage(String langCode) {
        List<SpokesmanBean> actionInfos = new ArrayList<>();
        Cursor cursor = query(SpokesmanField.LANG_CODE + "=?",
                new String[]{langCode});
        if (cursor == null) {
            return actionInfos;
        }
        while (cursor.moveToNext()) {
            SpokesmanBean info = new SpokesmanBean();
            int langCodeIndex = cursor.getColumnIndex(SpokesmanField.LANG_CODE);
            int nicknameIndex = cursor.getColumnIndex(SpokesmanField.NICKNAME);
            int typeIndex = cursor.getColumnIndex(SpokesmanField.TYPE);
            int isDefaultIndex = cursor.getColumnIndex(SpokesmanField.IS_DEFAULT);
            int isCurrentIndex = cursor.getColumnIndex(SpokesmanField.IS_CURRENT);
            if (langCodeIndex >= 0) info.setLangCode(cursor.getString(langCodeIndex));
            if (nicknameIndex >= 0) info.setNickname(cursor.getString(nicknameIndex));
            if (typeIndex >= 0) info.setType(cursor.getString(typeIndex));
            if (isDefaultIndex >= 0) info.setIsDefault(cursor.getInt(isDefaultIndex));
            if (isCurrentIndex >= 0) info.setIsCurrent(cursor.getInt(isCurrentIndex));
            actionInfos.add(info);
        }
        cursor.close();
        return actionInfos;
    }

    @SuppressLint("Range")
    public String getUserDefaultSpokesman(String langCode) {
        String currentType = "";
        Cursor cursor = query(new String[]{SpokesmanField.TYPE},
                SpokesmanField.LANG_CODE + "=? and " + SpokesmanField.IS_CURRENT + "=?",
                new String[]{langCode, String.valueOf(SpokesmanBean.UseState.CURRENT)},
                null);
        if (cursor == null) {
            return currentType;
        }
        while (cursor.moveToNext()) {
            int typeIndex = cursor.getColumnIndex(SpokesmanField.TYPE);
            if (typeIndex >= 0) currentType = cursor.getString(typeIndex);
        }
        Log.e(TAG, "getUserDefaultSpokesman: currentType=" + currentType);
        cursor.close();
        return currentType;
    }

    @SuppressLint("Range")
    public String getServerDefaultSpokesman(String langCode) {
        String defaulttType = "";
        Cursor cursor = query(new String[]{SpokesmanField.TYPE},
                SpokesmanField.LANG_CODE + "=? and " + SpokesmanField.IS_DEFAULT + "=?",
                new String[]{langCode, String.valueOf(SpokesmanBean.DefaultState.DEFAULT)},
                null);
        if (cursor == null) {
            return defaulttType;
        }
        while (cursor.moveToNext()) {
            int typeIndex = cursor.getColumnIndex(SpokesmanField.TYPE);
            if (typeIndex >= 0) defaulttType = cursor.getString(typeIndex);
        }
        cursor.close();
        Log.e(TAG, "getServerDefaultSpokesman: defaulttType=" + defaulttType);
        return defaulttType;
    }

    public void updateSpecialLangCurrentState(String langCode, int state) {
        Log.i(TAG, "updateSpecialLangCurrentState: langCode=" + langCode + " state=" + state);
        ContentValues contentValues = new ContentValues();
        contentValues.put(SpokesmanField.IS_CURRENT, state);
        int updateRows = update(contentValues,
                SpokesmanField.LANG_CODE + "=?", new String[]{langCode});
        Log.i(TAG, "updateSpecialLangCurrentState: updateRows=" + updateRows);
    }

    public void updateSpecialTypeCurrentState(String langCode, String type, int state) {
        Log.i(TAG, "updateSpecialTypeCurrentState: langCode=" + langCode
                + " type=" + type + " state=" + state);
        ContentValues contentValues = new ContentValues();
        contentValues.put(SpokesmanField.IS_CURRENT, state);
        int updateRows = update(contentValues,
                SpokesmanField.LANG_CODE + "=? and " + SpokesmanField.TYPE + "=?",
                new String[]{langCode, type});
        Log.i(TAG, "updateSpecialTypeCurrentState: updateRows=" + updateRows);
    }


    public void registerListener(ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                getContentUri(), true, contentObserver);
    }

    public static final class SpokesmanField implements BaseColumns {
        static final String TABLE_NAME = "spokesman_list";

        static final String LANG_CODE = "langCode";
        static final String NICKNAME = "nickname";
        static final String TYPE = "type";
        static final String IS_DEFAULT = "is_default";
        static final String IS_CURRENT = "is_current";
    }

}
