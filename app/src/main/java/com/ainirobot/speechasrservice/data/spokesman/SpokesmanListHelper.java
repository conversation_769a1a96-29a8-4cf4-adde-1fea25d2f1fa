/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.speechasrservice.data.spokesman;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.Context;
import android.database.ContentObserver;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.provider.BaseColumns;
import android.util.Log;

import com.ainirobot.agent.BuildConfig;
import com.ainirobot.coreservice.data.provider.BaseDataHelper;
import com.ainirobot.coreservice.data.provider.Column.DataType;
import com.ainirobot.coreservice.data.provider.SQLiteTable;
import com.ainirobot.speechasrservice.data.bean.SpokesmanBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 发言人
 * <p>
 * 数据更新依赖语音服务器返回，不需要本地数据初始化
 */
public class SpokesmanListHelper extends BaseDataHelper {
    private static final String TAG = "SpokesmanDataHelper";

    private Context mContext;

    public static final SQLiteTable TABLE = new SQLiteTable(SpokesmanField.TABLE_NAME,
            SpokesmanProvider.AUTHORITY, true) {
        @Override
        public void importData(Context context, SQLiteDatabase db) {
        }

        @Override
        public void update(Context context, SQLiteDatabase db, int oldVersion, int newVersion) {
        }
    };

    static {
        TABLE.addColumn(SpokesmanField.LANG_CODE, DataType.TEXT)
                .addColumn(SpokesmanField.NICKNAME, DataType.TEXT)
                .addColumn(SpokesmanField.TYPE, DataType.TEXT)
                .addColumn(SpokesmanField.IS_DEFAULT, DataType.INTEGER)
                .addColumn(SpokesmanField.IS_CURRENT, DataType.INTEGER);
    }

    public SpokesmanListHelper(Context context) {
        super(context);
        this.mContext = context;

        // 在DEBUG模式下，主动测试列索引问题
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "SpokesmanListHelper: DEBUG mode - testing for column index issues");
            try {
                // 延迟执行，确保Provider已经初始化
                new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            debugCursorColumnIssue("zh_CN");
                        } catch (Exception e) {
                            Log.e(TAG, "SpokesmanListHelper: Error during debug test", e);
                        }
                    }
                }, 1000); // 延迟1秒执行
            } catch (Exception e) {
                Log.e(TAG, "SpokesmanListHelper: Error setting up debug test", e);
            }
        }
    }

    @Override
    public Uri getContentUri() {
        return TABLE.getContentUri();
    }

    private ContentValues getContentValues(SpokesmanBean info) {
        ContentValues values = new ContentValues();
        values.put(SpokesmanField.LANG_CODE, info.getLangCode());
        values.put(SpokesmanField.NICKNAME, info.getNickname());
        values.put(SpokesmanField.TYPE, info.getType());
        values.put(SpokesmanField.IS_DEFAULT, info.getIsDefault());
        values.put(SpokesmanField.IS_CURRENT, info.getIsCurrent());
        return values;
    }

    public void bulkInsert(List<SpokesmanBean> infos) {
        ArrayList<ContentValues> contentValues = new ArrayList<>();
        for (SpokesmanBean data : infos) {
            ContentValues values = getContentValues(data);
            contentValues.add(values);
        }

        ContentValues[] valueArray = new ContentValues[contentValues.size()];
        int insertRows = bulkInsert(contentValues.toArray(valueArray));
        Log.i(TAG, "bulkInsert: insertRows=" + insertRows);
    }

    public void insert(SpokesmanBean info) {
        ContentValues values = getContentValues(info);
        insert(values);
    }

    public void deleteAll() {
        int deleteRows = delete(null, null);
        Log.i(TAG, "deleteAll: deleteRows=" + deleteRows);
    }

    @SuppressLint("Range")
    public List<SpokesmanBean> getAllSpokesmanList() {
        List<SpokesmanBean> actionInfos = new ArrayList<>();
        Cursor cursor = query(null, null, null, null);
        if (cursor == null) {
            return actionInfos;
        }
        while (cursor.moveToNext()) {
            SpokesmanBean info = new SpokesmanBean();
            int langCodeIndex = cursor.getColumnIndex(SpokesmanField.LANG_CODE);
            int nicknameIndex = cursor.getColumnIndex(SpokesmanField.NICKNAME);
            int typeIndex = cursor.getColumnIndex(SpokesmanField.TYPE);
            int isDefaultIndex = cursor.getColumnIndex(SpokesmanField.IS_DEFAULT);
            int isCurrentIndex = cursor.getColumnIndex(SpokesmanField.IS_CURRENT);
            if (langCodeIndex >= 0) info.setLangCode(cursor.getString(langCodeIndex));
            if (nicknameIndex >= 0) info.setNickname(cursor.getString(nicknameIndex));
            if (typeIndex >= 0) info.setType(cursor.getString(typeIndex));
            if (isDefaultIndex >= 0) info.setIsDefault(cursor.getInt(isDefaultIndex));
            if (isCurrentIndex >= 0) info.setIsCurrent(cursor.getInt(isCurrentIndex));
            actionInfos.add(info);
        }
        cursor.close();
        return actionInfos;
    }

    @SuppressLint("Range")
    public List<SpokesmanBean> getSpokesmanListByLanguage(String langCode) {
        List<SpokesmanBean> actionInfos = new ArrayList<>();
        Cursor cursor = query(SpokesmanField.LANG_CODE + "=?",
                new String[]{langCode});
        if (cursor == null) {
            return actionInfos;
        }
        while (cursor.moveToNext()) {
            SpokesmanBean info = new SpokesmanBean();
            int langCodeIndex = cursor.getColumnIndex(SpokesmanField.LANG_CODE);
            int nicknameIndex = cursor.getColumnIndex(SpokesmanField.NICKNAME);
            int typeIndex = cursor.getColumnIndex(SpokesmanField.TYPE);
            int isDefaultIndex = cursor.getColumnIndex(SpokesmanField.IS_DEFAULT);
            int isCurrentIndex = cursor.getColumnIndex(SpokesmanField.IS_CURRENT);
            if (langCodeIndex >= 0) info.setLangCode(cursor.getString(langCodeIndex));
            if (nicknameIndex >= 0) info.setNickname(cursor.getString(nicknameIndex));
            if (typeIndex >= 0) info.setType(cursor.getString(typeIndex));
            if (isDefaultIndex >= 0) info.setIsDefault(cursor.getInt(isDefaultIndex));
            if (isCurrentIndex >= 0) info.setIsCurrent(cursor.getInt(isCurrentIndex));
            actionInfos.add(info);
        }
        cursor.close();
        return actionInfos;
    }

    @SuppressLint("Range")
    public String getUserDefaultSpokesman(String langCode) {
        String currentType = "";
        Log.d(TAG, "getUserDefaultSpokesman: querying for langCode=" + langCode + ", TYPE field='" + SpokesmanField.TYPE + "'");

        Cursor cursor = query(new String[]{SpokesmanField.TYPE},
                SpokesmanField.LANG_CODE + "=? and " + SpokesmanField.IS_CURRENT + "=?",
                new String[]{langCode, String.valueOf(SpokesmanBean.UseState.CURRENT)},
                null);
        if (cursor == null) {
            Log.w(TAG, "getUserDefaultSpokesman: cursor is null");
            return currentType;
        }

        try {
            // 详细调试信息
            int columnCount = cursor.getColumnCount();
            int rowCount = cursor.getCount();
            Log.d(TAG, "getUserDefaultSpokesman: cursor columnCount=" + columnCount + ", rowCount=" + rowCount);

            // 打印所有列名
            String[] columnNames = cursor.getColumnNames();
            for (int i = 0; i < columnNames.length; i++) {
                Log.d(TAG, "getUserDefaultSpokesman: column[" + i + "]='" + columnNames[i] + "'");
            }

            while (cursor.moveToNext()) {
                // 使用安全的方法获取值
                currentType = getStringSafely(cursor, SpokesmanField.TYPE);
                if (currentType != null) {
                    Log.d(TAG, "getUserDefaultSpokesman: successfully got currentType=" + currentType);
                    break; // 找到第一个匹配的记录就退出
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "getUserDefaultSpokesman: error processing cursor", e);
        } finally {
            cursor.close();
        }

        Log.e(TAG, "getUserDefaultSpokesman: final currentType=" + currentType);
        return currentType;
    }

    @SuppressLint("Range")
    public String getServerDefaultSpokesman(String langCode) {
        String defaulttType = "";
        Log.d(TAG, "getServerDefaultSpokesman: querying for langCode=" + langCode + ", TYPE field='" + SpokesmanField.TYPE + "'");

        Cursor cursor = query(new String[]{SpokesmanField.TYPE},
                SpokesmanField.LANG_CODE + "=? and " + SpokesmanField.IS_DEFAULT + "=?",
                new String[]{langCode, String.valueOf(SpokesmanBean.DefaultState.DEFAULT)},
                null);
        if (cursor == null) {
            Log.w(TAG, "getServerDefaultSpokesman: cursor is null");
            return defaulttType;
        }

        try {
            // 详细调试信息
            int columnCount = cursor.getColumnCount();
            int rowCount = cursor.getCount();
            Log.d(TAG, "getServerDefaultSpokesman: cursor columnCount=" + columnCount + ", rowCount=" + rowCount);

            // 打印所有列名
            String[] columnNames = cursor.getColumnNames();
            for (int i = 0; i < columnNames.length; i++) {
                Log.d(TAG, "getServerDefaultSpokesman: column[" + i + "]='" + columnNames[i] + "'");
            }

            while (cursor.moveToNext()) {
                // 使用安全的方法获取值
                defaulttType = getStringSafely(cursor, SpokesmanField.TYPE);
                if (defaulttType != null) {
                    Log.d(TAG, "getServerDefaultSpokesman: successfully got defaulttType=" + defaulttType);
                    break; // 找到第一个匹配的记录就退出
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "getServerDefaultSpokesman: error processing cursor", e);
        } finally {
            cursor.close();
        }

        Log.e(TAG, "getServerDefaultSpokesman: final defaulttType=" + defaulttType);
        return defaulttType;
    }

    public void updateSpecialLangCurrentState(String langCode, int state) {
        Log.i(TAG, "updateSpecialLangCurrentState: langCode=" + langCode + " state=" + state);
        ContentValues contentValues = new ContentValues();
        contentValues.put(SpokesmanField.IS_CURRENT, state);
        int updateRows = update(contentValues,
                SpokesmanField.LANG_CODE + "=?", new String[]{langCode});
        Log.i(TAG, "updateSpecialLangCurrentState: updateRows=" + updateRows);
    }

    public void updateSpecialTypeCurrentState(String langCode, String type, int state) {
        Log.i(TAG, "updateSpecialTypeCurrentState: langCode=" + langCode
                + " type=" + type + " state=" + state);
        ContentValues contentValues = new ContentValues();
        contentValues.put(SpokesmanField.IS_CURRENT, state);
        int updateRows = update(contentValues,
                SpokesmanField.LANG_CODE + "=? and " + SpokesmanField.TYPE + "=?",
                new String[]{langCode, type});
        Log.i(TAG, "updateSpecialTypeCurrentState: updateRows=" + updateRows);
    }


    public void registerListener(ContentObserver contentObserver) {
        mContext.getContentResolver().registerContentObserver(
                getContentUri(), true, contentObserver);
    }

    /**
     * 调试方法：测试Cursor的列索引问题
     */
    public void debugCursorColumnIssue(String langCode) {
        Log.e(TAG, "=== CRITICAL DEBUG: Testing cursor column issue for langCode=" + langCode + " ===");
        Log.e(TAG, "DEBUG: SpokesmanField.TYPE = '" + SpokesmanField.TYPE + "'");
        Log.e(TAG, "DEBUG: SpokesmanField.LANG_CODE = '" + SpokesmanField.LANG_CODE + "'");
        Log.e(TAG, "DEBUG: SpokesmanField.IS_CURRENT = '" + SpokesmanField.IS_CURRENT + "'");
        Log.e(TAG, "DEBUG: Content URI = " + getContentUri());

        // 测试1：查询单列 - 模拟问题场景
        Log.e(TAG, "DEBUG: Test 1 - Single column query (TYPE only) - PROBLEM SCENARIO");
        Cursor cursor1 = query(new String[]{SpokesmanField.TYPE}, null, null, null);
        if (cursor1 != null) {
            try {
                Log.e(TAG, "DEBUG: Test 1 - columnCount=" + cursor1.getColumnCount() + ", rowCount=" + cursor1.getCount());
                String[] columnNames = cursor1.getColumnNames();
                for (int i = 0; i < columnNames.length; i++) {
                    Log.e(TAG, "DEBUG: Test 1 - column[" + i + "]='" + columnNames[i] + "'");
                }
                int typeIndex = cursor1.getColumnIndex(SpokesmanField.TYPE);
                Log.e(TAG, "DEBUG: Test 1 - getColumnIndex('" + SpokesmanField.TYPE + "') = " + typeIndex + " (EXPECTED: 0)");

                // 测试所有可能的列名变体
                Log.e(TAG, "DEBUG: Test 1 - Testing column name variants:");
                Log.e(TAG, "DEBUG: Test 1 - getColumnIndex('type') = " + cursor1.getColumnIndex("type"));
                Log.e(TAG, "DEBUG: Test 1 - getColumnIndex('TYPE') = " + cursor1.getColumnIndex("TYPE"));
                Log.e(TAG, "DEBUG: Test 1 - getColumnIndex('Type') = " + cursor1.getColumnIndex("Type"));

                if (typeIndex >= cursor1.getColumnCount()) {
                    Log.e(TAG, "DEBUG: Test 1 - ANOMALY DETECTED: typeIndex=" + typeIndex + " >= columnCount=" + cursor1.getColumnCount());
                }
            } finally {
                cursor1.close();
            }
        } else {
            Log.e(TAG, "DEBUG: Test 1 - cursor1 is NULL");
        }

        // 测试2：查询所有列
        Log.e(TAG, "DEBUG: Test 2 - All columns query");
        Cursor cursor2 = query(null, null, null, null);
        if (cursor2 != null) {
            try {
                Log.e(TAG, "DEBUG: Test 2 - columnCount=" + cursor2.getColumnCount() + ", rowCount=" + cursor2.getCount());
                String[] columnNames = cursor2.getColumnNames();
                for (int i = 0; i < columnNames.length; i++) {
                    Log.e(TAG, "DEBUG: Test 2 - column[" + i + "]='" + columnNames[i] + "'");
                }
                int typeIndex = cursor2.getColumnIndex(SpokesmanField.TYPE);
                Log.e(TAG, "DEBUG: Test 2 - getColumnIndex('" + SpokesmanField.TYPE + "') = " + typeIndex);
            } finally {
                cursor2.close();
            }
        } else {
            Log.e(TAG, "DEBUG: Test 2 - cursor2 is NULL");
        }

        // 测试3：模拟实际的问题查询
        Log.e(TAG, "DEBUG: Test 3 - Exact problem query simulation");
        Cursor cursor3 = query(new String[]{SpokesmanField.TYPE},
                SpokesmanField.LANG_CODE + "=? and " + SpokesmanField.IS_CURRENT + "=?",
                new String[]{langCode, String.valueOf(SpokesmanBean.UseState.CURRENT)},
                null);
        if (cursor3 != null) {
            try {
                Log.e(TAG, "DEBUG: Test 3 - columnCount=" + cursor3.getColumnCount() + ", rowCount=" + cursor3.getCount());
                String[] columnNames = cursor3.getColumnNames();
                for (int i = 0; i < columnNames.length; i++) {
                    Log.e(TAG, "DEBUG: Test 3 - column[" + i + "]='" + columnNames[i] + "'");
                }
                int typeIndex = cursor3.getColumnIndex(SpokesmanField.TYPE);
                Log.e(TAG, "DEBUG: Test 3 - getColumnIndex('" + SpokesmanField.TYPE + "') = " + typeIndex + " (THIS IS THE PROBLEM CASE)");

                if (typeIndex >= cursor3.getColumnCount()) {
                    Log.e(TAG, "DEBUG: Test 3 - CRITICAL ANOMALY: typeIndex=" + typeIndex + " >= columnCount=" + cursor3.getColumnCount());
                    Log.e(TAG, "DEBUG: Test 3 - This would cause the original crash!");
                }
            } finally {
                cursor3.close();
            }
        } else {
            Log.e(TAG, "DEBUG: Test 3 - cursor3 is NULL");
        }

        Log.e(TAG, "=== CRITICAL DEBUG: End of cursor column issue test ===");
    }

    /**
     * 静态方法：从外部触发诊断测试
     * 使用方法：SpokesmanListHelper.triggerDiagnosticTest(context);
     */
    public static void triggerDiagnosticTest(Context context) {
        Log.e("SpokesmanDataHelper", "=== EXTERNAL DIAGNOSTIC TEST TRIGGERED ===");
        try {
            SpokesmanListHelper helper = new SpokesmanListHelper(context);
            helper.debugCursorColumnIssue("zh_CN");

            // 尝试触发实际的问题方法
            Log.e("SpokesmanDataHelper", "=== Testing actual problem method ===");
            String result = helper.getUserDefaultSpokesman("zh_CN");
            Log.e("SpokesmanDataHelper", "=== getUserDefaultSpokesman result: " + result + " ===");

        } catch (Exception e) {
            Log.e("SpokesmanDataHelper", "=== DIAGNOSTIC TEST CAUGHT EXCEPTION ===", e);
            // 不重新抛出，让应用继续运行
        }
        Log.e("SpokesmanDataHelper", "=== EXTERNAL DIAGNOSTIC TEST COMPLETED ===");
    }

    /**
     * 安全地从Cursor获取字符串值
     * @param cursor Cursor对象
     * @param columnName 列名
     * @return 字符串值，如果出错返回null
     */
    private String getStringSafely(Cursor cursor, String columnName) {
        try {
            if (cursor == null) {
                Log.w(TAG, "getStringSafely: cursor is null");
                return null;
            }

            int columnIndex = cursor.getColumnIndex(columnName);
            Log.d(TAG, "getStringSafely: getColumnIndex('" + columnName + "') = " + columnIndex);

            if (columnIndex >= 0 && columnIndex < cursor.getColumnCount()) {
                String value = cursor.getString(columnIndex);
                Log.d(TAG, "getStringSafely: got value from index " + columnIndex + ": " + value);
                return value;
            } else {
                // 检测到异常情况：列索引无效
                Log.e(TAG, "CRITICAL ERROR: getStringSafely: invalid columnIndex=" + columnIndex + " for column='" + columnName + "', columnCount=" + cursor.getColumnCount());

                // 调用调试方法收集完整信息
                Log.e(TAG, "CRITICAL ERROR: Triggering debug analysis before crash...");
                debugCursorColumnIssue("debug_on_error");

                // 尝试恢复：如果是单列查询且查询的就是这个列，尝试使用索引0
                if (cursor.getColumnCount() == 1) {
                    String[] columnNames = cursor.getColumnNames();
                    if (columnNames.length > 0) {
                        Log.d(TAG, "getStringSafely: single column '" + columnNames[0] + "', trying index 0");
                        // 检查列名是否匹配（忽略大小写）
                        if (columnName.equalsIgnoreCase(columnNames[0])) {
                            String value = cursor.getString(0);
                            Log.d(TAG, "getStringSafely: RECOVERED - got value from index 0: " + value);
                            return value;
                        }
                    }
                }

                // 如果无法恢复，主动触发crash以获取完整堆栈信息
                Log.e(TAG, "CRITICAL ERROR: Cannot recover from column index issue, triggering controlled crash for diagnosis");
                throw new IllegalStateException("Column index anomaly detected: getColumnIndex('" + columnName + "') returned " + columnIndex +
                    " but cursor only has " + cursor.getColumnCount() + " columns. This is the controlled crash for diagnosis.");
            }
        } catch (IllegalStateException e) {
            // 重新抛出我们主动触发的异常
            throw e;
        } catch (Exception e) {
            Log.e(TAG, "getStringSafely: unexpected error getting string for column='" + columnName + "'", e);

            // 对于其他异常，也调用调试方法
            Log.e(TAG, "UNEXPECTED ERROR: Triggering debug analysis...");
            debugCursorColumnIssue("debug_on_unexpected_error");

            return null;
        }
    }

    /**
     * 安全地从Cursor获取整数值
     * @param cursor Cursor对象
     * @param columnName 列名
     * @return 整数值，如果出错返回0
     */
    private int getIntSafely(Cursor cursor, String columnName) {
        try {
            if (cursor == null) {
                Log.w(TAG, "getIntSafely: cursor is null");
                return 0;
            }

            int columnIndex = cursor.getColumnIndex(columnName);
            Log.d(TAG, "getIntSafely: getColumnIndex('" + columnName + "') = " + columnIndex);

            if (columnIndex >= 0 && columnIndex < cursor.getColumnCount()) {
                int value = cursor.getInt(columnIndex);
                Log.d(TAG, "getIntSafely: got value from index " + columnIndex + ": " + value);
                return value;
            } else {
                // 检测到异常情况：列索引无效
                Log.e(TAG, "CRITICAL ERROR: getIntSafely: invalid columnIndex=" + columnIndex + " for column='" + columnName + "', columnCount=" + cursor.getColumnCount());

                // 调用调试方法收集完整信息
                Log.e(TAG, "CRITICAL ERROR: Triggering debug analysis before crash...");
                debugCursorColumnIssue("debug_on_int_error");

                // 主动触发crash以获取完整堆栈信息
                Log.e(TAG, "CRITICAL ERROR: Cannot recover from column index issue, triggering controlled crash for diagnosis");
                throw new IllegalStateException("Column index anomaly detected in getIntSafely: getColumnIndex('" + columnName + "') returned " + columnIndex +
                    " but cursor only has " + cursor.getColumnCount() + " columns. This is the controlled crash for diagnosis.");
            }
        } catch (IllegalStateException e) {
            // 重新抛出我们主动触发的异常
            throw e;
        } catch (Exception e) {
            Log.e(TAG, "getIntSafely: unexpected error getting int for column='" + columnName + "'", e);

            // 对于其他异常，也调用调试方法
            Log.e(TAG, "UNEXPECTED ERROR: Triggering debug analysis...");
            debugCursorColumnIssue("debug_on_unexpected_int_error");

            return 0;
        }
    }

    public static final class SpokesmanField implements BaseColumns {
        static final String TABLE_NAME = "spokesman_list";

        static final String LANG_CODE = "langCode";
        static final String NICKNAME = "nickname";
        static final String TYPE = "type";
        static final String IS_DEFAULT = "is_default";
        static final String IS_CURRENT = "is_current";
    }

}
