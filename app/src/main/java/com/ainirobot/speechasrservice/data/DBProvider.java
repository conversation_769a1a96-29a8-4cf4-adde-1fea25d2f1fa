package com.ainirobot.speechasrservice.data;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.Context;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;

public class DB<PERSON>rovider extends ContentProvider {

    private static final String TAG = "DBProvider";
    public static final String authority = "com.ainirobot.agentservice";
    public static final String RAWQUERY = "rawQuery";
    private DBHelper dbHelper;

    private static final int CODE_WAKEUP_WORD = 1;
    private static final int CODE_WORD_MAPPING = 2;
    private static final int CODE_WORD_USER = 3;
    private static final int CODE_LOCAL_MUSIC = 4;

    private static final UriMatcher URI_MATCHER = new UriMatcher(UriMatcher.NO_MATCH);

    static {
        URI_MATCHER.addURI(authority, ConstantDB.WordWakeUp.TABLE_NAME, CODE_WAKEUP_WORD);
        URI_MATCHER.addURI(authority, ConstantDB.WordMapping.TABLE_NAME, CODE_WORD_MAPPING);
        URI_MATCHER.addURI(authority, ConstantDB.WordUser.TABLE_NAME, CODE_WORD_USER);
        URI_MATCHER.addURI(authority, ConstantDB.LocalMusic.TABLE_NAME, CODE_LOCAL_MUSIC);
    }

    private Context mContext;

    @Override
    public boolean onCreate() {
        Log.i(TAG, "onCreate");
        mContext = this.getContext();
        this.dbHelper = new DBHelper(mContext);
        return false;
    }

    @Nullable
    @Override
    public Cursor query(@NonNull Uri uri, @Nullable String[] projection, @Nullable String selection,
                        @Nullable String[] selectionArgs, @Nullable String sortOrder) {
        Log.i(TAG, "query");
        SQLiteDatabase db = dbHelper.getReadableDatabase();
        String tableName = getType(uri);
        if (tableName == null) {
            throw new IllegalArgumentException("Unsupported URI: " + uri);
        }
        if (TextUtils.equals(sortOrder,RAWQUERY)){
            return db.rawQuery(selection, selectionArgs);
        }else {
            return db.query(tableName, projection, selection, selectionArgs,
                    null, null, sortOrder);
        }

    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues values) {
        Log.i(TAG, "insert");
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        String tableName = getType(uri);
        if (tableName == null) {
            throw new IllegalArgumentException("Unsupported URI: " + uri);
        }

        db.insert(tableName, null, values);
        mContext.getContentResolver().notifyChange(uri, null);
        return uri;
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String selection, @Nullable String[] selectionArgs) {
        Log.i(TAG, "delete");
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        String tableName = getType(uri);
        if (tableName == null) {
            throw new IllegalArgumentException("Unsupported URI: " + uri);
        }

        int count = db.delete(tableName, selection, selectionArgs);
        if (count > 0) {
            mContext.getContentResolver().notifyChange(uri, null);
        }
        return count;
    }

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues values, @Nullable String selection, @Nullable String[] selectionArgs) {
        Log.i(TAG, "update");
        SQLiteDatabase db = dbHelper.getWritableDatabase();
        String tableName = getType(uri);
        if (tableName == null) {
            throw new IllegalArgumentException("Unsupported URI: " + uri);
        }

        int row = db.update(tableName, values, selection, selectionArgs);
        if (row > 0) {
            mContext.getContentResolver().notifyChange(uri, null);
        }
        return row;
    }

    @Nullable
    @Override
    public String getType(@NonNull Uri uri) {
        String tableName = null;
        switch (URI_MATCHER.match(uri)) {
            case CODE_WAKEUP_WORD:
                tableName = ConstantDB.WordWakeUp.TABLE_NAME;
                break;
            case CODE_WORD_MAPPING:
                tableName = ConstantDB.WordMapping.TABLE_NAME;
                break;
            case CODE_WORD_USER:
                tableName = ConstantDB.WordUser.TABLE_NAME;
                break;
            case CODE_LOCAL_MUSIC:
                tableName = ConstantDB.LocalMusic.TABLE_NAME;
                break;
            default:
                break;
        }
        return tableName;
    }


}
