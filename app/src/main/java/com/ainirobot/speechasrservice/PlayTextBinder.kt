package com.ainirobot.speechasrservice

import android.os.Handler
import com.ainirobot.common.Config
import com.ainirobot.coreservice.listener.IMusicListener
import com.ainirobot.coreservice.listener.ITextListener
import com.ainirobot.coreservice.listener.IToneListener

class PlayTextBinder(
    private val text: String?,
    private val handler: Handler,
    private var listener: ITextListener?,
) : ITextListener.Stub(), Runnable {

    @Volatile
    private var isCallCompleted = false

    override fun onStart() {
        listener?.onStart()
        val timeout = if (text.isNullOrEmpty()) {
            5000
        } else {
            Config.calculateTtsDuration(text) + 3000
        }
        handler.postDelayed(this, timeout)
    }

    override fun onStop() {
        listener?.onStop()
        notifyComplete()
    }

    override fun onError() {
        listener?.onError()
        notifyComplete()
    }

    override fun onComplete() {
        listener?.onComplete()
        notifyComplete()
    }

    override fun onStreamComplete(streamSid: String?, textSid: String?) {

    }

    private fun notifyComplete() {
        if (!isCallCompleted) {
            handler.removeCallbacks(this)
            isCallCompleted = true
        }
    }

    override fun run() {
        listener?.onError()
        listener = null
    }
}

class PlayToneBinder(
    private val handler: Handler,
    private var listener: IToneListener?
) : IToneListener.Stub(), Runnable {

    @Volatile
    private var isCallCompleted = false

    override fun onStart() {
        listener?.onStart()
        handler.postDelayed(this, 10000)
    }

    override fun onStop() {
        listener?.onStop()
        notifyComplete()
    }

    override fun onError() {
        listener?.onError()
        notifyComplete()
    }

    override fun onComplete() {
        listener?.onComplete()
        notifyComplete()
    }

    private fun notifyComplete() {
        if (!isCallCompleted) {
            handler.removeCallbacks(this)
            isCallCompleted = true
        }
    }

    override fun run() {
        listener?.onError()
        listener = null
    }
}

class PlayMusicBinder(
    private val handler: Handler,
    private var listener: IMusicListener?
) : IMusicListener.Stub(), Runnable {

    @Volatile
    private var isCallCompleted = false

    override fun onStart() {
        listener?.onStart()
        handler.postDelayed(this, 300000)
    }

    override fun onResume() {
        listener?.onResume()
    }

    override fun onPause() {
        listener?.onPause()
    }

    override fun onStop() {
        listener?.onStop()
        notifyComplete()
    }

    override fun onError() {
        listener?.onError()
        notifyComplete()
    }

    override fun onComplete() {
        listener?.onComplete()
        notifyComplete()
    }

    private fun notifyComplete() {
        if (!isCallCompleted) {
            handler.removeCallbacks(this)
            isCallCompleted = true
        }
    }

    override fun run() {
        listener?.onError()
        listener = null
    }
}