/*
 *     Copyright (C) 2017 OrionStar Technology Project
 *
 *     Licensed under the Apache License, Version 2.0 (the "License");
 *     you may not use this file except in compliance with the License.
 *     You may obtain a copy of the License at
 *
 *          http://www.apache.org/licenses/LICENSE-2.0
 *
 *     Unless required by applicable law or agreed to in writing, software
 *     distributed under the License is distributed on an "AS IS" BASIS,
 *     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     See the License for the specific language governing permissions and
 *     limitations under the License.
 */

package com.ainirobot.speechasrservice.utils;

import android.util.Log;

public class LogMsg {
    private String msg;
    private boolean highlight;
    private boolean clearLog = false;

    public LogMsg(String msg, boolean highlight, boolean clearLog) {
        this.msg = msg;
        this.highlight = highlight;
        this.clearLog = clearLog;
    }

    public LogMsg(String msg, boolean highlight) {
        this.msg = msg;
        this.highlight = highlight;
    }

    public String getMsg() {
        return msg;
    }

    public boolean isHighlight() {
        return highlight;
    }

    public boolean isClearLog() {
        return clearLog;
    }

    @Override
    public String toString() {
        return msg;
    }

    public static void print(String tag, String message) {
        Log.v(tag, message);
    }
}
