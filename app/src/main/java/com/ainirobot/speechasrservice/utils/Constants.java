/*
 *     Copyright (C) 2017 OrionStar Technology Project
 *
 *     Licensed under the Apache License, Version 2.0 (the "License");
 *     you may not use this file except in compliance with the License.
 *     You may obtain a copy of the License at
 *
 *          http://www.apache.org/licenses/LICENSE-2.0
 *
 *     Unless required by applicable law or agreed to in writing, software
 *     distributed under the License is distributed on an "AS IS" BASIS,
 *     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     See the License for the specific language governing permissions and
 *     limitations under the License.
 */
package com.ainirobot.speechasrservice.utils;

public class Constants {


    //命令测试开关，配合自动化测试
    public static final String COMMAND_TEST_SWITCH = "command_test_switch"; // 命令测试开关

    public static final String SPLIT_JOINT_CHAR = "&";
    /**
     * 区分文本query
     */
    public static final String SID_WRITE = "writing_";
    public static final float MAX_ANGLE_CENTER_INPUT = 360;
    public static final float MIN_ANGLE_CENTER_INPUT = 0;
    public static final float MAX_ANGLE_RANGE_INPUT = 120;
    public static final float MIN_ANGLE_RANGE_INPUT = 0;
    public static final String KEYSTORE_FILE = "client.p12";
    public static final String KEYSTORE_PASS = "2E4AFF85C93251EBB42F96D1ACBB2E6D";
    public static final String ACTION_TIME_COMPLETED = "android.intent.action.NTP_TIME_SET";
    public static final String ACTION_TEST_QUERYBYTEXT = "ainirobot.test.action.QUERYBYTEXT";
    public static final String ACTION_TEST_PLAYTEXT = "ainirobot.test.action.PLAYTEXT";
    public static final String ACTION_PRIMITIVE_AUDIO
            = "ainirobot.record.action.PRIMITIVE_AUDIO";
    public static final String ACTION_PRIMITIVE_AUDIO_RESULT
            = "ainirobot.record.action.PRIMITIVE_AUDIO_RESULT";
    public static final String ACTION_WAKEUP_ID = "action_wakeup_id";
    public static final String ACTION_SWITCH_SPEECH = "action_switch_speech";
    public static final String BROADCAST_PERMISSION_WAKEUP_ID = "com.ainirobot.permission.WAKEUP_ID_RECEIVER";
    public static final String BROADCAST_PERMISSION_COMMAND = "com.ainirobot.permission.command";
    private static final int WAKEUP_CUSTOM = 201;//自定义唤醒词
    private static final int WAKEUP_DEFAULT = 200;//默认唤醒词（小豹小豹）
    public static final String SPLIT_WAKEUP_CHAR = ",";
    public static final String WAKEUPSTR = String.valueOf(Constants.WAKEUP_DEFAULT) +
            Constants.SPLIT_WAKEUP_CHAR +
            Constants.WAKEUP_CUSTOM;
    public static final int WAKEUP_OFFSET = 30;//唤醒词偏移量
    public static final int AUDIOAUTH_REGIST = 0;//声纹注册
    public static final int AUDIOAUTH_QUERY = 3;//声纹查询
    public static final int AUDIOAUTH_DELETE = 4;//声纹删除

    public static final String LANGUAGES_RANGE_SPLIT = ",";

    //用于区分是主唤醒词还是副唤醒词
    public static final int USER_SET_WORD = 0;
    public static final int PRESET_DEFAULT_WORD = 1;
    public static final String SAIPH_PRESET_DEFAULT_CHINESE_WORD = "欧尅拉克伊";
    public static final String MINI_PRESET_DEFAULT_CHINESE_WORD = "欧尅迷妮";

}
