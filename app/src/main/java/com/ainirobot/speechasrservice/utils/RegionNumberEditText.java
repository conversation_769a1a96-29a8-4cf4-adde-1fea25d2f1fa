package com.ainirobot.speechasrservice.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.util.Log;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;

@SuppressLint("AppCompatCustomView")
public class RegionNumberEditText extends EditText {
    private float max = -1;
    private float min = -1;
    private int inputType;

    public RegionNumberEditText(Context context) {
        super(context);
    }

    public RegionNumberEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public RegionNumberEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    /**
     * 设置输入数字的范围
     *
     * @param maxNum 最大数
     * @param minNum 最小数
     */
    public void setRegion(float maxNum, float minNum) {
        inputType = EditorInfo.TYPE_CLASS_NUMBER | EditorInfo.TYPE_NUMBER_FLAG_DECIMAL;
        setInputType(inputType);
        this.max = maxNum;
        this.min = minNum;
    }

    public void setRegion(int maxNum, int minNum) {
        inputType = EditorInfo.TYPE_CLASS_NUMBER;
        setInputType(inputType);
        this.max = maxNum;
        this.min = minNum;
    }

    public void setTextWatcher() {
        addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (start >= 0) {//从一输入就开始判断，
                    if (min != -1 && max != -1) {
                        try {
                            if (inputType == (EditorInfo.TYPE_CLASS_NUMBER
                                    | EditorInfo.TYPE_NUMBER_FLAG_DECIMAL)) {
                                float num = Float.parseFloat(s.toString());
                                //判断当前edittext中的数字(可能一开始Edittext中有数字)是否大于max
                                if (num > max) {
                                    s = String.valueOf(max);//如果大于max，则内容为max
                                    setText(s);
                                } else if (num < min) {
                                    s = String.valueOf(min);//如果小于min,则内容为min
                                    setText(s);
                                }
                            } else if (inputType == EditorInfo.TYPE_CLASS_NUMBER) {
                                int num = Integer.parseInt(s.toString());
                                //判断当前edittext中的数字(可能一开始Edittext中有数字)是否大于max
                                if (num > max) {
                                    s = String.valueOf((int) max);//如果大于max，则内容为max
                                    setText(s);
                                } else if (num < min) {
                                    s = String.valueOf((int) min);//如果小于min,则内容为min
                                    setText(s);
                                }
                            }

                        } catch (NumberFormatException e) {
                            Log.e("ontextchanged", "==" + e.toString());
                        }
                        //edittext中的数字在max和min之间，则不做处理，正常显示即可。
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }
}
