package com.ainirobot.speechasrservice.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.ainirobot.app.MainApp;

public class SharedPreferencesUtil {
    private static final String TAG = SharedPreferencesUtil.class.getSimpleName();
    private static SharedPreferences sp;
    private static final String SHAREPRE_NAME = "speechService";
    private static final String LASTRELEASENUM = "lastreleasenum";
    private static final String AUTHORREFRESHTOKEN = "authorRefreshToken";
    private static final String AUTHORTOKEN = "authorToken";
    private static final String BUILD_TYPE = "buildType";
    private static final String NLP_RESOURCE_TYPE = "NLPResourceType";

    static {
        sp = MainApp.getInstance().getSharedPreferences(SHAREPRE_NAME, Context.MODE_PRIVATE);
    }

    public static void setReleaseNum(String releaseNum) {
        SharedPreferences.Editor edit = sp.edit();
        edit.putString(LASTRELEASENUM, releaseNum);
        edit.apply();
    }

    public static String getLastReleaseNum() {
        return sp.getString(LASTRELEASENUM, "");
    }

    public static void setAuthRefreshToken(String refreshToken, String token) {
        SharedPreferences.Editor editor = sp.edit();
        editor.putString(AUTHORREFRESHTOKEN, refreshToken);
        editor.putString(AUTHORTOKEN, token);
        editor.apply();
    }

    public static String getAuthRefreshToken() {
        return sp.getString(AUTHORREFRESHTOKEN, "");
    }

    public static String getAuthToken() {
        return sp.getString(AUTHORTOKEN, "");
    }

    public static void setBuildType(int buildType) {
        SharedPreferences.Editor edit = sp.edit();
        edit.putInt(BUILD_TYPE, buildType);
        edit.apply();
    }

    public static int getBuildType() {
        return sp.getInt(BUILD_TYPE, 0);
    }

    public static void setNlpBuildType(String buildType) {
        SharedPreferences.Editor edit = sp.edit();
        edit.putString(NLP_RESOURCE_TYPE, buildType);
        edit.apply();
    }

    public static String getNlpBuildType() {
        return sp.getString(NLP_RESOURCE_TYPE, "");
    }
}
