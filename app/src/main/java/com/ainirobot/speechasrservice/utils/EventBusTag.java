package com.ainirobot.speechasrservice.utils;

public class EventBusTag {
    public static final String SPEECHREADY = "speechReady";
    public static final String WAKEUPANGLE = "wakeupAngle";
    public static final String VOLUME = "volume";
    public static final String VADMUTETIME = "vadMuteTime";
    public static final String SPEECHRESULT = "speechResult";
    public static final String SPEECHSEMANTICSRESULTS = "speechSemanticsResults";
    public static final String SPEECHDECODERSRESULTS = "speechDecoderResults";
    public static final String SPEECHPRARESULT = "speechParResult";
    public static final String AUDIOAUTH = "audioAuth";
    public static final String SPEECHBEGIN = "speechBegin";
    public static final String SPEECHEND = "speechEnd";
    public static final String ASRRESULTERROR = "asrResultError";
    public static final String LOGPRINT = "logPrint";
    public static final String SOCKETCONNECTCHANGE = "socketConnectChange";
    public static final String SILENTTIMEOUT = "silentTimeout";
    public static final String AUDIOAUTH_RESULT = "audioAuth_result";
    public static final String ASR_FATAL_ERROR = "asr_fatal_error";
    public static final String DISABLE_REASON = "disable_reason";
}
