/*
 * Copyright (C) 2017 Orion Technology Co., Ltd. All Rights Reserved.
 */
package com.ainirobot.speechasrservice.utils;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.support.annotation.NonNull;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


public class Executors {
    private static int mThreadCount = 0;
    private static Singleton<ExecutorService>
            WORKER_EXECUTORS = new Singleton<ExecutorService>() {
        @Override
        protected ExecutorService create() {
            return new ThreadPoolExecutor(2, 4, 30,
                    TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(), new ThreadFactory() {
                @Override
                public Thread newThread(@NonNull Runnable r) {
                    Thread thread = new Thread(r);
                    thread.setName("Interaction-" + mThreadCount++);
                    return thread;
                }
            });
        }
    };

    private Executors() {
    }

    public static ExecutorService getExecutor() {
        return WORKER_EXECUTORS.get();
    }

    private static Singleton<Handler> UI_HANDLER = new Singleton<Handler>() {
        @Override
        protected Handler create() {
            return new Handler(Looper.getMainLooper());
        }
    };

    public static Handler getUiHandler() {
        return UI_HANDLER.get();
    }

    private static Singleton<Handler> BG_HANDLER = new Singleton<Handler>() {
        @Override
        protected Handler create() {
            HandlerThread handlerThread = new HandlerThread("Interaction.Bg",
                    android.os.Process.THREAD_PRIORITY_DEFAULT);
            handlerThread.start();
            return new Handler(handlerThread.getLooper());
        }
    };

    public static Handler getBgHandler() {
        return BG_HANDLER.get();
    }


}
