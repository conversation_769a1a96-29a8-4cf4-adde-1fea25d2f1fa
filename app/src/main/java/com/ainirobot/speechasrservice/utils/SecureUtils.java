package com.ainirobot.speechasrservice.utils;

import java.io.IOException;
import java.io.InputStream;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;

public class SecureUtils {
    public static SSLSocketFactory getSSLSocketFactory(InputStream clientInstream,
                                                       String clientCertPassword) {

        try {
            SSLContext sslContext = SSLContext.getInstance("TLS");
            // 2
            KeyStore keyStore = KeyStore.getInstance("pkcs12");
            keyStore.load(clientInstream, clientCertPassword.toCharArray());
            KeyManagerFactory keyManagerFactory = KeyManagerFactory
                    .getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, clientCertPassword.toCharArray());
            // 3
            sslContext.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            return sslContext.getSocketFactory();

        } catch (KeyStoreException | CertificateException | IOException | NoSuchAlgorithmException
                | KeyManagementException | UnrecoverableKeyException e) {
            throw new SecurityException(e);
        }
    }

}
