package com.ainirobot.speechasrservice.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class NetworkUtil {

    private static final String TAG = NetworkUtil.class.getSimpleName();

    //判断网络是否存在
    public static boolean isNetworkAvailable(Context context) {
        if (context == null) {
            return false;
        }
        ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivity == null) {
            return false;
        } else {
            try {
                // TODO 调用getAllNetworkInfo()在某些情况下面，系统内部会抛出空指针
                NetworkInfo[] info = connectivity.getAllNetworkInfo();
                if (info != null) {
                    for (int i = 0; i < info.length; i++) {
                        if (info[i].getState() == NetworkInfo.State.CONNECTED) {
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                try {
                    NetworkInfo activeNetInfo = connectivity.getActiveNetworkInfo();
                    if (activeNetInfo != null
                            && activeNetInfo.getType() == ConnectivityManager.TYPE_WIFI
                            && activeNetInfo.isConnected()
                            && activeNetInfo.isAvailable()) {
                        return true;
                    }
                } catch (Exception e1) {
                    e1.printStackTrace();
                }

            }
        }
        return false;
    }

    /**
     *      * 解析出url请求的ip端口或域名
     *      * @param strURL
     *      * @return
     *      
     */
    public static boolean judgeUrlIsIp(final String urlStr) {
        String regEx = "((http|ftp|https)://)" +
                "(([0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}))" +
                "(([a-zA-Z]{2,6})|(:[0-9]{1,4})?)(.*)";
        Pattern pattern = Pattern.compile(regEx);
        Matcher m = pattern.matcher(urlStr);
        return m.matches();
    }

}
