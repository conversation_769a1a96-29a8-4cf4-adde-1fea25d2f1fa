package com.ainirobot.speechasrservice.utils;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class Translate {
    public static byte[] intToByteArray(int var0, ByteOrder var1) {
        ByteBuffer var2 = ByteBuffer.allocate(4);
        var2.order(var1);
        var2.putInt(var0);
        return var2.array();
    }

    public static byte[] floatToByteArray(float var0, ByteOrder var1) {
        ByteBuffer var2 = ByteBuffer.allocate(4);
        var2.order(var1);
        var2.putFloat(var0);
        return var2.array();
    }
}
