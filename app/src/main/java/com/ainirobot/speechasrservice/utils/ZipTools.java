package com.ainirobot.speechasrservice.utils;

import android.util.Log;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

public class ZipTools {

    private static final String TAG = ZipTools.class.getSimpleName() + ":OrionUpload:";
    private static final String TEMP_FILE_SUFFIX = ".tmp";

    /**
     * @param sourceList
     * @param zipFile
     * @throws Exception
     */
    public static void zipFiles(ArrayList<File> sourceList, File zipFile, final ZipResult result)
            throws Exception {
        Log.d(TAG, "sourceList:" + sourceList + "zipFiles:" + zipFile.getAbsolutePath());
        if (sourceList != null) {
            File tempFile = new File(zipFile.getAbsolutePath() + TEMP_FILE_SUFFIX);
            if (tempFile.exists()) {
                boolean isSuc = tempFile.delete();
                if (!isSuc) {
                    Log.d(TAG, "delete file fail:" + tempFile.getAbsolutePath());
                }
            }

            if (zipFile.exists()) {
                for (File file : sourceList) {
                    zip(zipFile, file);
                }
            } else {
                ZipOutputStream zipOutStream = null;
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                    zipOutStream = new ZipOutputStream(new FileOutputStream(zipFile),
                            Charset.forName("UTF-8"));
                }
                BufferedOutputStream bufferOutStream = new BufferedOutputStream(zipOutStream);
                for (File file : sourceList) {
                    zipFile(file, zipOutStream, bufferOutStream);
                }
                //最后关闭输出流
                bufferOutStream.close();
                if (zipOutStream != null) {
                    zipOutStream.close();
                }
            }
        }
        if (result != null) {
            result.onZipResult(true, null);
        }
    }

    /**
     * @param file
     * @param zipOutStream
     * @param bufferOutStream
     * @throws IOException
     */
    private static void zipFile(File file, ZipOutputStream zipOutStream,
                                BufferedOutputStream bufferOutStream) throws IOException {
        ZipEntry entry = new ZipEntry(file.getName());
        zipOutStream.putNextEntry(entry);
        BufferedInputStream bufferInputStream = new BufferedInputStream(new FileInputStream(file));
        write(bufferInputStream, bufferOutStream);
        zipOutStream.closeEntry();
    }

    /**
     * 压缩单个文件
     *
     * @param zipFile
     * @param sourceFile
     * @throws Exception
     */
    public static void zip(File zipFile, File sourceFile) throws Exception {
        Log.d(TAG, "start zip file:" + zipFile.getAbsolutePath());
        if (zipFile.exists()) {
            File tempFile = new File(zipFile.getAbsolutePath() + TEMP_FILE_SUFFIX);
            ZipOutputStream zipOutStream = null;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                zipOutStream = new ZipOutputStream(new FileOutputStream(tempFile),
                        Charset.forName("UTF-8"));
            }
            BufferedOutputStream bufferOutStream = new BufferedOutputStream(zipOutStream);
            ZipFile zipOutFile = new ZipFile(zipFile);

            Enumeration<? extends ZipEntry> entries = zipOutFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                Log.d(TAG, "copy: " + entry.getName());
                zipOutStream.putNextEntry(entry);
                if (!entry.isDirectory()) {
                    write(zipOutFile.getInputStream(entry), bufferOutStream);
                }
                zipOutStream.closeEntry();
            }
            //记得关闭zip文件，否则后面无法删除原始文件
            zipOutFile.close();
            ZipEntry entry = new ZipEntry(sourceFile.getName());
            if (entry != null) {
                zipOutStream.putNextEntry(entry);
            }
            BufferedInputStream bufferInputStream = new BufferedInputStream(
                    new FileInputStream(sourceFile));
            write(bufferInputStream, bufferOutStream);
            bufferOutStream.close();
            zipOutStream.close();
            boolean flag = zipFile.delete();
            if (flag) {
                boolean isSuc = tempFile.renameTo(zipFile);
                if (!isSuc) {
                    Log.d(TAG, "rename file fail");
                }
            } else {
                Log.d(TAG, "delete file fail:" + zipFile.getAbsolutePath());
            }
        } else {
            ZipOutputStream zipOutStream = null;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                zipOutStream = new ZipOutputStream(new FileOutputStream(zipFile),
                        Charset.forName("UTF-8"));
            }
            BufferedOutputStream bufferOutStream = new BufferedOutputStream(zipOutStream);
            ZipEntry entry = new ZipEntry(sourceFile.getName());
            if (entry != null) {
                zipOutStream.putNextEntry(entry);
            }
            BufferedInputStream bufferInputStream = new BufferedInputStream(
                    new FileInputStream(sourceFile));
            write(bufferInputStream, bufferOutStream);
            bufferOutStream.close();
            zipOutStream.close();
        }

    }

    /**
     * 读写zip文件
     *
     * @param inputStream
     * @param outStream
     * @throws IOException
     */
    private static void write(InputStream inputStream, OutputStream outStream) throws IOException {
        byte[] data = new byte[4096];
        int length = 0;
        while ((length = inputStream.read(data)) != -1) {
            outStream.write(data, 0, length);
        }
        outStream.flush();
        inputStream.close();
    }

    /**
     * @param dirFile
     * @param zipFile
     * @throws IOException
     */
    public static void zipDirectory(File dirFile, File zipFile) throws IOException {
        if (dirFile != null && dirFile.isDirectory()) {
            if (zipFile == null) {
                zipFile = new File(dirFile.getAbsolutePath() + ".zip");
            }
            String dirName = dirFile.getName() + File.separator;
            ZipOutputStream zipOutStream = null;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                zipOutStream = new ZipOutputStream(new FileOutputStream(zipFile),
                        Charset.forName("UTF-8"));
            }
            BufferedOutputStream bufferOutStream = new BufferedOutputStream(zipOutStream);
            dealDirFile(dirFile, dirName, bufferOutStream, zipOutStream);
            bufferOutStream.close();
            if (zipOutStream != null) {
                zipOutStream.close();
            }
        } else {
            Log.d(TAG, "is not a file");
        }
    }

    /**
     * 处理目录文件
     *
     * @param dirFile
     * @param parentDir
     * @param bufferOutStream
     * @param zipOutStream
     * @throws IOException
     */
    private static void dealDirFile(File dirFile, String parentDir,
                                    BufferedOutputStream bufferOutStream,
                                    ZipOutputStream zipOutStream) throws IOException {
        File[] fileList = dirFile.listFiles();
        if (fileList == null) {
            return;
        }
        for (File file : fileList) {
            if (file.isFile()) {
                ZipEntry entry = new ZipEntry(parentDir + file.getName());
                zipOutStream.putNextEntry(entry);
                BufferedInputStream bufferInputStream = new BufferedInputStream(
                        new FileInputStream(file));
                write(bufferInputStream, bufferOutStream);
            } else {
                dealDirFile(file, parentDir + file.getName() + File.separator,
                        bufferOutStream, zipOutStream);
            }
        }
    }

    /**
     * 重载zipDirectory
     *
     * @param dirPath
     * @param zipPath
     * @throws IOException
     */
    public static void zipDirectory(String dirPath, String zipPath) throws IOException {
        if (zipPath == null || "".equals(zipPath)) {
            zipDirectory(new File(dirPath), null);
        } else {
            zipDirectory(new File(dirPath), new File(zipPath));
        }
    }
    //---------------------米特华丽的分割线---------------------

    /**
     * 解压文件
     *
     * @param zipFile
     * @param destDir
     * @throws IOException
     */
    public static void unzip(File zipFile, File destDir) throws IOException {
        ZipFile zipOutFile = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            zipOutFile = new ZipFile(zipFile, Charset.forName("gbk"));
        }
        if (zipOutFile == null) {
            return;
        }
        Enumeration<? extends ZipEntry> entries = zipOutFile.entries();
        while (entries.hasMoreElements()) {
            ZipEntry entry = entries.nextElement();
            if (entry.isDirectory()) {
                File tempFile = new File(destDir.getAbsolutePath() + File.separator
                        + entry.getName());
                if (!tempFile.exists()) {
                    boolean isSuc = tempFile.mkdirs();
                    if (!isSuc) {
                        Log.d(TAG, "create file fail");
                    }
                }
            } else {
                File tempFile = new File(destDir.getAbsolutePath() + File.separator
                        + entry.getName());
                checkParentDir(tempFile);
                FileOutputStream fileOutStream = new FileOutputStream(tempFile);
                BufferedOutputStream bufferOutStream = new BufferedOutputStream(fileOutStream);
                write(zipOutFile.getInputStream(entry), bufferOutStream);
                bufferOutStream.close();
                fileOutStream.close();
            }
        }
        zipOutFile.close();
    }

    /**
     * 验证父目录是否存在，否则创建
     *
     * @param file
     */
    private static void checkParentDir(File file) {
        if (!file.getParentFile().exists()) {
            boolean isSuc = file.getParentFile().mkdirs();
            if (!isSuc) {
                Log.d(TAG, "create file fail");
            }
        }
    }

}
