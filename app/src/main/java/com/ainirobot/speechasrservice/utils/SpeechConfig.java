/*
 *  Copyright (C) 2017 OrionStar Technology Project
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

package com.ainirobot.speechasrservice.utils;

import android.util.Log;

import com.ainirobot.coreservice.client.Definition;
import com.ainirobot.coreservice.client.robotsetting.RobotSettingApi;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;

/**
 * "配置项":{
 * "语言":"普通话",
 * "语音服务地址":"",
 * "ASR配置":"在线",
 * "TTS配置":"在线",
 * "是否启用李博士算法":true
 * }
 */

public class SpeechConfig {
    private static final String TAG = SpeechConfig.class.getSimpleName();
    public static final String CONFIG_TTS_MIX = "混合";
    public static final String CONFIG_TTS_ONLINE = "在线";
    public static final String CONFIG_TTS_OFFLINE = "本地";
    public static final String CONFIG_AUDIO_DATA_FROM_OUT = "外部";
    public static final String CONFIG_AUDIO_DATA_FROM_INNER = "内部";

    public static final String CONFIG_WAKUP_SINGLE_CNN_DNN = "单通道CNN_DNN";
    public static final String CONFIG_WAKUP_SINGLE_WAVE_NET = "单通道WaveNet";
    public static final String CONFIG_WAKUP_MULTIPLE_WAVE_NET = "多通道WaveNet";

    public static final String CONFIG_VAD_1 = "vad_1.0";
    public static final String CONFIG_VAD_2 = "vad_2.0";

    /**
     * "语言":{@link com.ainirobot.coreservice.client.speech.entity.LangParamsEnum}
     * "语音服务地址":"",
     * "ASR配置":"本地、在线、混合",
     * "TTS配置":"本地、在线、混合",
     * "是否启用李博士算法":true/false
     */

    @SerializedName("ASR配置")
    private String configAsr;
    @SerializedName("TTS配置")
    private String configTts;
    @SerializedName("是否禁用ASR")
    private boolean disableAsr;//这里禁用并不是真正意义上的禁用麦克风，因为机器人tts播放必须要求录音机打开
    @SerializedName("是否获取声音信息")
    private boolean audioInfoEnable;
    @SerializedName("音频灌入来源")
    private String audioDataFrom;
    @SerializedName("唤醒算法")
    private String wakeupAlgorithm;
    @SerializedName("vad算法")
    private String vadAlgorithm;
    @SerializedName("客户端ID")
    private String clientID;
    @SerializedName("客户端Secret")
    private String clientSecret;
    @SerializedName("客户端PID")
    private String pid;
    @SerializedName("正式语音服务地址")
    private SpeechServiceUrl releaseUrl;
    @SerializedName("正式语音服务地址分区")
    private SpeechServiceUrlZone[] releaseUrlZone;
    @SerializedName("测试语音服务地址")
    private SpeechServiceUrl testUrl;
    @SerializedName("灰度语音服务地址")
    private SpeechServiceUrl preReleaseUrl;

    //Log.WARN,Log.DEBUG,Log.VERBOSE
    @SerializedName("日志级别")
    private int logLevel;

    public String getConfigAsr() {
        return configAsr;
    }

    public void setConfigAsr(String configAsr) {
        this.configAsr = configAsr;
    }

    public String getConfigTts() {
        return configTts;
    }

    public void setConfigTts(String configTts) {
        this.configTts = configTts;
    }

    public boolean isDisableAsr() {
        return disableAsr;
    }

    public void setDisableAsr(boolean disableAsr) {
        this.disableAsr = disableAsr;
    }

    public boolean isAudioInfoEnable() {
        return audioInfoEnable;
    }

    public void setAudioInfoEnable(boolean audioInfoEnable) {
        this.audioInfoEnable = audioInfoEnable;
    }

    public String getAudioDataFrom() {
        return audioDataFrom;
    }

    public void setAudioDataFrom(String audioDataFrom) {
        this.audioDataFrom = audioDataFrom;
    }

    public String getWakeupAlgorithm() {
        return wakeupAlgorithm;
    }

    public void setWakeupAlgorithm(String wakeupAlgorithm) {
        this.wakeupAlgorithm = wakeupAlgorithm;
    }

    public String getVadAlgorithm() {
        return vadAlgorithm;
    }

    public void setVadAlgorithm(String vadAlgorithm) {
        this.vadAlgorithm = vadAlgorithm;
    }

    public String getClientID() {
        return clientID;
    }

    public void setClientID(String clientID) {
        this.clientID = clientID;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public SpeechServiceUrl getReleaseUrl() {
        return releaseUrl;
    }

    public void setReleaseUrl(SpeechServiceUrl releaseUrl) {
        this.releaseUrl = releaseUrl;
    }

    public SpeechServiceUrl getTestUrl() {
        return testUrl;
    }

    public void setTestUrl(SpeechServiceUrl testUrl) {
        this.testUrl = testUrl;
    }

    public SpeechServiceUrl getPreReleaseUrl() {
        return preReleaseUrl;
    }

    public void setPreReleaseUrl(SpeechServiceUrl preReleaseUrl) {
        this.preReleaseUrl = preReleaseUrl;
    }

    public int getLogLevel() {
        return logLevel;
    }

    public void setLogLevel(int logLevel) {
        this.logLevel = logLevel;
    }

    @Override
    public String toString() {
        return "SpeechConfig{" +
                "configAsr='" + configAsr + '\'' +
                ", configTts='" + configTts + '\'' +
                ", disableAsr=" + disableAsr +
                ", audioInfoEnable=" + audioInfoEnable +
                ", audioDataFrom='" + audioDataFrom + '\'' +
                ", wakeupAlgorithm='" + wakeupAlgorithm + '\'' +
                ", vadAlgorithm='" + vadAlgorithm + '\'' +
                ", clientID='" + clientID + '\'' +
                ", clientSecret='" + clientSecret + '\'' +
                ", pid='" + pid + '\'' +
                ", releaseUrl=" + releaseUrl +
                ", testUrl=" + testUrl +
                ", preReleaseUrl=" + preReleaseUrl +
                ", logLevel=" + logLevel +
                '}';
    }

    public static class SpeechServiceUrl {
        /**
         * 获取Token : https://robot-passport.ainirobot.com:8866/
         * 刷新Token : https://robot-passport.ainirobot.com:8888/
         * ASR : https://speech-bxm.ainirobot.com:443/
         * QueryByText : https://bxm-asr.ainirobot.com:8004/
         * TTS : https://bxm-asr.ainirobot.com:8004/
         * 心跳 : https://speech.ainirobot.com
         * 发音人列表
         */

        @SerializedName("获取Token")
        private String fetchToken;
        @SerializedName("刷新Token")
        private String refreshToken;
        private String ASR;
        private String QueryByText;
        private String TTS;
        @SerializedName("心跳")
        private String heart;
        @SerializedName("获取发音人")
        private String spokerman;
        @SerializedName("获取唤醒应答音频")
        private String wakeupAudio;
        @SerializedName("建立流式WS连接的请求地址")
        private String streamWsUrl;
        @SerializedName("建立流式SSE连接的请求地址")
        private String streamSseUrl;
        @SerializedName("agent_url")
        private String agentUrl;

        public String getFetchToken() {
            return fetchToken;
        }

        public void setFetchToken(String fetchToken) {
            this.fetchToken = fetchToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public void setRefreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
        }

        public String getASR() {
            return ASR;
        }

        public void setASR(String asr) {
            this.ASR = asr;
        }

        public String getQueryByText() {
            return QueryByText;
        }

        public void setQueryByText(String querybytext) {
            this.QueryByText = querybytext;
        }

        public String getTTS() {
            return TTS;
        }

        public void setTTS(String tts) {
            this.TTS = tts;
        }

        public String getHeart() {
            return heart;
        }

        public void setHeart(String heart) {
            this.heart = heart;
        }

        public String getSpokerman() {
            return spokerman;
        }

        public void setSpokerman(String spokerman) {
            this.spokerman = spokerman;
        }

        public String getWakeupAudio() {
            return wakeupAudio;
        }

        public void setWakeupAudio(String wakeupAudio) {
            this.wakeupAudio = wakeupAudio;
        }

        public String getStreamWsUrl() {
            return streamWsUrl;
        }

        public void setStreamWsUrl(String streamWsUrl) {
            this.streamWsUrl = streamWsUrl;
        }

        public String getStreamSseUrl() {
            return streamSseUrl;
        }

        public void setStreamSseUrl(String streamSseUrl) {
            this.streamSseUrl = streamSseUrl;
        }

        public String getAgentUrl() {
            return agentUrl;
        }

        public void setAgentUrl(String agentUrl) {
            this.agentUrl = agentUrl;
        }

        @Override
        public String toString() {
            return "SpeechServiceUrl{" +
                    "fetchToken='" + fetchToken + '\'' +
                    ", refreshToken='" + refreshToken + '\'' +
                    ", ASR='" + ASR + '\'' +
                    ", QueryByText='" + QueryByText + '\'' +
                    ", TTS='" + TTS + '\'' +
                    ", heart='" + heart + '\'' +
                    ", spokerman='" + spokerman + '\'' +
                    ", wakeupAudio='" + wakeupAudio + '\'' +
                    ", streamWsUrl='" + streamWsUrl + '\'' +
                    ", streamSseUrl='" + streamSseUrl + '\'' +
                    ", agentUrl='" + agentUrl + '\'' +
                    '}';
        }
    }

    /**
     * 域名分区设置
     */
    public static class SpeechServiceUrlZone {
        /**
         * 服务器区位: default,
         * 获取Token : https://robot-passport.ainirobot.com:8866/
         * 刷新Token : https://robot-passport.ainirobot.com:8888/
         * ASR : https://speech-bxm.ainirobot.com:443/
         * QueryByText : https://bxm-asr.ainirobot.com:8004/
         * TTS : https://bxm-asr.ainirobot.com:8004/
         * 心跳 : https://speech.ainirobot.com
         * 发音人列表
         */
        @SerializedName("服务器区位")
        private String zone;
        @SerializedName("获取Token")
        private String fetchToken;
        @SerializedName("刷新Token")
        private String refreshToken;
        private String ASR;
        private String QueryByText;
        private String TTS;
        @SerializedName("心跳")
        private String heart;
        @SerializedName("获取发音人")
        private String spokerman;
        @SerializedName("获取唤醒应答音频")
        private String wakeupAudio;
        @SerializedName("建立流式WS连接的请求地址")
        private String streamWsUrl;
        @SerializedName("建立流式SSE连接的请求地址")
        private String streamSseUrl;
        @SerializedName("agent_url")
        private String agentUrl;

        public String getZone() {
            return zone;
        }

        public void setZone(String zone) {
            this.zone = zone;
        }

        public String getFetchToken() {
            return fetchToken;
        }

        public void setFetchToken(String fetchToken) {
            this.fetchToken = fetchToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public void setRefreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
        }

        public String getASR() {
            return ASR;
        }

        public void setASR(String asr) {
            this.ASR = asr;
        }

        public String getQueryByText() {
            return QueryByText;
        }

        public void setQueryByText(String querybytext) {
            this.QueryByText = querybytext;
        }

        public String getTTS() {
            return TTS;
        }

        public void setTTS(String tts) {
            this.TTS = tts;
        }

        public String getHeart() {
            return heart;
        }

        public void setHeart(String heart) {
            this.heart = heart;
        }

        public String getSpokerman() {
            return spokerman;
        }

        public void setSpokerman(String spokerman) {
            this.spokerman = spokerman;
        }

        public String getWakeupAudio() {
            return wakeupAudio;
        }

        public void setWakeupAudio(String wakeupAudio) {
            this.wakeupAudio = wakeupAudio;
        }

        public String getStreamWsUrl() {
            return streamWsUrl;
        }

        public void setStreamWsUrl(String streamWsUrl) {
            this.streamWsUrl = streamWsUrl;
        }

        public String getStreamSseUrl() {
            return streamSseUrl;
        }

        public void setStreamSseUrl(String streamSseUrl) {
            this.streamSseUrl = streamSseUrl;
        }

        public String getAgentUrl() {
            return agentUrl;
        }

        public void setAgentUrl(String agentUrl) {
            this.agentUrl = agentUrl;
        }

        @Override
        public String toString() {
            return "SpeechServiceUrl{" +
                    "fetchToken='" + fetchToken + '\'' +
                    ", refreshToken='" + refreshToken + '\'' +
                    ", ASR='" + ASR + '\'' +
                    ", QueryByText='" + QueryByText + '\'' +
                    ", TTS='" + TTS + '\'' +
                    ", heart='" + heart + '\'' +
                    ", spokerman='" + spokerman + '\'' +
                    ", streamWsUrl='" + streamWsUrl + '\'' +
                    ", streamSseUrl='" + streamSseUrl + '\'' +
                    ", zone='" + zone + '\'' +
                    ", agentUrl='" + agentUrl + '\'' +
                    '}';
        }
    }

    /**
     * 处理正式服务器分区数据
     */
    public void processReleaseServerZoneData(){

        String zone = RobotSettingApi.getInstance().getRobotString(Definition.ROBOT_SETTINGS_CLOUD_SERVER_ZONE);
        String zoneDefault = Definition.CloudServerZone.DEFAULT.getValue();
        SpeechServiceUrlZone serverZone;

        Log.d(TAG,"processServerZoneData zone: " + zone);

        if((this.releaseUrlZone != null) && (this.releaseUrlZone.length > 0)){
            serverZone = this.getServerZone(zone);
            if(serverZone == null){
                serverZone = this.getServerZone(zoneDefault);
            }
            Log.d(TAG,"getServerZoneConfig: " + new Gson().toJson(serverZone));
            this.copyReleaseServerZoneToServer(serverZone);
        }

    }

    private void copyReleaseServerZoneToServer(SpeechServiceUrlZone serverZone){
        if(serverZone != null){
            this.releaseUrl.ASR = serverZone.ASR;
            this.releaseUrl.fetchToken = serverZone.fetchToken;
            this.releaseUrl.heart =serverZone.heart;
            this.releaseUrl.refreshToken = serverZone.refreshToken;
            this.releaseUrl.QueryByText = serverZone.QueryByText;
            this.releaseUrl.spokerman = serverZone.spokerman;
            this.releaseUrl.TTS = serverZone.TTS;
            this.releaseUrl.wakeupAudio = serverZone.wakeupAudio;
            this.releaseUrl.streamWsUrl = serverZone.streamWsUrl;
            this.releaseUrl.streamSseUrl = serverZone.streamSseUrl;
            this.releaseUrl.agentUrl = serverZone.agentUrl;
        }
    }

    /**
     * 根据分区获取服务器分区配置
     * @param zone
     * @return
     */
    private SpeechServiceUrlZone getServerZone(String zone){
        if((this.releaseUrlZone != null) && (this.releaseUrlZone.length > 0)){
            for(int i=0;i<this.releaseUrlZone.length;i++){
                if(this.releaseUrlZone[i].zone.equals(zone)){
                    return this.releaseUrlZone[i];
                }
            }
        }
        return null;
    }

}
