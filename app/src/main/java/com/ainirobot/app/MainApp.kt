package com.ainirobot.app

import android.app.Application
import android.content.Context
import android.content.Intent
import android.util.Log
import com.ainirobot.base.analytics.utils.StringUtil.ProcessHelper
import com.ainirobot.common.Config
import com.ainirobot.common.CrashCatcher
import com.ainirobot.common.utils.KLog
import com.ainirobot.coreservice.client.upload.bi.BiReport
import com.ainirobot.daemon.AgentDaemonService
import com.tencent.mmkv.MMKV

class MainApp : Application() {

    companion object {
        @JvmStatic
        private var mInstance: MainApp? = null

        @JvmStatic
        fun getInstance(): MainApp? {
            return mInstance
        }

        private fun setContext(context: Context) {
            mInstance = context as MainApp
        }
    }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        setContext(this)
    }

    override fun onCreate() {
        super.onCreate()

        if (!isMainProcess()) {
            return
        }

        // 检查资源是否正常初始化
        if (resources == null) {
            throw IllegalStateException("Resources not initialized properly")
        }

        Config.initialize(this.applicationContext)

        if (Config.releaseMode) {
            CrashCatcher.initialize()
        }

        KLog.initialize()
        MMKV.initialize(this)

        if (ProcessHelper.getProcessName() == this.packageName) {
            BiReport.init()
        }

        startDaemonService()
    }

    private fun isMainProcess(): Boolean {
        return ProcessHelper.getProcessName() == this.packageName
    }

    private fun startDaemonService() {
        Log.d("MainApp", "startAgentDaemonService")
        val intent = Intent(this, AgentDaemonService::class.java)
        startService(intent)
    }
}