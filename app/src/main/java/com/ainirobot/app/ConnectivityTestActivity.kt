package com.ainirobot.app

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ainirobot.agent.R
import com.ainirobot.common.debug.ConnectivityHelper
import com.ainirobot.common.debug.TestMode
import com.ainirobot.common.debug.TestPrinter
import com.ainirobot.common.utils.DateUtil.dateFormat

class ConnectivityTestActivity : AppCompatActivity(), TestPrinter {

    data class LogItem(
        val timestamp: String,
        val content: String
    )

    private class LogAdapter : RecyclerView.Adapter<LogAdapter.LogViewHolder>() {

        private val logList = mutableListOf<LogItem>()
        private val maxLogCount = 5000

        @SuppressLint("NotifyDataSetChanged")
        fun addLog(logItem: LogItem) {
            // 如果超过最大条数，清空列表
            if (logList.size >= maxLogCount) {
                logList.clear()
                notifyDataSetChanged()
            }

            val text = logList.lastOrNull()?.content
            if (text != null && logItem.content.startsWith(text)) {
                logList[logList.size - 1] = logItem
                notifyItemChanged(logList.size - 1)
                return
            }
            logList.add(logItem)
            notifyItemInserted(logList.size - 1)
        }

        @SuppressLint("NotifyDataSetChanged")
        fun clearLogs() {
            logList.clear()
            notifyDataSetChanged()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LogViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_log, parent, false)
            return LogViewHolder(view)
        }

        override fun onBindViewHolder(holder: LogViewHolder, position: Int) {
            val logItem = logList[position]
            holder.bind(logItem)
        }

        override fun getItemCount(): Int = logList.size

        class LogViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            private val tvTimestamp: TextView = itemView.findViewById(R.id.tvTimestamp)
            private val tvLogContent: TextView = itemView.findViewById(R.id.tvLogContent)

            fun bind(logItem: LogItem) {
                tvTimestamp.text = logItem.timestamp
                tvLogContent.text = logItem.content
            }
        }
    }

    private lateinit var radioGroup: RadioGroup
    private lateinit var radioMessage: RadioButton
    private lateinit var radioTTS: RadioButton
    private lateinit var radioASR: RadioButton
    private lateinit var btnStartStop: TextView
    private lateinit var btnClearLog: TextView
    private lateinit var recyclerViewLog: RecyclerView
    private lateinit var logAdapter: LogAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_connectivity_test)

        initViews()
        setupRecyclerView()
        setupListeners()
    }

    private fun initViews() {
        radioGroup = findViewById(R.id.radioGroup)
        radioMessage = findViewById(R.id.radioMessage)
        radioTTS = findViewById(R.id.radioTTS)
        radioASR = findViewById(R.id.radioASR)
        btnStartStop = findViewById(R.id.btnStartStop)
        btnClearLog = findViewById(R.id.btnClearLog)
        recyclerViewLog = findViewById(R.id.recyclerViewLog)
    }

    private fun setupRecyclerView() {
        logAdapter = LogAdapter()
        recyclerViewLog.apply {
            layoutManager = LinearLayoutManager(this@ConnectivityTestActivity)
            adapter = logAdapter
        }
    }

    private fun setupListeners() {
        findViewById<TextView>(R.id.back_btn).setOnClickListener {
            finish()
        }

        // 单选按钮监听
        radioGroup.setOnCheckedChangeListener { _, checkedId ->
            ConnectivityHelper.mode = when (checkedId) {
                R.id.radioMessage -> {
                    TestMode.MESSAGE
                }
                R.id.radioTTS -> {
                    TestMode.TTS
                }
                R.id.radioASR -> {
                    TestMode.ASR
                }
                else -> TestMode.MESSAGE
            }
        }

        // 开始/停止按钮监听
        btnStartStop.setOnClickListener {
            if (ConnectivityHelper.isStop) {
                startTest()
            } else {
                stopTest()
            }
        }

        // 清空日志按钮监听
        btnClearLog.setOnClickListener {
            logAdapter.clearLogs()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun startTest() {
        ConnectivityHelper.start()
        btnStartStop.text = "STOP"
        btnStartStop.setBackgroundResource(R.drawable.button_stop_background)
    }

    @SuppressLint("SetTextI18n")
    private fun stopTest() {
        ConnectivityHelper.stop()
        btnStartStop.text = "START"
        btnStartStop.setBackgroundResource(R.drawable.button_start_background)
    }

    private fun addLog(content: String) {
        logAdapter.addLog(
            LogItem(
                System.currentTimeMillis().dateFormat("yyyy-MM-dd HH:mm:ss.SSS"),
                content
            )
        )
        // 自动滚动到底部
        recyclerViewLog.scrollToPosition(logAdapter.itemCount - 1)
    }

    override fun onPrint(content: String) {
        runOnUiThread {
            addLog(content)
        }
    }

    override fun onStart() {
        super.onStart()
        ConnectivityHelper.printer = this
    }

    override fun onStop() {
        ConnectivityHelper.printer = null
        stopTest()
        super.onStop()
    }
}