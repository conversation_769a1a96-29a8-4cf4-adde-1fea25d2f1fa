package com.ainirobot.app

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.os.Bundle
import android.widget.Button
import android.widget.RadioGroup
import android.widget.TextView
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.ainirobot.agent.BuildConfig
import com.ainirobot.agent.R
import com.ainirobot.agent.service.RobotStatus
import com.ainirobot.common.Config
import com.ainirobot.common.DataStore
import com.ainirobot.common.audio.PCMAudioWriter
import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.common.utils.KLog
import com.lzf.easyfloat.interfaces.OnPermissionResult
import com.lzf.easyfloat.permission.PermissionUtils


class MainActivity : AppCompatActivity() {

    companion object {

        private const val THOUGHT_MODE_SIMPLE = "turbo"

        private const val THOUGHT_MODE_COMPLEX = "full_power"

    }

    private var consoleBtn: Button? = null
    private var recordBtn: Button? = null
    private var thoughtModeRadioGroup: RadioGroup? = null

    private var clickCount = 0
    private var lastClickTime = 0L

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        consoleBtn = findViewById<Button>(R.id.show_console_bt).also {
            it.setOnClickListener {
                RobotConsole.show()
            }
        }

        findViewById<Button>(R.id.back_btn).setOnClickListener {
            finish()
        }
        findViewById<Button>(R.id.test_btn).setOnClickListener {
            startActivity(Intent(this, ConnectivityTestActivity::class.java))
        }
        findViewById<Button>(R.id.restart_btn).setOnClickListener {
            android.os.Process.killProcess(android.os.Process.myPid())
        }

        findViewById<Button>(R.id.record_audio_btn).let {
            recordBtn = it
            updateRecordBtn()
            it.setOnClickListener {
                if (PCMAudioWriter.isRecording) {
                    PCMAudioWriter.stopRecord()
                } else {
                    PCMAudioWriter.startRecord()
                }
                updateRecordBtn()
            }
        }

        findViewById<TextView>(R.id.app_version_view).let {
            it.text = "P: v${BuildConfig.PRODUCT_VERSION}\nC: v${BuildConfig.VERSION_NAME}\nA: ${Config.actionVersion}"
            it.setOnClickListener { _ ->
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastClickTime > 500) { // 如果两次点击间隔超过500ms，重置计数
                    KLog.d("clickCount reset", "MainActivity")
                    clickCount = 1
                } else {
                    clickCount++
                    KLog.d("clickCount: $clickCount", "MainActivity")
                    if (clickCount >= 8) {
                        Config.debugMode = !Config.debugMode
                        updateDebugView()
                        if (Config.debugMode) {
                            Toast.makeText(this, R.string.debug_mode_opened, Toast.LENGTH_SHORT).show()
                            RobotConsole.show()
                        } else {
                            Toast.makeText(this, R.string.debug_mode_closed, Toast.LENGTH_SHORT).show()
                            RobotConsole.dismiss()
                        }
                        clickCount = 0
                    }
                }
                lastClickTime = currentTime
            }
        }
        findViewById<RadioGroup>(R.id.thought_mode_radio_group)?.let {
            thoughtModeRadioGroup = it
            updateThoughtMode()
            it.setOnCheckedChangeListener { _, checkedId ->
                when (checkedId) {
                    R.id.thought_mode_simple -> {
                        DataStore.agentThoughtMode = THOUGHT_MODE_SIMPLE
                    }
                    R.id.thought_mode_complex -> {
                        DataStore.agentThoughtMode = THOUGHT_MODE_COMPLEX
                    }
                }
                RobotStatus.report(
                    mapOf(RobotStatus.KEY_AGENT_MODE to DataStore.agentThoughtMode)
                )
            }
        }

        // 申请悬浮窗权限
        if (!PermissionUtils.checkPermission(this)) {
            PermissionUtils.requestPermission(this, object : OnPermissionResult {
                override fun permissionResult(isOpen: Boolean) {
                    Toast.makeText(this@MainActivity, "悬浮窗权限开启: $isOpen", Toast.LENGTH_SHORT).show()
                }
            })
        }
        requestNeededPermissions {}

        updateDebugView()
    }

    private fun updateDebugView() {
        if (Config.debugMode) {
            consoleBtn?.visibility = Button.VISIBLE
            thoughtModeRadioGroup?.visibility = Button.VISIBLE
        } else {
            consoleBtn?.visibility = Button.GONE
            thoughtModeRadioGroup?.visibility = Button.GONE
        }
    }

    private fun updateThoughtMode() {
        when (DataStore.agentThoughtMode) {
            THOUGHT_MODE_SIMPLE -> {
                thoughtModeRadioGroup?.check(R.id.thought_mode_simple)
            }
            THOUGHT_MODE_COMPLEX -> {
                thoughtModeRadioGroup?.check(R.id.thought_mode_complex)
            }
        }
    }

    private fun updateRecordBtn() {
        if (PCMAudioWriter.isRecording) {
            recordBtn?.setTextColor(Color.RED)
            recordBtn?.setText(R.string.stop_record_audio)
        } else {
            recordBtn?.setTextColor(Color.WHITE)
            recordBtn?.setText(R.string.start_record_audio)
        }
    }

    private fun requestNeededPermissions(onHasPermissions: () -> Unit) {
        // 申请动态权限
        val requestPermissionLauncher =
            registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { grants ->
                var hasDenied = false
                // Check if any permissions weren't granted.
                for (grant in grants.entries) {
                    if (!grant.value) {
                        Toast.makeText(this, "Missing permission: ${grant.key}", Toast.LENGTH_SHORT).show()
                        hasDenied = true
                    }
                }

                if (!hasDenied) {
                    onHasPermissions()
                }
            }

        // Assemble the needed permissions to request
        val neededPermissions = listOf(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.CAMERA,
//            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
            .filter { ContextCompat.checkSelfPermission(this, it) == PackageManager.PERMISSION_DENIED }
            .toTypedArray()

        if (neededPermissions.isNotEmpty()) {
            requestPermissionLauncher.launch(neededPermissions)
        } else {
            onHasPermissions()
        }
    }
}