package com.ainirobot.bridge.skill

import com.ainirobot.coreservice.listener.IMusicListener
import com.ainirobot.coreservice.listener.ITextListener
import com.ainirobot.coreservice.listener.IToneListener

interface ISkillServer {

    fun playText(text: String, iTextListener: ITextListener?)

    fun stopTTS()

    fun onForeground(appId: String)

    fun onBackground(appId: String)

    fun onPageChanged(appId: String, pageId: String)

    fun queryByText(text: String)

    fun sendAgentMessage(type: String?, code: Int, message: String?)

    fun onAgentActionFinish(action: String, code: Int, message: String)

    fun onAgentActionState(action: String, state: Int, data: String?)

    fun playToneByLocalPath(localPath: String?,iToneListener: IToneListener?)

    fun playMusicByLocalPath(
        localPath: String?, looping: Boolean, enableAudioFocus: <PERSON><PERSON>an,
        iMusicListener: IMusicListener?
    )

    fun stopMusicPlay()

    fun queryByTextWithThinking(text: String, isShowThinking: Boolean)
}