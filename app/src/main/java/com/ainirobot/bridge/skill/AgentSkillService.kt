package com.ainirobot.bridge.skill

import android.graphics.Color
import android.os.Build
import androidx.annotation.RequiresApi
import com.ainirobot.agent.action.ActionCompletion
import com.ainirobot.agent.action.ExecutionSide
import com.ainirobot.agent.core.AgentClient
import com.ainirobot.agent.core.AudioInfo
import com.ainirobot.agent.core.CommonMessage
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.agent.core.SpeechPlayer
import com.ainirobot.agent.service.RobotStatus
import com.ainirobot.bridge.executor.RobotActionSender
import com.ainirobot.common.Config
import com.ainirobot.common.Config.gson
import com.ainirobot.common.Config.ioScope
import com.ainirobot.common.DataStore
import com.ainirobot.common.debug.CaptionWindow
import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.MessageSenderUtil
import com.ainirobot.coreservice.listener.IMusicListener
import com.ainirobot.coreservice.listener.ITextListener
import com.ainirobot.coreservice.listener.IToneListener
import com.ainirobot.features.web.WebFeature
import com.ainirobot.speechasrservice.skill.VolumeServer
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.launch
import kotlinx.coroutines.time.delay
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import java.time.Duration

enum class MediaPageStatus {
    ENTER,
    EXIT
}

class AgentSkillService(
    private val agentClient: AgentClient
) : ISkillServer {

    private data class AudioStream(
        @SerializedName("messages")
        val messages: Array<Map<String, *>>,
        @SerializedName("llm_config")
        val llmConfig: Map<String, *>
    ) {

        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as AudioStream

            if (!messages.contentEquals(other.messages)) return false
            if (llmConfig != other.llmConfig) return false

            return true
        }

        override fun hashCode(): Int {
            var result = messages.contentHashCode()
            result = 31 * result + llmConfig.hashCode()
            return result
        }
    }

    override fun playText(text: String, iTextListener: ITextListener?) {
        KLog.d("PlayTTS: $text", "AgentSkillServer")
        val textValue = gson.fromJson<Map<String, String>>(
            text,
            object : TypeToken<Map<String, String>>() {}.type
        )["text"]
        if (textValue.isNullOrEmpty()) {
            KLog.d("PlayTTS: textValue is null, $text", "AgentSkillServer")
            iTextListener?.onError()
            return
        }

        iTextListener?.onStart()
        RobotConsole.print("PlayTTS Start: $textValue")
        ioScope.launch {
            CaptionWindow.clearAiCaption()
            val result = if (textValue.startsWith("@{")) {
                val realValue = textValue.substring(1, textValue.length - 1)
                KLog.d("REALTIME_SAY: $realValue", "AgentSkillServer")
                val audioResult = gson.fromJson(
                    realValue,
                    AudioStream::class.java
                )
                SpeechPlayer.playSync(
                    AudioInfo(
                        isStream = true,
                        streamConfig = mapOf(
                            "messages" to audioResult.messages,
                            "llm_config" to audioResult.llmConfig
                        ),
                        timeoutMillis = 30000
                    )
                )
            } else {
                val timeoutMillis = Config.calculateTtsDuration(text)
                KLog.d("TTS_PLAY, timeoutMillis: $timeoutMillis", "AgentSkillServer")
                SpeechPlayer.playSync(
                    AudioInfo(
                        isStream = textValue.startsWith("@{"),
                        text = textValue,
                        timeoutMillis = timeoutMillis
                    )
                )
            }
            if (result == null) {
                iTextListener?.onError()
                KLog.d("PlayTTS failed: $textValue", "AgentSkillServer")
                RobotConsole.print("PlayTTS failed: $textValue, Reason: timeout", Color.RED)
            } else if (result.status) {
                iTextListener?.onComplete()
                KLog.d("PlayTTS Success: $textValue", "AgentSkillServer")
                RobotConsole.print("PlayTTS Success: $textValue")
            } else {
                iTextListener?.onStop()
                KLog.d("PlayTTS stop: $textValue", "AgentSkillServer")
                RobotConsole.print(
                    "PlayTTS stop: $textValue, Reason: maybe interrupted",
                    Color.YELLOW
                )
            }
        }
    }

    override fun stopTTS() {
        SpeechPlayer.stop()
    }

    override fun playToneByLocalPath(localPath: String?, iToneListener: IToneListener?) {
        VolumeServer.getInstance().toneToSpeechByLocalPath(localPath, iToneListener)
    }

    override fun playMusicByLocalPath(
        localPath: String?,
        looping: Boolean,
        enableAudioFocus: Boolean,
        iMusicListener: IMusicListener?
    ) {
        VolumeServer.getInstance()
            .playMusicByLocalPath(localPath, looping, enableAudioFocus, iMusicListener)
    }

    override fun stopMusicPlay() {
        VolumeServer.getInstance().stopMusicPlay()
    }

    override fun onForeground(appId: String) {
        WebFeature.closeWeb(false)
        if (DataStore.currentAppId != appId) {
            EventBus.getDefault().post(MediaPageStatus.EXIT)
        }

        DataStore.currentAppId = appId
        DataStore.currentPageId = ""
        MessageSender.sendMessage(
            CommonMessage(
                name = "state.robot_state",
                content = mapOf(
                    "interface_state" to mapOf(
                        "app_id" to DataStore.currentAppId,
                        "page_id" to ""
                    )
                )
            )
        )
        RobotConsole.print("onForeground: $appId")
    }

    override fun onBackground(appId: String) {}

    override fun onPageChanged(appId: String, pageId: String) {
        DataStore.currentAppId = appId
        DataStore.currentPageId = pageId
        MessageSender.sendMessage(
            CommonMessage(
                name = "state.robot_state",
                content = mapOf(
                    "interface_state" to mapOf(
                        "app_id" to appId,
                        "page_id" to pageId
                    )
                )
            )
        )
        RobotConsole.print("onPageChanged: $appId, $pageId")
    }

    override fun queryByText(text: String) {
        KLog.d("QueryByText: $text", "AgentSkillServer")
        CaptionWindow.clearAiCaption()
        CaptionWindow.clearUserCaption()
        MessageSender.sendMessage(
            msgType = "plan.plan",
            content = mapOf("query" to text),
            isSafety = false
        )
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun queryByTextWithThinking(text: String, isShowThinking: Boolean) {
        KLog.d("queryByTextWithThinking: $text", "AgentSkillServer")
        CaptionWindow.clearAiCaption()
        if (isShowThinking) {
            CaptionWindow.queryByTextThinking(text)
        } else {
            CaptionWindow.clearUserCaption()
        }
        ioScope.launch {
            delay(Duration.ofSeconds(1))
            MessageSender.sendMessage(
                msgType = "plan.plan",
                content = mapOf("query" to text),
                isSafety = false
            )
        }
    }

    private data class DataMessage(
        @SerializedName("id")
        val id: String,
        @SerializedName("type")
        val type: String,
        @SerializedName("timestamp")
        val timestamp: Long,
        @SerializedName("data")
        val data: Map<String, Any>? = null
    )

    override fun sendAgentMessage(type: String?, code: Int, message: String?) {
        KLog.d("sendAgentMessage, type: $type, message: $message", "AgentSkillServer")
        if (type.isNullOrEmpty()) {
            return
        }
        when (type) {
            "agent_data_message" -> {
                val dataMessage = gson.fromJson(
                    message,
                    DataMessage::class.java
                )
                ioScope.launch {
                    handleAgentDataMessage(dataMessage)
                }
            }

            else -> {
                MessageSenderUtil.sendAgentMessage(type, message)
            }
        }
    }

    private suspend fun handleAgentDataMessage(message: DataMessage) {
        when (message.type) {
            "action_status" -> {
                val data = message.data ?: return
                RobotActionSender.onReceiveResponse(
                    data["sid"] as String,
                    data["status"] as String,
                    null
                )
            }

            "execute_action" -> {
                val data = message.data ?: return
                val action = data["action"] as String
                val params = data["params"] as Map<String, *>
                agentClient.executeAction(
                    actionName = action,
                    executionSide = ExecutionSide.BOTH,
                    timeout = 30,
                    params = params
                )
            }

            "media_page_status" -> {
                val data = message.data ?: return
                when (data["status"]) {
                    "enter" -> {
                        EventBus.getDefault().post(MediaPageStatus.ENTER)
                    }

                    "exit" -> {
                        EventBus.getDefault().post(MediaPageStatus.EXIT)
                    }
                }
            }

            "robot_agent_character_info" -> {
                val data = message.data ?: return
                val jsonObject = JSONObject(data)
                KLog.d("robot_agent_character_info====: $data", "AgentSkillServer")

                // 清空相关的profile数据
                DataStore.agentProfileDevice = ""
                DataStore.agentProfileEnterprise = ""

                val characterInfo = mapOf(
                    "project_name" to jsonObject.optString("project_name", ""),
                    "company_profile" to jsonObject.optString("company_profile", ""),
                    "role" to jsonObject.optString("role", ""),
                    "nickname" to jsonObject.optString("nickname", ""),
                    "role_name" to jsonObject.optString("role_name", ""),
                    "character_info" to jsonObject.optString("character_info", ""),
                    "corp_desc" to jsonObject.optString("corp_desc", "")
                )
                MessageSender.sendMessage(
                    msgType = "state.robot_state",
                    content = mapOf(
                        "character_state" to characterInfo
                    ),
                    isSafety = true
                )
                DataStore.agentProfileInfo = gson.toJson(characterInfo)
            }

            "robot_agent_character_profile" -> {
                val data = message.data ?: return
                // 清空角色信息数据
                DataStore.agentProfileInfo = ""
                val type = data["type"] as String
                val profile = data["profile"] as Map<*, *>

                if (profile.containsKey("query_clarification_enabled")) {
                    DataStore.turnOnClarify = (profile["query_clarification_enabled"] == 1.0)
                }

                if (profile.containsKey("task_reconfirmation_enabled")) {
                    DataStore.turnOnConfirm = (profile["task_reconfirmation_enabled"] == 1.0)
                }

                if (type == "device") {
                    val deviceProfile = gson.toJson(profile)
                    if (deviceProfile != DataStore.agentProfileDevice) {
                        if (profile.isEmpty()) {
                            if (DataStore.agentProfileDevice.isNotEmpty()
                                && DataStore.agentProfileEnterprise.isNotEmpty()
                            ) {
                                val enterpriseProfile: Map<String, Any> = gson.fromJson(
                                        DataStore.agentProfileEnterprise,
                                        object : TypeToken<Map<String, Any>>() {}.type
                                    )

                                if (enterpriseProfile.containsKey("query_clarification_enabled")) {
                                    DataStore.turnOnClarify = (enterpriseProfile["query_clarification_enabled"] == 1.0)
                                }

                                if (enterpriseProfile.containsKey("task_reconfirmation_enabled")) {
                                    DataStore.turnOnConfirm = (enterpriseProfile["task_reconfirmation_enabled"] == 1.0)
                                }

                                MessageSender.sendMessage(
                                    msgType = "state.robot_state",
                                    content = mapOf(
                                        "character_state" to enterpriseProfile,
                                        RobotStatus.KEY_TURN_ON_CLARIFY to DataStore.turnOnClarify,
                                        RobotStatus.KEY_TURN_ON_CONFIRM to DataStore.turnOnConfirm
                                    ),
                                    isSafety = true
                                )
                                DataStore.agentProfileDevice = ""
                            }
                        } else {
                            MessageSender.sendMessage(
                                msgType = "state.robot_state",
                                content = mapOf(
                                    "character_state" to profile,
                                    RobotStatus.KEY_TURN_ON_CLARIFY to DataStore.turnOnClarify,
                                    RobotStatus.KEY_TURN_ON_CONFIRM to DataStore.turnOnConfirm
                                ),
                                isSafety = true
                            )
                            DataStore.agentProfileDevice = deviceProfile
                            KLog.d("agentProfileDevice set: $deviceProfile", "AgentSkillService")
                        }
                    }
                } else {
                    val enterpriseProfile = gson.toJson(profile)
                    if (enterpriseProfile != DataStore.agentProfileEnterprise) {
                        if (profile.isNotEmpty()) {
                            KLog.d(
                                "agentProfileDevice get: ${DataStore.agentProfileDevice}",
                                "AgentSkillService"
                            )
                            if (DataStore.agentProfileDevice.isEmpty()) {
                                MessageSender.sendMessage(
                                    msgType = "state.robot_state",
                                    content = mapOf(
                                        "character_state" to profile,
                                        RobotStatus.KEY_TURN_ON_CLARIFY to DataStore.turnOnClarify,
                                        RobotStatus.KEY_TURN_ON_CONFIRM to DataStore.turnOnConfirm
                                    ),
                                    isSafety = true
                                )
                            }
                            DataStore.agentProfileEnterprise = enterpriseProfile
                        }
                    }
                }
            }

            else -> {
                MessageSender.sendMessage(
                    msgType = message.type,
                    content = message.data ?: emptyMap(),
                    isSafety = false
                )
            }
        }
    }

    override fun onAgentActionFinish(action: String, code: Int, message: String) {
        ioScope.launch {
            RobotActionSender.onReceiveResponse(
                action,
                if (code == 1) ActionCompletion.SUCCESS else ActionCompletion.FAILED,
                message
            )
        }
        KLog.d(
            "onAgentActionFinish action: $action, code: $code, message: $message",
            "SpeechServiceImpl"
        )
    }

    override fun onAgentActionState(action: String, state: Int, data: String?) {
        KLog.d(
            "onAgentActionState action: $action, state: $state, data: $data",
            "SpeechServiceImpl"
        )
    }
}