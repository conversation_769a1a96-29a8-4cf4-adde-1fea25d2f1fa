/*
 *     Copyright (C) 2017 OrionStar Technology Project
 *
 *     Licensed under the Apache License, Version 2.0 (the "License");
 *     you may not use this file except in compliance with the License.
 *     You may obtain a copy of the License at
 *
 *          http://www.apache.org/licenses/LICENSE-2.0
 *
 *     Unless required by applicable law or agreed to in writing, software
 *     distributed under the License is distributed on an "AS IS" BASIS,
 *     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     See the License for the specific language governing permissions and
 *     limitations under the License.
 */
package com.ainirobot.bridge.skill;

import android.content.Intent;
import android.os.RemoteException;
import android.util.Log;

import com.ainirobot.common.utils.KLog;
import com.ainirobot.coreservice.ISkill;
import com.ainirobot.coreservice.ISkillCallback;
import com.ainirobot.coreservice.ISkillServerCheckListener;
import com.ainirobot.coreservice.client.speech.entity.ASRParams;
import com.ainirobot.coreservice.config.SceneEntity;
import com.ainirobot.coreservice.listener.IMusicListener;
import com.ainirobot.coreservice.listener.ITextListener;
import com.ainirobot.coreservice.listener.IToneListener;
import com.ainirobot.speechasrservice.skill.SkillManager;

import java.util.List;
import java.util.Map;

/**
 * SkillServerProxy is proxy for SkillServer, concrete implementation please see {SkillServer}.
 *
 * <AUTHOR>
 * @date 2018-11-20
 */
public final class SkillServiceImpl extends ISkill.Stub {

    private static final String ACTION_NLP_STATE_CHANGED = "com.ainirobot.action.nlp_state_changed";
    private static final String KEY_NLP_STATE = "data_nlp_state";

    private static final String TAG = "SkillServerProxy";

    private final SpeechClient mSpeechClient;

    public SkillServiceImpl(SpeechClient mSpeechClient) {
        this.mSpeechClient = mSpeechClient;
    }

    @Override
    public void registerCallBack(ISkillCallback iSkillCallback) {
        Log.d(TAG, "registerCallBack");
        mSpeechClient.setSkillCallback(iSkillCallback);
    }

    @Override
    public void unregisterCallBack(ISkillCallback iSkillCallback) {
        // do not need to unregister
    }

    @Override
    public void playText(String text, ITextListener iTextListener) {
        Log.d(TAG, "play Text " + text);
        mSpeechClient.getSkillServer().playText(text, iTextListener);
    }

    @Deprecated
    @Override
    public void playTone(String toneType, IToneListener iToneListener) {
        Log.e(TAG, "play tone api has deprecated, plaease use playToneByLocalPath" + toneType);
    }

    @Override
    public void playToneByLocalPath(String localPath, IToneListener iToneListener) {
        Log.d(TAG, "play local tone " + localPath);
        mSpeechClient.getSkillServer().playToneByLocalPath(localPath, iToneListener);
    }

    @Override
    public void playMusicByLocalPath(String localPath, boolean looping, boolean enableAudioFocus,
                                     IMusicListener iMusicListener) {
        Log.d(TAG, "Agent play local music localPath = " + localPath + ",looping = " + looping
                + ",enableAudioFocus = " + enableAudioFocus);
        mSpeechClient.getSkillServer().playMusicByLocalPath(localPath, looping, enableAudioFocus,
                iMusicListener);
    }

    @Override
    public void stopMusicPlay() {
        Log.d(TAG, "stopMusicPlay");
        mSpeechClient.getSkillServer().stopMusicPlay();
    }

    @Override
    public void stopTTS() {
        Log.d(TAG, "stop TTS");
        mSpeechClient.getSkillServer().stopTTS();
    }

    @Override
    public void setRecognizeMode(boolean isContinue) {
        Log.d(TAG, "setRecognizeMode: " + isContinue);
    }

    public void setRecognizeModeForce(boolean isContinue) {
        Log.d(TAG, "setRecognizeModeForce: " + isContinue);
    }

    @Override
    public void setRecognizeModeNew(boolean isContinue, boolean isCloseStreamData) throws RemoteException {
        Log.d(TAG, "setRecognizeModeNew: " + isContinue + " isCloseStreamData=" + isCloseStreamData);
    }

    @Override
    public void setASREnabled(boolean enable) {
        Log.d(TAG, "set ASR enable " + enable);
    }

    @Override
    public void setRecognizable(boolean enable) {
        Log.d(TAG, "set recognizable " + enable);
    }

    @Override
    public void queryByText(String text) {
        Log.d(TAG, "queryByText: " + text);
        mSpeechClient.getSkillServer().queryByText(text);
    }

    @Override
    public void getActiveAsk(String propertYPE, String robotProperJson) {
        Log.d(TAG, "getActiveAsk: propertYPE:" + propertYPE + ", robotProperJson:" + robotProperJson);
    }

    @Override
    @Deprecated
    public boolean setAsrExtendProperty(String propertyJson) {
        Log.d(TAG, "setAsrExtendProperty: propertyJson:" + propertyJson);
        return false;
    }

    @Override
    public void setASRParams(@ASRParams String asrType, String value) {
        Log.d(TAG, "setASRParams: asrType:" + asrType + ", value:" + value);
    }

    @Override
    public void cancleAudioOperation() {
        Log.d(TAG, "cancelAudioOperation ");
    }

    @Override
    public void setWakeupHintClosed(boolean isWakeupHintClosed) {
        Log.d(TAG, "setWakeupHintClosed: " + isWakeupHintClosed);
        SkillManager.getInstance().setWakeupHintClosed(isWakeupHintClosed);
    }

    @Override
    public void setAngleCenterRange(float angle_center, float angle_range) {
        Log.d(TAG, "setAngleCenterRange, angle_center:" + angle_center + ",angle_range: " + angle_range);
    }

    @Override
    public void setTTSParams(String ttsType, int value) {
        Log.d(TAG, "setTTSParams, ttsType:" + ttsType + ",value: " + value);
    }

    @Override
    public void setMultipleModeEnable(boolean enable) {
        Log.d(TAG, "setMultipleModeEnable, enable:" + enable);
    }

    @Override
    public void setLangRec(String autoLangJson) {
        Log.d(TAG, "setLangRec:  " + autoLangJson);
    }

    @Override
    public void stopTone() {
        Log.d(TAG, "stop tone");
    }

    @Override
    public void switchScene(SceneEntity sceneEntity) {
        KLog.INSTANCE.d("switchScene: " + sceneEntity.getScenes(), TAG);
    }

    @Override
    public int setCustomizeWakeUpWord(String wakeUpWordChinese, String wakeUpWordSpell,
                                      String separator) {
        Log.d(TAG, "setCustomizeWakeUpWord wakeUpWordChinese:" + wakeUpWordChinese
                + ",wakeUpWordSpell:" + wakeUpWordSpell + ",separator:" + separator);
        return SkillManager.getInstance().setCustomizeWakeUpWord(wakeUpWordChinese, wakeUpWordSpell, separator);
    }

    @Override
    public int closeCustomizeWakeUpWord() {
        Log.d(TAG, "closeCustomizeWakeUpWord");
        return SkillManager.getInstance().closeCustomizeWakeUpWord();
    }

    @Override
    public int getPinYinScore(String pinyin, String separator) {
        Log.d(TAG, "getPinYinScore pinyin:" + pinyin + ",separator:" + separator);
        return SkillManager.getInstance().getPinYinScore(pinyin, separator);
    }

    @Override
    public String queryPinYinFromChinese(String chinese) {
        Log.d(TAG, "queryPinYinFromChinese chinese" + chinese);
        return SkillManager.getInstance().queryPinYinFromChinese(chinese);
    }

    @Override
    public String queryPinYinMappingTable(String spell) {
        Log.d(TAG, "queryPinYinMappingTable spell" + spell);
        return SkillManager.getInstance().queryPinYinMappingTable(spell);
    }

    @Override
    public String queryUserSetWakeUpWord() {
        Log.d(TAG, "queryUserSetWakeUpWord");
        return SkillManager.getInstance().queryUserSetWakeUpWord();
    }

    @Override
    public void registerServerCheck(ISkillServerCheckListener iSkillServerCheckListener) {
        Log.d(TAG, "registerServerCheck");
    }

    @Override
    public void unregisterServerCheck() {
        Log.d(TAG, "unregisterServerCheck");
    }


    @Override
    public void onCreate(String app_id) throws RemoteException {

    }

    @Override
    public void onForeground(String app_id) throws RemoteException {
        KLog.INSTANCE.d("onForeground app_id: " + app_id, TAG);
        mSpeechClient.getSkillServer().onForeground(app_id);
    }

    @Override
    public void onBackground(String app_id) throws RemoteException {
        KLog.INSTANCE.d("onBackground app_id: " + app_id, TAG);
        mSpeechClient.getSkillServer().onBackground(app_id);
    }

    @Override
    public void onDestroy(String app_id) throws RemoteException {
    }

    @Override
    public void setPath(String app_id, String path) throws RemoteException {
        mSpeechClient.getSkillServer().onPageChanged(app_id, path);
    }

    @Override
    public void sendAgentMessage(String type,int code,String message) throws RemoteException {
        mSpeechClient.getSkillServer().sendAgentMessage(type, code, message);
    }

    @Override
    public void setSyncReportCustomNlpData(String app_id, String data) throws RemoteException {
        KLog.INSTANCE.d("setSyncReportCustomNlpData app_id: " + app_id + ", data: %s" + data, TAG);
    }

    @Override
    public void setAsyncReportCustomNlpData(String app_id, String data) throws RemoteException {

    }

    @Override
    public void setVersion(String app_id, String version) throws RemoteException {
    }

    @Override
    public void setSyncCustomNlpData(Map map) throws RemoteException {
    }

    @Override
    public String setAsyncCustomNlpData(String opt, String data) throws RemoteException {
        Log.d(TAG, "setAsyncCustomNlpData opt: " + opt + ", data: " + data);
        Intent intent = new Intent(ACTION_NLP_STATE_CHANGED);
        intent.putExtra(KEY_NLP_STATE, data);
        return "";
    }

    @Override
    public void setServerApp(List<String> list) throws RemoteException {
    }

    @Override
    public void resetNlpState() throws RemoteException {
    }

    @Override
    public void setDebug(boolean value) throws RemoteException {
    }

    @Override
    public int getTtsPlayStatus() throws RemoteException {
        Log.d(TAG, "getTtsPlayStatus");
        return 0;
    }

    @Override
    public void downloadTtsAudio(String ttsEntitiesJson) throws RemoteException {
        Log.d(TAG, "downloadMusic " + ttsEntitiesJson);
    }

    @Override
    public String getSpokemanListByLanguage(String lang) throws RemoteException {
        Log.d(TAG, "getSpokemanListByLanguage " + lang);
        return "";
    }

    @Override
    public void closeStreamDataReceived(String s) throws RemoteException {
        Log.d(TAG, "closeStreamDataReceived " + s);
    }

    @Override
    public boolean isRecognizeContinue() throws RemoteException {
        return false;
    }

    @Override
    public boolean isRecognizable() throws RemoteException {
        return false;
    }

    @Override
    public void onAgentActionFinish(String action, int code, String message) throws RemoteException {
        mSpeechClient.getSkillServer().onAgentActionFinish(action, code, message);
    }

    @Override
    public void onAgentActionState(String action, int state, String data) throws RemoteException {
        mSpeechClient.getSkillServer().onAgentActionState(action, state, data);
    }

    @Override
    public void queryByTextWithThinking(String text, boolean isShowThinking) {
        Log.d(TAG, "queryByTextWithThinking: " + text);
        mSpeechClient.getSkillServer().queryByTextWithThinking(text, isShowThinking);
    }
}
