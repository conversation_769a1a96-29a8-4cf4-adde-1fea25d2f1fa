/*
 *     Copyright (C) 2017 OrionStar Technology Project
 *
 *     Licensed under the Apache License, Version 2.0 (the "License");
 *     you may not use this file except in compliance with the License.
 *     You may obtain a copy of the License at
 *
 *          http://www.apache.org/licenses/LICENSE-2.0
 *
 *     Unless required by applicable law or agreed to in writing, software
 *     distributed under the License is distributed on an "AS IS" BASIS,
 *     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *     See the License for the specific language governing permissions and
 *     limitations under the License.
 */
package com.ainirobot.bridge.skill;

import com.ainirobot.coreservice.ISkillCallback;

/**
 * Every SpeechClient represent a APP, such as ModuleAPP and other third-APP. we use package name to
 * identify different APP.
 *
 * <AUTHOR>
 * @date 2018-11-20
 */
public class SpeechClient {

    private String mPackageName;
    private ISkillServer mSkillServer;
    private ISkillCallback mISkillCallback;

    public SpeechClient(String packageName, ISkillServer skillServer) {
        this.mPackageName = packageName;
        this.mSkillServer = skillServer;
    }

    public String getPackageName() {
        return mPackageName;
    }

    public void setPackageName(String packageName) {
        this.mPackageName = packageName;
    }

    public ISkillServer getSkillServer() {
        return mSkillServer;
    }

    private void setSkillServer(ISkillServer skillServer) {
        this.mSkillServer = skillServer;
    }

    public ISkillCallback getSkillCallback() {
        return mISkillCallback;
    }

    public void setSkillCallback(ISkillCallback iSkillCallback) {
        this.mISkillCallback = iSkillCallback;
    }
}
