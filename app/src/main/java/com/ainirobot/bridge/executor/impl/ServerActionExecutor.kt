package com.ainirobot.bridge.executor.impl

import com.ainirobot.agent.action.ActionCompletion
import com.ainirobot.agent.action.ActionEvent
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.bridge.executor.Action
import com.ainirobot.bridge.executor.ActionEvents
import com.ainirobot.bridge.executor.ActionExecutor
import com.ainirobot.common.utils.ImageUtil
import com.ainirobot.common.utils.KLog.e
import com.google.gson.annotations.SerializedName
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.suspendCancellableCoroutine
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.widget.TextView
import android.widget.Toast
import com.ainirobot.agent.R
import com.ainirobot.agent.core.AudioInfo
import com.ainirobot.agent.core.SpeechPlayer
import com.ainirobot.common.network.RemoteApi
import com.ainirobot.common.runOnDefault
import com.ainirobot.common.runOnMain
import com.ainirobot.common.widget.Item
import com.ainirobot.common.widget.KAlertDialog
import com.ainirobot.common.widget.SelectableDialog

open class ServerActionExecutor : ActionExecutor() {

    open suspend fun executeServerAction(event: ActionEvent): ActionCompletion {
        return makeActionCompletion(
            MessageSender.executeActionSync(event)
        )
    }

    override suspend fun execute(event: ActionEvent): ActionCompletion {
        if (event.name == ActionEvents.Server.TTS_PLAY) {
            return makeActionCompletion(
                SpeechPlayer.playSync(
                    AudioInfo(
                        isStream = false,
                        text = event.params["text"] as? String
                    )
                )
            )
        }
        val completion = executeServerAction(event)
        if (!completion.isSuccessful) {
            return completion
        }
        val audioResult = completion.result?.get("audio_result")
        if (audioResult == null
            || audioResult !is Map<*, *>
            || audioResult.isEmpty()) {
            return completion
        }
        return makeActionCompletion(
            SpeechPlayer.playSync(
                AudioInfo(
                    isStream = audioResult["stream"] as Boolean,
                    text = audioResult["text"] as? String,
                    streamConfig = mapOf(
                        "messages" to audioResult["messages"],
                        "llm_config" to audioResult["llm_config"]
                    )
                )
            )
        )
    }
}

@Action(ActionEvents.Server.SEND_MESSAGE)
class SendMessageExecutor: ActionExecutor() {

    data class PictureFile(
        @SerializedName("pictures")
        val pictures: List<String>?,
        @SerializedName("status")
        val status: String
    )

    data class PictureInfo(
        @SerializedName("file_url")
        val url: String
    )

    data class PictureData(
        @SerializedName("data")
        val data: PictureInfo
    )

    override suspend fun execute(event: ActionEvent): ActionCompletion {
        val recipientName = event.params["recipient_name"] as? String
        val messageContent = event.params["message_content"]
        if (recipientName.isNullOrEmpty()) {
            return ActionCompletion(ActionCompletion.FAILED, message = "Invalid params: recipientName=$recipientName")
        }

        if (recipientName.length < 2) {
            KAlertDialog(
                context,
                title = context.getString(R.string.tips),
                message = context.getString(R.string.msg_person_name_too_short, recipientName),
                positiveText = context.getString(R.string.ok)
            ).show("send_message")
            return ActionCompletion(ActionCompletion.FAILED, message = "Invalid recipient name: $recipientName")
        }

        val persons = RemoteApi.getPersonListSync(recipientName)
        return when (persons.size) {
            0 -> {
                KAlertDialog(
                    context,
                    title = context.getString(R.string.tips),
                    message = context.getString(R.string.msg_person_not_found, recipientName),
                    positiveText = context.getString(R.string.ok),
                ).show("send_message")
                ActionCompletion(ActionCompletion.FAILED, message = "No person found")
            }
            1 -> {
                showSendMessageDialog(event, persons[0], messageContent)
            }
            else -> {
                showSelectPersonDialog(event, persons, messageContent)
            }
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private suspend fun showSendMessageDialog(
        event: ActionEvent,
        person: RemoteApi.Person,
        messageContent: Any?,
    ): ActionCompletion {
        return suspendCancellableCoroutine { continuation ->
            KAlertDialog(
                context,
                SpannableString(context.getString(R.string.send_message_to, person.fullName))
                    .apply {
                        setSpan(
                            ForegroundColorSpan(context.getColor(R.color.primary)),
                            4,
                            5 + person.fullName.length,
                            SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    },
                layoutId = R.layout.layout_send_message,
                layoutInit = {
                    it.findViewById<TextView>(R.id.text_view).text = messageContent?.toString()
                    it.findViewById<TextView>(R.id.text_view_2).text = context.getString(R.string.send_message_take_picture_tips)
                },
                positiveText = context.getString(R.string.ok),
                negativeText = context.getString(R.string.cancel),
                positiveListener = {
                    runOnDefault {
                        continuation.resume(sendMessageOnly(event, person, messageContent)) { e ->
                            e.e()
                        }
                    }
                },
                negativeListener = {
                    continuation.resume(
                        ActionCompletion(ActionCompletion.FAILED, message = "Action canceled")
                    ) { e ->
                        e.e()
                    }
                }
            ).apply {
                setOnCancelListener {
                    continuation.resume(
                        ActionCompletion(ActionCompletion.FAILED, message = "Action canceled")
                    ) { e ->
                        e.e()
                    }
                }
            }.show("send_message")
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private suspend fun showSelectPersonDialog(
        event: ActionEvent,
        persons: List<RemoteApi.Person>,
        messageContent: Any?,
    ): ActionCompletion {
        return suspendCancellableCoroutine { continuation ->
            SelectableDialog(
                context,
                title = context.getString(R.string.select_person_sent),
                items = persons.mapIndexed { index, person ->
                    Item(
                        id = index,
                        title = person.fullName,
                        subtitle = person.organization,
                        imageUrl = person.avatarUrl
                    )
                },
                tag = "send_message"
            ).apply {
                setOnCancelListener {
                    continuation.resume(
                        ActionCompletion(ActionCompletion.FAILED, message = "Action canceled")
                    ) { e ->
                        e.e()
                    }
                }
            }.show { item ->
                runOnDefault {
                    continuation.resume(
                        showSendMessageDialog(event, persons[item.id], messageContent)
                    ) { e ->
                        e.e()
                    }
                }
            }
        }
    }

    private suspend fun sendMessageOnly(
        event: ActionEvent,
        person: RemoteApi.Person,
        messageContent: Any?,
    ): ActionCompletion {
        val url = ImageUtil.takePictureSync(2, true).second
        val pictureUrl = url ?: ""
        val params = mutableMapOf(
            "recipient_name" to person.fullName,
            "message_content" to messageContent,
            "message_type" to event.params["message_type"],
            "sender_photo_url" to pictureUrl,
            "person_id" to person.id
        )
        runOnMain {
            Toast.makeText(context, R.string.message_sending, Toast.LENGTH_LONG).show()
        }
        return makeActionCompletion(MessageSender.executeActionSync(event, params, timeoutMillis = 20000))
    }
}
