package com.ainirobot.bridge.executor

import android.content.Context
import android.graphics.Color
import com.ainirobot.agent.action.ActionCompletion
import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.agent.action.ActionEvent
import com.ainirobot.agent.action.AgentEvent
import com.ainirobot.agent.action.ExecutionSide
import com.ainirobot.agent.bus.AgentBus
import com.ainirobot.bridge.executor.impl.AdjustSpeedExecutor
import com.ainirobot.bridge.executor.impl.BackTranslator
import com.ainirobot.bridge.executor.impl.BothActionExecutor
import com.ainirobot.bridge.executor.impl.CancelTranslator
import com.ainirobot.bridge.executor.impl.ComeFarTranslator
import com.ainirobot.bridge.executor.impl.ConfirmTranslator
import com.ainirobot.bridge.executor.impl.DoNothingTranslator
import com.ainirobot.bridge.executor.impl.ExitTranslator
import com.ainirobot.bridge.executor.impl.HeadNodTranslator
import com.ainirobot.bridge.executor.impl.LastFourDigitsInputTranslator
import com.ainirobot.bridge.executor.impl.MoveDirectionTranslator
import com.ainirobot.bridge.executor.impl.NavigationTranslator
import com.ainirobot.bridge.executor.impl.TurnDirectionTranslator
import com.ainirobot.bridge.executor.impl.VerificationCodeInputTranslator
import com.ainirobot.bridge.executor.impl.OpenPageTranslator
import com.ainirobot.bridge.executor.impl.ConfigureWelcomeMessageTranslator
import com.ainirobot.bridge.executor.impl.RegisterTranslator
import com.ainirobot.bridge.executor.impl.SendMessageExecutor
import com.ainirobot.bridge.executor.impl.GuideRouteSelectionTranslator
import com.ainirobot.bridge.executor.impl.SetLocationTranslator
import com.ainirobot.bridge.executor.impl.ClickTranslator

import com.ainirobot.bridge.executor.impl.GuideRouteSelectionFromMapTranslator
import com.ainirobot.bridge.executor.impl.GuideScoreTranslator
import com.ainirobot.bridge.executor.impl.AnswerQuestionFromVisionTranslator
import com.ainirobot.bridge.executor.impl.RobotActionExecutor
import com.ainirobot.bridge.executor.impl.ServerActionExecutor
import com.ainirobot.bridge.executor.impl.SetVolumeExecutor
import com.ainirobot.common.debug.CaptionWindow
import com.ainirobot.common.utils.KLog
import com.ainirobot.features.FollowupFeature
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class Action(vararg val value: String)

class EventDispatcher(
    private val context: Context,
    private val scope: CoroutineScope
) {

    companion object {

        private val translators = listOf(
            // Actions
            MoveDirectionTranslator::class.java,
            TurnDirectionTranslator::class.java,
            ComeFarTranslator::class.java,
            HeadNodTranslator::class.java,
            NavigationTranslator::class.java,
            SetVolumeExecutor::class.java,
            DoNothingTranslator::class.java,
            VerificationCodeInputTranslator::class.java,
            ConfirmTranslator::class.java,
            CancelTranslator::class.java,
            BackTranslator::class.java,
            LastFourDigitsInputTranslator::class.java,
            ExitTranslator::class.java,
            OpenPageTranslator::class.java,
            ConfigureWelcomeMessageTranslator::class.java,
            RegisterTranslator::class.java,
            AdjustSpeedExecutor::class.java,
            GuideRouteSelectionTranslator::class.java,
            SetLocationTranslator::class.java,
            GuideRouteSelectionFromMapTranslator::class.java,
            ClickTranslator::class.java,
            GuideScoreTranslator::class.java,
            AnswerQuestionFromVisionTranslator::class.java,

            // 双端执行，但先客户端执行，再服务端执行
            SendMessageExecutor::class.java,
        )
    }

    private val executorClasses = mutableMapOf<String, Class<out EventExecutor<out AgentEvent>>>()

    init {
        translators.forEach { registerAction(it) }
    }

    fun dispatcher(event: AgentEvent) {
        scope.launch {
//            if (event is ActionEvent) {
                //CaptionWindow.updateAIExecutionProgress(event.displayName)
//            }
            val completion = executeEvent(event)
            when (completion.status) {
                ActionCompletion.SUCCESS -> {
                    CaptionWindow.actionCompleteSuccess(event.name)
                    KLog.d("Action：${event.name} 执行成功, Result: ${completion.result}", "EventDispatcher")
                    RobotConsole.print("Action：${event.name} 执行成功, Result: ${completion.result}")
                }
                ActionCompletion.RECALLED -> {
                    KLog.d("Action：${event.name} 执行完成，同一Action重复执行, Reason: ${completion.message}", "EventDispatcher")
                    RobotConsole.print("Action：${event.name} 执行完成，同一Action重复执行, Reason: ${completion.message}", Color.YELLOW)
                }
                ActionCompletion.INTERRUPTED -> {
                    KLog.d("Action：${event.name} 执行被打断，Reason: ${completion.message}, $completion", "EventDispatcher")
                    RobotConsole.print("Action：${event.name} 执行被打断，Reason: ${completion.message}", Color.YELLOW)
                }
                else -> {
                    KLog.d("Action：${event.name}, 执行失败，Reason: ${completion.message}", "EventDispatcher")
                    RobotConsole.print("Action：${event.name}, 执行失败，Reason: ${completion.message}", Color.RED)
                }
            }
            event.notifyExecuteStatus(completion)
        }
    }

    private fun registerAction(clazz: Class<out EventExecutor<out AgentEvent>>) {
        val annotation = clazz.getAnnotation(Action::class.java)
        if (annotation != null) {
            if (annotation.value.isEmpty()) {
                throw IllegalArgumentException("${clazz.name} annotation value cannot be empty")
            }
            annotation.value.forEach { executorClasses[it] = clazz }
        }
    }

    @Suppress("UNCHECKED_CAST")
    private suspend fun executeEvent(event: AgentEvent): ActionCompletion {
        if (event is ActionEvent) {
            KLog.d("executeEvent agentSDK start, name: ${event.name}, runId: ${event.runId}", "EventDispatcher")
            val completion = AgentBus.executeActionEvent(event)
            // 如果不为空，说明此Action已经在外部AgentAppClient中被处理
            KLog.d("executeEvent agentSDK End, completion: $completion", "EventDispatcher")
            if (completion != null) {
                return completion
            }
            FollowupFeature.pickFollowup(event.planId, event.runId, event.name)
        }

        val actionClass = executorClasses[event.name]
        KLog.d("ActionBridge executeAction: $event, ${event.name}, oneway: ${event.oneway}, timeout: ${event.timeout}, actionClass: $actionClass", "EventDispatcher")
        RobotConsole.print("开始执行Action：${event.name}, oneway: ${event.oneway}, timeout: ${event.timeout}, ${event.params.entries.joinToString(", ")}")

        if (actionClass == null) {
            if (event is ActionEvent) {
                return when (event.side) {
                    ExecutionSide.SERVER -> {
                        ServerActionExecutor().execute(event)
                    }
                    ExecutionSide.ROBOT -> {
                        RobotActionExecutor().execute(event)
                    }
                    ExecutionSide.BOTH -> {
                        BothActionExecutor().execute(event)
                    }
                }
            }
            val msg = "不支持的Action：${event.name}, ${event.params.entries.joinToString(", ")}"
            KLog.d(msg, "EventDispatcher")
            RobotConsole.print(msg)
            return ActionCompletion(ActionCompletion.UNSUPPORTED)
        }

        return actionClass.constructors.firstOrNull()?.let { constructor ->
            val executor = constructor.newInstance() as EventExecutor<AgentEvent>
            executor.context = context
            executor.execute(event)
        } ?: ActionCompletion(ActionCompletion.FAILED, message = "No valid constructor found")
    }
}
