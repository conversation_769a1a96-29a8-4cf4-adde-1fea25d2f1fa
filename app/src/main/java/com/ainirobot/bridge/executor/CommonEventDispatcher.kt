package com.ainirobot.bridge.executor

import android.content.Context
import com.ainirobot.agent.R
import com.ainirobot.agent.action.ActionNode
import com.ainirobot.agent.action.ActionParser
import com.ainirobot.agent.action.CommonEvent
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.agent.core.RunningStatus
import com.ainirobot.common.Config.gson
import com.ainirobot.common.debug.CaptionWindow
import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.TextUtil.formatXml
import com.ainirobot.common.widget.KAlertDialog
import com.ainirobot.features.FollowupFeature
import com.google.gson.JsonArray
import com.google.gson.JsonObject
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import com.ainirobot.agent.core.AudioInfo
import com.ainirobot.agent.core.SpeechPlayer
import com.ainirobot.common.Config
import com.ainirobot.common.Config.ioScope
import com.ainirobot.common.utils.TextUtil


class CommonEventDispatcher(
    private val context: Context,
    private val scope: CoroutineScope
) {

    fun dispatcher(event: CommonEvent) {
        scope.launch { handleEvent(event) }
    }

    private suspend fun handleEvent(event: CommonEvent) {
        when (event.name) {
            "plan.state" -> {
                handlePlanState(event)
            }
            "plan.received" -> {
                handlePlanReceived(event)
            }
            "plan.show" -> {
                handlePlanShow(event)
            }
            "plan.finish" -> {
                handlePlanFinish(event)
            }
            "chat.qa_answer" -> {
                handleChatQAAnswer(event)
            }
        }
    }

    private fun handlePlanState(event: CommonEvent) {
        val params = event.params
        val status = params["status"]
        when (status) {
            "running" -> {
                RobotConsole.print("Plan is running: ${params["message"]}")
            }
            "rejected" -> {
                RobotConsole.print("Plan is rejected: ${params["message"]}")
            }
        }
    }

    private fun handlePlanReceived(event: CommonEvent) {
        CaptionWindow.onPlanReceived()
        val debugInfo = StringBuilder()
        event.params.forEach { (k, v) ->
            if (k == "plan_xml") {
                (v as? String)?.formatXml()?.let {
                    KLog.d(it, "PlanReceived")
                    RobotConsole.print("=================\n${it}=================")
                }
            } else {
                debugInfo.append("$k: $v\n")
            }
        }
        KLog.d(debugInfo.toString(), "PlanReceived")
        RobotConsole.print("=================\n$debugInfo=================")
    }

    private fun handlePlanShow(event: CommonEvent) {
        val xml = event.params["plan_xml"] as? String ?: return
        val message = StringBuilder()
        ActionParser(xml) { node ->
            if (node is ActionNode) {
                message.append(node.displayName).append("->")
            }
        }.parse()
        if (message.length > 2) {
            message.setLength(message.length - 2)
        }
        KAlertDialog(
            context,
            title = context.getString(R.string.plan_show_dialog_title),
            message = message,
            positiveText = context.getString(R.string.ok),
            negativeText = context.getString(R.string.cancel),
            positiveListener = {
                MessageSender.sendMessage(
                    msgType = "plan.plan",
                    content = mapOf("query" to "确认执行")
                )
                RobotConsole.print("确认执行")
            },
            negativeListener = {
                MessageSender.sendMessage(
                    msgType = "plan.plan",
                    content = mapOf("query" to "取消执行")
                )
                RobotConsole.print("取消执行")
            },
        ).show("plan_show")
    }

    private suspend fun handlePlanFinish(event: CommonEvent) {
        val status = event.params["status"] as RunningStatus
        val elapseTime = event.params["elapse_time"] as Int
        if (status == RunningStatus.SUCCEEDED && elapseTime > 500) {
            FollowupFeature.executeAfterPlan(
                planId = event.params["plan_id"] as String,
                runId = event.params["run_id"] as String
            )
        }
    }

    private fun handleChatQAAnswer(event: CommonEvent) {
        val answer = (event.params["answer"] as? String) ?: ""
        val question = (event.params["question"] as? String) ?: ""
        val imageInfoStr = (event.params["image_info"] as? String) ?: ""
        val videoInfoStr = (event.params["video_info"] as? String) ?: ""
        val radioInfoStr = (event.params["radio_info"] as? String) ?: ""
        val userQuery = (event.params["user_query"] as? String) ?: ""

        val uiArray = JsonArray()
        
        // Process image_info if available
        if (!imageInfoStr.isNullOrEmpty()) {
            try {
                val imageArray = gson.fromJson(imageInfoStr, JsonArray::class.java)
                for (i in 0 until imageArray.size()) {
                    val imageObj = imageArray[i].asJsonObject
                    val uiObj = JsonObject().apply {
                        add("image", imageObj)
                        addProperty("type", "Image")
                    }
                    uiArray.add(uiObj)
                }
            } catch (e: Exception) {
                KLog.e("Failed to parse image_info: $e", "ChatQAAnswer")
            }
        } 
        // Process video_info if available
        else if (!videoInfoStr.isNullOrEmpty()) {
            try {
                val videoArray = gson.fromJson(videoInfoStr, JsonArray::class.java)
                for (i in 0 until videoArray.size()) {
                    val videoObj = videoArray[i].asJsonObject
                    val uiObj = JsonObject().apply {
                        add("video", videoObj)
                        addProperty("type", "StandardVideo")
                    }
                    uiArray.add(uiObj)
                }
            } catch (e: Exception) {
                KLog.e("Failed to parse video_info: $e", "ChatQAAnswer")
            }
        } 
        // Process radio_info if available
        else if (!radioInfoStr.isNullOrEmpty()) {
            try {
                val radioArray = gson.fromJson(radioInfoStr, JsonArray::class.java)
                for (i in 0 until radioArray.size()) {
                    val radioObj = radioArray[i].asJsonObject
                    val uiObj = JsonObject().apply {
                        add("radio", radioObj)
                        addProperty("type", "Radio")
                    }
                    uiArray.add(uiObj)
                }
            } catch (e: Exception) {
                KLog.e("Failed to parse audio_info: $e", "ChatQAAnswer")
            }
        }

        val reqParamObj = JsonObject().apply {
            add("ui", uiArray)
            addProperty("text", answer)
        }

        RobotActionSender.send(
            OutputEntity(
                sid = "${event.name}-${System.currentTimeMillis()}",
                traceId = "",
                englishDomain = "tell_me_why",
                englishIntent = "common",
                slots = "{}",
                card = gson.toJson(reqParamObj),
                skillData = "{}",
                userText = if (userQuery.isEmpty()) question else userQuery,
                answerText = answer
            )
        )
    }
}