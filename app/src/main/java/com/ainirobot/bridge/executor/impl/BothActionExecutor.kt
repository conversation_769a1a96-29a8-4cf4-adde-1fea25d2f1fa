package com.ainirobot.bridge.executor.impl

import com.ainirobot.agent.action.ActionEvent
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.bridge.executor.ActionEvents
import com.ainirobot.bridge.executor.CompatibleActionExecutor
import com.ainirobot.bridge.executor.OutputEntity
import com.ainirobot.common.Config.gson
import com.ainirobot.common.utils.KLog


class BothActionExecutor : CompatibleActionExecutor() {

    companion object {

        /**
         * Action与domain intent的映射
         * 注：此映射以后要由服务端（如：接待后台）下发配置
         */
        private val actionMappings = mapOf(
            ActionEvents.Server.WEATHER to Pair("weather", "get_weather"),
            ActionEvents.Server.CALENDAR to Pair("calendar", "search_calendar"),
            ActionEvents.Server.GUIDE_SELECT_SPECIFIC_ROUTE to Pair("MEUI", "select_specific_route"),
            ActionEvents.Server.GUIDE_ROUTE_RECOMMENDATION to Pair("MEUI", "recommendation"),
            ActionEvents.Server.GUIDE_INTRODUCTION to Pair("MEUI", "guide_introduction")
        )
    }

    override suspend fun translate(event: ActionEvent): OutputEntity? {
        KLog.d("translate: ${event.name}", "BothActionExecutor")
        var result = event.preemptiveResult as? Map<*, *>
        if (result == null) {
            result = MessageSender.executeActionSync(event, timeoutMillis = 20000L)?.result
        }
        if (result == null) {
            KLog.d("Action result is null", "BothActionExecutor")
            return null
        }

        val domain: String
        val intent: String
        val item = actionMappings[event.name]
        KLog.d("actionMapping find: ${event.name} => $item", "BothActionExecutor")
        if (item == null) {
            val index = event.name.lastIndexOf('.')
            if (index == -1) {
                KLog.d("Invalid action name: ${event.name}", "BothActionExecutor")
                return null
            }
            domain = event.name.substring(0, index)
            intent = event.name.substring(index + 1)
        } else {
            domain = item.first
            intent = item.second
        }

        return OutputEntity(
            event = event,
            englishDomain = domain,
            englishIntent = intent,
            slots = "{}",
            card = "{}",
            skillData = "{}",
            nlpData = "{}",
            userText = event.displayName,
            answerText = "",
            actionResult = gson.toJson(result)
        )
    }
}

