package com.ainirobot.bridge.executor

import com.ainirobot.agent.action.ActionCompletion
import com.ainirobot.agent.action.ActionEvent
import com.ainirobot.common.Config.gson
import com.ainirobot.common.Config.ioScope
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.KLog.e
import com.ainirobot.coreservice.client.hardware.RobotCore
import com.google.gson.annotations.SerializedName
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withTimeoutOrNull

data class AudioAuth(
    @SerializedName("voice_id")
    val voiceId: String? = null,
    @SerializedName("score")
    val score: Float = 0f
)

data class OutputEntity(
    @SerializedName("sid")
    val sid: String,
    @SerializedName("traceId")
    val traceId: String,
    @SerializedName("queryType")
    val queryType: Int,
    @SerializedName("englishDomain")
    val englishDomain: String,
    @SerializedName("englishIntent")
    val englishIntent: String,
    @SerializedName("intent")
    val intent: String,
    @SerializedName("slots")
    val slots: String,
    @SerializedName("skillData")
    val skillData: String,
    @SerializedName("card")
    val card: String,
    @SerializedName("nlpData")
    val nlpData: String,
    @SerializedName("userText")
    val userText: String,
    @SerializedName("answerText")
    val answerText: String?,
    @SerializedName("answerTextPlay")
    val answerTextPlay: Boolean,
    @SerializedName("soundAngle")
    val soundAngle: Int = -1,
    @SerializedName("audioAuth")
    val audioAuth: AudioAuth? = null,
    @SerializedName("actionResult")
    val actionResult: String? = null,
) {

    @Transient
    var pageName: String = ""

    @Transient
    var timeoutMillis: Long = 0

    constructor(
        sid: String,
        traceId: String,
        englishDomain: String,
        englishIntent: String,
        slots: String,
        card: String,
        userText: String,
        answerText: String? = null,
        queryType: Int = 1,
        nlpData: String = "{}",
        skillData: String = "{}",
        soundAngle: Int = -1,
        audioAuth: AudioAuth? = null,
        actionResult: String? = null,
    ) : this(
        sid = sid,
        traceId = traceId,
        queryType = queryType,
        englishDomain = englishDomain,
        englishIntent = englishIntent,
        intent = "$englishDomain&$englishIntent",
        slots = slots,
        skillData = skillData,
        card = card,
        nlpData = nlpData,
        userText = userText,
        answerText = answerText,
        answerTextPlay = answerText != null,
        soundAngle = soundAngle,
        audioAuth = audioAuth,
        actionResult = actionResult
    )

    constructor(
        event: ActionEvent,
        englishDomain: String,
        englishIntent: String,
        slots: String,
        card: String,
        userText: String,
        answerText: String? = null,
        queryType: Int = 1,
        nlpData: String = "{}",
        skillData: String = "{}",
        soundAngle: Int = -1,
        audioAuth: AudioAuth? = null,
        actionResult: String? = null,
    ) : this(
        sid = "${event.runId}-${event.actionId}",
        traceId = event.planId,
        queryType = queryType,
        englishDomain = englishDomain,
        englishIntent = englishIntent,
        intent = "$englishDomain&$englishIntent",
        slots = slots,
        skillData = skillData,
        card = card,
        nlpData = nlpData,
        userText = userText,
        answerText = answerText,
        answerTextPlay = answerText != null,
        soundAngle = soundAngle,
        audioAuth = audioAuth,
        actionResult = actionResult
    )
}

object RobotActionSender {

    private data class Callback(
        val sid: String,
        val pageName: String,
        val call: (ActionCompletion) -> Unit
    )

    private data class Message(
        @SerializedName("appId")
        val appId: String,
        @SerializedName("screen")
        val screen: String,
        @SerializedName("intent")
        val intent: String,
        @SerializedName("englishIntent")
        val englishIntent: String
    )

    private val mutex = Mutex()
    private val callbacks = mutableMapOf<String, Callback>()

    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun sendSync(
        entity: OutputEntity,
        timeoutMillis: Long = 0
    ): ActionCompletion {
        if (timeoutMillis <= 0) {
            send(entity)
            return ActionCompletion()
        }
        return withTimeoutOrNull(timeoutMillis) {
            suspendCancellableCoroutine { continuation ->
                send(entity) { result ->
                    continuation.resume(result) { e ->
                        e.e("RobotActionSender")
                    }
                }
            }
        } ?: ActionCompletion(
            ActionCompletion.TIMEOUT,
            message = "Robot Action execution timeout"
        )
    }

    fun send(
        entity: OutputEntity,
        callback: ((ActionCompletion) -> Unit)? = null
    ) {
        ioScope.launch {
            mutex.withLock {
                if (callback != null) {
                    KLog.d("Send OutputEntity: ${entity.intent}, sid: ${entity.sid}", "RobotActionSender")
                    callbacks[entity.sid] = Callback(
                        entity.sid,
                        entity.pageName,
                        callback
                    )
                }
            }
            RobotCore.sendRequest(
                entity.intent,
                entity.userText,
                gson.toJson(entity)
            )
        }
    }

    suspend fun onReceiveResponse(
        sid: String,
        status: String,
        message: String?
    ) {
        mutex.withLock {
            val callback = callbacks[sid]
            if (callback != null) {
                val msg = if (message != null) {
                    gson.fromJson(message, Message::class.java)
                } else null
                if (msg == null || callback.pageName == msg.screen) {
                    callback.call(ActionCompletion(status))
                    callbacks.remove(sid)
                }
            }
        }
    }
}