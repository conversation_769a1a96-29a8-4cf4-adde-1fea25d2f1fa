package com.ainirobot.bridge.executor

import android.content.Context
import com.ainirobot.agent.action.ActionCompletion
import com.ainirobot.agent.action.ActionEvent
import com.ainirobot.agent.action.ActionMonitor
import com.ainirobot.agent.action.AgentEvent
import com.ainirobot.agent.core.ActionResult
import com.ainirobot.common.utils.KLog

abstract class EventExecutor<T: AgentEvent> {

    lateinit var context: Context

    abstract suspend fun execute(event: T): ActionCompletion

    fun makeActionCompletion(
        result: ActionResult?
    ): ActionCompletion {
        return if (result != null) {
            ActionCompletion(
                if (result.status) ActionCompletion.SUCCESS else ActionCompletion.FAILED,
                result.result,
                result.message
            )
        } else {
            ActionCompletion(ActionCompletion.TIMEOUT, message = "Action execution timeout")
        }
    }
}

abstract class CompatibleEventExecutor<T: AgentEvent> : EventExecutor<T>() {

    override suspend fun execute(event: T): ActionCompletion {
        val entity = translate(event)
        KLog.d("ActionBridge executeEntity oneway(${event.oneway}): $entity")
        if (entity == null) {
            return ActionCompletion(status = ActionCompletion.FAILED, message = "Translate failed")
        }

        if (event.oneway) {
            RobotActionSender.send(entity)
            return ActionCompletion(ActionCompletion.SUCCESS)
        } else {
            val completion = RobotActionSender.sendSync(
                entity,
                timeoutMillis = if (entity.timeoutMillis <= 0) event.timeout - 5000 else entity.timeoutMillis
            )
            if (completion.isSuccessful) {
                val runningActionNode = ActionMonitor.getRunningLocalAction {
                    if (event is ActionEvent) {
                        event.name != it.name && event.runId != it.runId
                    } else event.name != it.name
                }
                if (runningActionNode != null) {
                    return ActionCompletion(
                        status = ActionCompletion.INTERRUPTED,
                        result = completion.result,
                        message = "there are running action: ${runningActionNode.name}"
                    )
                }
            }
            return completion
        }
    }

    abstract suspend fun translate(event: T): OutputEntity?
}

abstract class ActionExecutor : EventExecutor<ActionEvent>()

abstract class CompatibleActionExecutor : CompatibleEventExecutor<ActionEvent>()