package com.ainirobot.bridge.executor.impl

import android.content.ComponentName
import android.content.Intent
import android.net.Uri
import android.util.Log
import com.ainirobot.agent.R
import com.ainirobot.agent.action.ActionCompletion
import com.ainirobot.bridge.executor.Action
import com.ainirobot.bridge.executor.ActionEvents
import com.ainirobot.bridge.executor.OutputEntity
import com.ainirobot.agent.action.ActionEvent
import com.ainirobot.agent.core.MessageSender
import com.ainirobot.bridge.executor.ActionExecutor
import com.ainirobot.bridge.executor.CompatibleActionExecutor
import com.ainirobot.common.Config.gson
import com.ainirobot.common.utils.DeviceOS
import com.ainirobot.common.utils.KLog
import com.ainirobot.common.utils.KLog.e
import com.ainirobot.common.debug.RobotConsole
import com.ainirobot.common.runOnMain
import com.ainirobot.common.widget.EasyDialog
import com.ainirobot.coreservice.client.SystemApi
import com.ainirobot.coreservice.client.listener.CommandListener
import com.ainirobot.features.web.WebFeature


class RobotActionExecutor : CompatibleActionExecutor() {

    private data class RobotIntent(
        val domain: String,
        val intent: String,
        val pageName: String = ""
    )

    companion object {

        /**
         * Action与domain intent的映射
         * 注：此映射以后要由服务端（如：接待后台）下发配置
         */
        private val actionMappings = mapOf(
            ActionEvents.CRUISE to RobotIntent("robot_navigation", "cruise", "cruise"),
            ActionEvents.NOT_MOVE to RobotIntent("general_command", "stop"),
            ActionEvents.START_DANCE to RobotIntent("interactive", "start_dance", "dance"),
            ActionEvents.INTERVIEW_START to RobotIntent("visit", "interview", "reception"),
            ActionEvents.TAKE_PHOTO to RobotIntent("verification_code", "reinput"),
            ActionEvents.NEXT to RobotIntent("general_command", "next"),
            ActionEvents.MULTIMEDIA_PLAY to RobotIntent("multimedia_command", "play"),
            ActionEvents.COMMON_PAUSE to RobotIntent("general_command", "pause"),
            ActionEvents.COMMON_REPLAY to RobotIntent("general_command", "replay"),
            ActionEvents.START_QUESTION to RobotIntent("general_command", "start_question"),
            ActionEvents.COMMON_REPEAT to RobotIntent("general_command", "repeat"),
            ActionEvents.ROUTES_OTHERS to RobotIntent("MEUI", "routes_other"),
//            ActionEvents.GUIDE_INTRODUCTION to RobotIntent("MEUI", "guide_introduction", "guide"),
            ActionEvents.WHO_AM_I to RobotIntent("register", "ask"),
            ActionEvents.GO_CHARGING to RobotIntent("general_command", "charging"),
            ActionEvents.INTERVIEW_START_PHOTO to RobotIntent("interactive", "start_photo", "group_photo"),
            ActionEvents.CHANGE_FACE to RobotIntent("interactive", "change_face"),
            ActionEvents.EXIT_CRUISE to RobotIntent("robot_navigation", "stop_cruise"),
            ActionEvents.START_IMMEDIATELY to RobotIntent("general_command", "start_immediately"),
            ActionEvents.SILENT to RobotIntent("general_command", "silent"),



        )
    }

    override suspend fun translate(event: ActionEvent): OutputEntity? {
        val domain: String
        val intent: String
        val pageName: String
        val item = actionMappings[event.name]
        KLog.d("actionMapping find: ${event.name} => $item", "RobotActionExecutor")
        if (item == null) {
            val index = event.name.lastIndexOf('.')
            if (index == -1) {
                KLog.d("Invalid action name: ${event.name}", "RobotActionExecutor")
                return null
            }
            domain = event.name.substring(0, index)
            intent = event.name.substring(index + 1)
            pageName = ""
        } else {
            domain = item.domain
            intent = item.intent
            pageName = item.pageName
        }

        return OutputEntity(
            event = event,
            englishDomain = domain,
            englishIntent = intent,
            slots = "{}",
            card = "{}",
            skillData = "{}",
            nlpData = "{}",
            userText = event.displayName,
            answerText = "",
            actionResult = gson.toJson(event.params)
        ).also { it.pageName = pageName }
    }
}

@Action(ActionEvents.MOVE_DIRECTION)
class MoveDirectionTranslator : CompatibleActionExecutor() {

    override suspend fun translate(event: ActionEvent): OutputEntity {
        val direction = event.params["direction"]
        //暂时不使用，目前业务中前进固定0.3m，后退0.2m，且要求必须在接待点才能执行
        var distance = 0.3

        if ("forward" == direction) {
            val movingDistance = event.params["moving_distance"].toString().toDoubleOrNull()

            if (movingDistance != null && movingDistance > 0 && movingDistance <= 10) {
                distance = movingDistance
            }
        }

        val englishDomain = "robot_action"
        val englishIntent = "go_${direction}"
        val nplData = object {
            val detail = listOf(
                object {
                    val intent = englishIntent
                    val english_domain = englishDomain
                    val actions = listOf(
                        listOf(
                            object {
                                val args = mapOf("play_mode" to englishIntent)
                                val action = "action_robot"
                                val distance = distance
                            }
                        )
                    )
                }
            )
        }

        return OutputEntity(
            event = event,
            englishDomain = englishDomain,
            englishIntent = englishIntent,
            slots = "{}",
            card = "{}",
            skillData = "{}",
            nlpData = gson.toJson(nplData),
            userText = event.displayName,
            answerText = "",
        ).apply { pageName = "sport"}
    }
}

@Action(ActionEvents.TURN_DIRECTION)
class TurnDirectionTranslator : CompatibleActionExecutor() {

    override suspend fun translate(event: ActionEvent): OutputEntity {
        val direction = event.params["direction"]
        val angle: Int? = event.params["angle"] as? Int

        val englishDomain = "robot_action"
        val englishIntent = "turn_any_direction"

        val nplData = object {
            val detail = listOf(
                object {
                    val intent = englishIntent
                    val english_domain = englishDomain
                    val actions = listOf(
                        listOf(
                            object {
                                val args = mapOf("play_mode" to englishIntent)
                                val action = "action_robot"
                                val angle = angle
                                val direction = direction
                            }
                        )
                    )
                }
            )
        }

        return OutputEntity(
            event = event,
            englishDomain = englishDomain,
            englishIntent = englishIntent,
            slots = "{}",
            card = "{}",
            skillData = "{}",
            nlpData = gson.toJson(nplData),
            userText = event.displayName,
            answerText = "",
        ).apply { pageName = "sport"}
    }
}

@Action(ActionEvents.COME_FAR)
class ComeFarTranslator : CompatibleActionExecutor() {

    override suspend fun translate(event: ActionEvent): OutputEntity {
        val englishDomain = "robot_action"
        val englishIntent = "come_far"
        val nplData = object {
            val detail = listOf(
                object {
                    val intent = englishIntent
                    val english_domain = englishDomain
                    val actions = listOf(
                        listOf(
                            object {
                                val args = mapOf("play_mode" to englishIntent)
                                val action = "action_robot"
                            }
                        )
                    )
                }
            )
        }

        return OutputEntity(
            event = event,
            englishDomain = englishDomain,
            englishIntent = englishIntent,
            slots = "{}",
            card = "{}",
            skillData = "{}",
            nlpData = gson.toJson(nplData),
            userText = event.displayName,
            answerText = "",
        ).apply { pageName = "sport"}
    }
}

@Action(ActionEvents.HEAD_NOD)
class HeadNodTranslator : CompatibleActionExecutor() {

    override suspend fun translate(event: ActionEvent): OutputEntity {
        val nplData = object {
            val detail = listOf(
                object {
                    val intent = "head_nod"
                    val english_domain = "robot_action"
                    val actions = listOf(
                        listOf(
                            object {
                                val args = mapOf("play_mode" to "head_nod",)
                                val action = "action_robot"
                            }
                        )
                    )
                }
            )
        }

        return OutputEntity(
            event = event,
            englishDomain = "robot_action",
            englishIntent = "head_nod",
            slots = "{}",
            card = "{}",
            skillData = "{}",
            nlpData = gson.toJson(nplData),
            userText = event.displayName,
            answerText = "",
        ).apply { pageName = "sport"}
    }
}

@Action(ActionEvents.NAVIGATE_REC_START)
class NavigationTranslator : CompatibleActionExecutor() {

    override suspend fun translate(event: ActionEvent): OutputEntity {
        // 从event中获取值
        val rawDestinations = event.params["destinations"] as? List<*>
        val rawExtendDestinations = event.params["extend_destinations"] as? List<*>
        
        // 过滤掉空字符串和null值
        val destinations = rawDestinations?.mapNotNull { 
            (it as? String)?.takeIf { str -> str.isNotEmpty() } 
        } ?: emptyList()
        
        val extendDestinations = rawExtendDestinations?.mapNotNull { 
            (it as? String)?.takeIf { str -> str.isNotEmpty() } 
        } ?: emptyList()

        // 构建 destination 对象，根据数组长度决定是否包含 text 和 value
        val destinationMap = mutableMapOf(
            "dict_name" to "SEMANTIC_TAG_DESTINATION",
            "slot_id" to 0,
            "slot_type" to "NORMAL"
        )

        // 只有当数组长度为1时才添加 text 和 value
        if (destinations.size == 1) {
            destinationMap["text"] = destinations[0]
            destinationMap["value"] = destinations[0]
        }

        // 构建 slots 部分
        val slots = gson.toJson(
            mapOf(
                "destination" to listOf(destinationMap),
                "destination_list" to destinations,
                "extend_destination_list" to extendDestinations,
                "guide_text" to (event.params["guide_text"]?.toString() ?: "")
            )
        )

//        val displayDestination = destinations?.firstOrNull() ?: ""
        return OutputEntity(
            event = event,
            englishDomain = "guide",
            englishIntent = "guide",
            slots = slots,
            card = "{}",
            userText = event.params["_USER_QUERY"]?.toString() ?: "",
        ).apply { pageName = "leading" }
    }
}

@Action(ActionEvents.SET_VOLUME)
class SetVolumeExecutor : ActionExecutor() {

    override suspend fun execute(event: ActionEvent): ActionCompletion {
        val volumePercent = parseVolumeLevel(event.params["volume_level"])
        if (volumePercent == null) {
            // 解析失败，不执行音量修改，直接返回失败
            return ActionCompletion(
                ActionCompletion.FAILED,
                message = "Invalid volume_level parameter: ${event.params["volume_level"]}"
            )
        }
        val tips = when (volumePercent) {
            0 -> {
                context.getString(R.string.volume_min_tips)
            }
            100 -> {
                context.getString(R.string.volume_max_tips)
            }
            else -> {
                context.getString(R.string.volume_changed_tips, volumePercent)
            }
        }
        MessageSender.executeAction(
            ActionEvents.Server.TTS_PLAY,
            mapOf("text" to tips),
            isSafety = false
        )
        val volumeLevel = (volumePercent*DeviceOS.maxVolumeLevel/100.0f).toInt()
        DeviceOS.setVolumeLevel(context, volumeLevel)
        return ActionCompletion()
    }

    private fun parseVolumeLevel(value: Any?): Int? {
        return try {
            when (value) {
                is Int -> value
                is Double -> value.toInt()
                is Float -> value.toInt()
                is String -> {
                    try {
                        value.toInt()
                    } catch (e: NumberFormatException) {
                        try {
                            value.toDouble().toInt()
                        } catch (e: NumberFormatException) {
                            null // 解析失败
                        }
                    }
                }
                else -> value.toString().let { str ->
                    try {
                        str.toInt()
                    } catch (e: NumberFormatException) {
                        try {
                            str.toDouble().toInt()
                        } catch (e: NumberFormatException) {
                            null // 解析失败
                        }
                    }
                }
            }
        } catch (e: Exception) {
            null // 解析失败
        }
    }
}

//@Action(ActionEvents.SET_VOLUME)
//class SetVolumeTranslator : CompatibleActionExecutor() {
//
//    override suspend fun translate(event: ActionEvent): OutputEntity {
//        val volumePercent = event.params["volume_level"].toString().toFloat()
//        val volumeLevel = (volumePercent*DeviceOS.maxVolumeLevel/100.0f).toInt()
//
//        val slots = mapOf(
//            "command_param" to listOf(
//                mapOf(
//                    "dict_name" to "",
//                    "slot_id" to 0,
//                    "slot_type" to "NORMAL",
//                    "text" to event.displayName,
//                    "value" to "Volume",
//                )
//            ),
//            "command_value" to listOf(
//                mapOf(
//                    "dict_name" to "set_command_level",
//                    "slot_id" to 0,
//                    "slot_type" to "NORMAL",
//                    "text" to "$volumeLevel",
//                    "value" to "$volumeLevel",
//                )
//            )
//        )
//
//        return OutputEntity(
//            event = event,
//            englishDomain = "general_command",
//            englishIntent = "set_command",
//            slots = gson.toJson(slots),
//            userText = event.displayName,
//            card = "{\"ui\":[{\"button\":{\"text\":\"\"}, \"type\":\"Button\"}]}"
//        )
//    }
//}

@Action(ActionEvents.DO_NOTHING)
class DoNothingTranslator : ActionExecutor() {

    override suspend fun execute(event: ActionEvent): ActionCompletion {
        return ActionCompletion()
    }
}

@Action(ActionEvents.VERIFICATION_CODE_INPUT)
class VerificationCodeInputTranslator : CompatibleActionExecutor() {//last_4_digits

    override suspend fun translate(event: ActionEvent): OutputEntity {

        val verificationCode = event.params["verification_code"] as? List<Int>
        val verificationCodeString = verificationCode?.joinToString("") { it.toString() } ?: ""

        val slotsMap = mapOf(
            "verification_code" to listOf(
                mapOf(
                    "ex" to emptyMap<String, Any>(),
                    "dict_name" to "SEMANTIC_TAG_VERIFICATION_CODE",
//                    "slot_id" to 242988597837847,
                    "slot_type" to "NUMBER",
                    "text" to verificationCodeString.map {
                        when(it) {
                            '0' -> "零"
                            '1' -> "一"
                            '2' -> "二"
                            '3' -> "三"
                            '4' -> "四"
                            '5' -> "五"
                            '6' -> "六"
                            '7' -> "七"
                            '8' -> "八"
                            '9' -> "九"
                            else -> it.toString()
                        }
                    }.joinToString(""),
                    "value" to verificationCodeString
                )
            )
        )

        // 明确指定类型进行转换
        val slots = gson.toJson(slotsMap)

        return OutputEntity(
            event = event,
            englishDomain = "verification_code",
            englishIntent = "inform",
            slots = slots,
            userText = event.displayName,
            card = "{}"
        )
    }
}

@Action(ActionEvents.SCORE)
class GuideScoreTranslator : CompatibleActionExecutor() {//last_4_digits

    override suspend fun translate(event: ActionEvent): OutputEntity {

        val score = event.params["score"] as? Int

        val slotsMap = mapOf(
            "star" to listOf(
                mapOf(
                    "value" to score
                )
            )
        )

        // 明确指定类型进行转换
        val slots = gson.toJson(slotsMap)

        return OutputEntity(
            event = event,
            englishDomain = "MEUI",
            englishIntent = "score",
            slots = slots,
            userText = event.displayName,
            card = "{}"
        )
    }
}

@Action(ActionEvents.ANSWER_QUESTION_FROM_VISION)
class AnswerQuestionFromVisionTranslator : CompatibleActionExecutor() {

    override suspend fun translate(event: ActionEvent): OutputEntity {

        val slots = gson.toJson(
            mapOf(
                "plan_id" to event.planId,
                "action_id" to event.actionId,
                "run_id" to event.runId,
                "action" to event.name,
                "original_action_name" to event.params["original_action_name"],
                "question" to event.params["question"],
                "_USER_QUERY" to event.params["_USER_QUERY"],
                "_CURRENT_SUMMARY" to event.params["_CURRENT_SUMMARY"]
            )
        )

        return OutputEntity(
            event = event,
            englishDomain = "orion.agent.action",
            englishIntent = "ANSWER_QUESTION_FROM_VISION",
            slots = slots,
            card = "{}",
            skillData = "{}",
            nlpData = "{}",
            userText = event.displayName,
            answerText = "",
        )
    }
}

@Action(ActionEvents.CONFIRM)
class ConfirmTranslator : CompatibleActionExecutor() {

    override suspend fun execute(event: ActionEvent): ActionCompletion {
        if (EasyDialog.onEventReceived(false)) {
            return ActionCompletion()
        }
        return super.execute(event)
    }

    override suspend fun translate(event: ActionEvent): OutputEntity {
        return OutputEntity(
            event = event,
            englishDomain = "general_command",
            englishIntent = "confirm",
            slots = "{}",
            card = "{}",
            skillData = "{}",
            nlpData = "{}",
            userText = event.displayName,
            answerText = "",
        )

    }
}

@Action(ActionEvents.BACK)
class BackTranslator : CompatibleActionExecutor() {

    override suspend fun execute(event: ActionEvent): ActionCompletion {
        if (WebFeature.closeWeb()) {
            return ActionCompletion()
        }
        return super.execute(event)
    }

    override suspend fun translate(event: ActionEvent): OutputEntity {
        return OutputEntity(
            event = event,
            englishDomain = "general_command",
            englishIntent = "return",
            slots = "{}",
            card = "{}",
            skillData = "{}",
            nlpData = "{}",
            userText = event.displayName,
            answerText = "",
        )
    }
}

@Action(ActionEvents.CANCEL)
class CancelTranslator : CompatibleActionExecutor() {

    override suspend fun execute(event: ActionEvent): ActionCompletion {
        if (WebFeature.closeWeb()) {
            return ActionCompletion()
        }
        if (EasyDialog.onEventReceived(true)) {
            return ActionCompletion()
        }
        return super.execute(event)
    }

    override suspend fun translate(event: ActionEvent): OutputEntity {
        return OutputEntity(
            event = event,
            englishDomain = "general_command",
            englishIntent = "cancel",
            slots = "{}",
            card = "{}",
            skillData = "{}",
            nlpData = "{}",
            userText = event.displayName,
            answerText = "",
        )
    }
}

@Action(ActionEvents.LAST_4_DIGITS_INPUT)
class LastFourDigitsInputTranslator : CompatibleActionExecutor() {//last_4_digits

    override suspend fun translate(event: ActionEvent): OutputEntity {
        val last4DigitsInt = event.params["last_4_digits"] as? List<Int>
        val last4DigitsString = last4DigitsInt?.joinToString("") { it.toString() } ?: ""

        val slotsMap = mapOf(
            "verification_code" to listOf(
                mapOf(
                    "ex" to emptyMap<String, Any>(),
                    "dict_name" to "SEMANTIC_TAG_VERIFICATION_CODE",
//                    "slot_id" to 242988597837847,
                    "slot_type" to "NUMBER",
                    "text" to last4DigitsString.map {
                        when(it) {
                            '0' -> "零"
                            '1' -> "一"
                            '2' -> "二"
                            '3' -> "三"
                            '4' -> "四"
                            '5' -> "五"
                            '6' -> "六"
                            '7' -> "七"
                            '8' -> "八"
                            '9' -> "九"
                            else -> it.toString()
                        }
                    }.joinToString(""),
                    "value" to last4DigitsString
                )
            )
        )

        // 明确指定类型进行转换
        val slots = gson.toJson(slotsMap)

        return OutputEntity(
            event = event,
            englishDomain = "verification_code",
            englishIntent = "inform",
            slots = slots,
            userText = event.displayName,
            card = "{}"
        )
    }
}

@Action(ActionEvents.EXIT)
class ExitTranslator : CompatibleActionExecutor() {

    override suspend fun execute(event: ActionEvent): ActionCompletion {
        if (WebFeature.closeWeb()) {
            return ActionCompletion()
        }
        return super.execute(event)
    }

    override suspend fun translate(event: ActionEvent): OutputEntity {
        // 构建 slots 和 skillData 的空 JSON 对象
        val slots = "{}"
        val skillData = "{}"
        val nlpData = "{}"

        // 返回转换后的 OutputEntity
        return OutputEntity(
            sid = event.runId,
            traceId = event.runId.replace("-", ""),
            queryType = 1,
            englishDomain = "general_command",
            englishIntent = "stop",
            intent = "general_command&stop",
            slots = slots,
            skillData = skillData,
            card = "{}",
            nlpData = nlpData,
            userText = event.displayName,
            answerText = null,
            answerTextPlay = false,
            soundAngle = -1,
            audioAuth = null
        )
    }
}

@Action(ActionEvents.OPEN_WEB_URL, ActionEvents.OUTDOOR_NAVIGATE_START)
class OpenPageTranslator : CompatibleActionExecutor() {

    private fun launchApp(url: String): Boolean {
        val uri = Uri.parse(url) ?: return false
        if (uri.scheme != "package_name") {
            return false
        }

        val packageName = uri.host?: return false
        val segments = uri.pathSegments
        val intent = if (segments.isNullOrEmpty()) {
            context.packageManager.getLaunchIntentForPackage(packageName)
        } else {
            Intent().also { it.component = ComponentName(packageName, segments[0]) }
        }?.also {
            it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }

        try {
            context.startActivity(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return true
    }

//    override suspend fun execute(event: ActionEvent): ActionCompletion {
//        val url = event.params["url"].toString()
//        if (launchApp(url)) {
//            return ActionCompletion()
//        }
//
//        return if (WebFeature.openWeb(url)) {
//            ActionCompletion()
//        } else {
//            return ActionCompletion(
//                ActionCompletion.FAILED,
//                message = "Open web url failed"
//            )
//        }
//    }

    override suspend fun execute(event: ActionEvent): ActionCompletion {
        val url = event.params["url"].toString()
        if (launchApp(url)) {
            return ActionCompletion()
        }

        return super.execute(event)
    }

    override suspend fun translate(event: ActionEvent): OutputEntity {
        val url = event.params["url"].toString();
        val scale = event.params["screen_scale_percent"]?.toString()?.toFloatOrNull() ?: 30
        event.params.forEach { (key, value) ->
            KLog.d("param key: $key, value: $value", "OpenPageTranslator")
        }
        val skillDataMap = mapOf(
            "web_app" to mapOf(
                "app_url" to url,
                "app_params" to mapOf(
                    "screen_scale_percent" to scale
                )
            )
        )
        val skillDataJson = gson.toJson(skillDataMap)
        return OutputEntity(
            event = event,
            englishDomain = "open_page",
            englishIntent = "open_page",
            slots = "{}",
            card = "{}",
            skillData = skillDataJson,
            nlpData = "{}",
            userText = event.displayName,
            answerText = "",
        )
    }
}

@Action(ActionEvents.CONFIGURE_WELCOME_MESSAGE)
class ConfigureWelcomeMessageTranslator: CompatibleActionExecutor() {

    override suspend fun translate(event: ActionEvent): OutputEntity {
        Log.w("ConfigureWelcomeMessageTranslator", "translate: $event")
        var name = event.params["nick_name"]
        val welcomeMessage = event.params["welcome_message"]
        Log.w("ConfigureWelcomeMessageTranslator", "translate: $name, $welcomeMessage")
        //todo test
//        name = null;
        if(name == null || name == "") {
            val registerMode = "Recognized"
            val slotsMap = mapOf(
                "start" to listOf(
                    mapOf(
                        "text" to "",
                        "value" to "",
                        "welcome_message" to welcomeMessage,
                        "register_mode" to registerMode,
                        "action_type" to "configure_welcome_message"
                    )
                )
            )

            // 明确指定类型进行转换
            val slots = gson.toJson(slotsMap)

            return OutputEntity(
                event = event,
                englishDomain = "register",
                englishIntent = "ask",
                slots = slots,
                card = "{}",
                skillData = "{}",
                nlpData = "{}",
                userText = event.displayName,
                answerText = "",
            )
        }

        val slotsMap = mapOf(
            "start" to listOf(
                mapOf(
                    "text" to name,
                    "value" to name,
                    "welcome_message" to welcomeMessage,
                    "action_type" to "configure_welcome_message"
                )
            )
        )

        // 明确指定类型进行转换
        val slots = gson.toJson(slotsMap)

        return OutputEntity(
            event = event,
            englishDomain = "register",
            englishIntent = "register",
            slots = slots,
            card = "{}",
            skillData = "{}",
            nlpData = "{}",
            userText = "",
            answerText = "",
        ).apply { pageName = "register"}
    }
}

@Action(ActionEvents.REGISTER)
class RegisterTranslator: CompatibleActionExecutor() {

    override suspend fun translate(event: ActionEvent): OutputEntity {
        val name = event.params["nick_name"]
        val welcomeMessage = event.params["welcome_message"]

        val slotsMap = mapOf(
            "start" to listOf(
                mapOf(
                    "text" to name,
                    "value" to name,
                    "welcome_message" to welcomeMessage,
                )
            )
        )

        // 明确指定类型进行转换
        val slots = gson.toJson(slotsMap)

        return OutputEntity(
            event = event,
            englishDomain = "register",
            englishIntent = "register",
            slots = slots,
            card = "{}",
            skillData = "{}",
            nlpData = "{}",
            userText = "",
            answerText = "",
        ).apply { pageName = "register"}
    }
}

@Action(ActionEvents.ADJUST_SPEED)
class AdjustSpeedExecutor: ActionExecutor() {

    override suspend fun execute(event: ActionEvent): ActionCompletion {
        Log.e("AdjustSpeedExecutor", "execute: $event")
        val speed = event.params["adjusted_speed"] as? Float ?: return ActionCompletion(
            ActionCompletion.FAILED,
            message = "adjusted_speed is null"
        )
        return try {
            Log.e("AdjustSpeedExecutor", "setLineSpeed: $speed")
            RobotConsole.print("Speed Adjust setLineSpeed: $speed")
            SystemApi.getInstance().setLineSpeed(
                (System.currentTimeMillis() % 100000).toInt(),
                speed.toString(),
                object : CommandListener() {

                    override fun onResult(result: Int, message: String?, extraData: String?) {
                        Log.e("AdjustSpeedExecutor", "onResult: $result, $message, $extraData")
                        RobotConsole.print("Speed Adjust result: $result, $message, $extraData")
                        KLog.d("onResult: $result, $message, $extraData", "AdjustSpeedExecutor")
                    }
                }
            )
            ActionCompletion()
        } catch (e: Exception) {
            e.e("AdjustSpeedExecutor")
            ActionCompletion(
                ActionCompletion.FAILED,
                message = "AdjustSpeedExecutor error: ${e.message}"
            )
        }
    }
}

@Action(ActionEvents.GUIDE_ROUTE_SELECTION)
class GuideRouteSelectionTranslator : CompatibleActionExecutor() {

    override suspend fun translate(event: ActionEvent): OutputEntity {
        val tour_id = event.params["route"]
        val slots = gson.toJson(
            mapOf(
                "tour_id" to tour_id
            )
        )
        return OutputEntity(
            event = event,
            englishDomain = "MEUI",
            englishIntent = "guide_introduction",
            slots = slots,
            card = "{}",
            userText = event.displayName,
        ).apply { pageName = "guide" }
    }
}

@Action(ActionEvents.SET_LOCATION)
class SetLocationTranslator : CompatibleActionExecutor() {

    override suspend fun translate(event: ActionEvent): OutputEntity {
        val location = event.params["location"]
        val slotsMap = mapOf(
            "location" to listOf(
                mapOf(
                    "ex" to emptyMap<Any, Any>(),
                    "dict_name" to "",
                    "slot_type" to "NORMAL",
                    "text" to location,
                    "value" to location,
//                    "slot_id" to 219139709796352
                )
            )
        )
        val slots = gson.toJson(slotsMap)
        return OutputEntity(
            event = event,
            englishDomain = "robot_navigation",
            englishIntent = "set_location",
            slots = slots,
            card = "{}",
            userText = event.displayName,
        )
    }
}

@Action(ActionEvents.GUIDE_ROUTE_SELECTION_FROM_MAP)
class GuideRouteSelectionFromMapTranslator : CompatibleActionExecutor() {

    override suspend fun translate(event: ActionEvent): OutputEntity {
        val tourId = event.params["tour_id"]
        val points = event.params["points"]
        val slots = gson.toJson(
            mapOf(
                "tour_id" to tourId,
                "points" to points
            )
        )
        return OutputEntity(
            event = event,
            englishDomain = "MEUI",
            englishIntent = "guide_introduction",
            slots = slots,
            card = "{}",
            userText = event.displayName,
        ).apply { pageName = "guide" }
    }
}

@Action(ActionEvents.CLICK)
class ClickTranslator : CompatibleActionExecutor() {

//    override suspend fun execute(event: ActionEvent): ActionCompletion {
//        val index = event.params["element_id"] as? Int
//            ?: return ActionCompletion(
//                ActionCompletion.FAILED,
//                message = "element_id is null"
//            )
//        runOnMain { WebFeature.simulateClick(index) }
//        return ActionCompletion()
//    }

    override suspend fun translate(event: ActionEvent): OutputEntity {

        val index = event.params["element_id"] as? Int
//        val index = "1"
        val slots = gson.toJson(
            mapOf(
                "type" to "click",
                "index" to index
            )
        )
        KLog.d("ClickTranslator: $slots", "ClickTranslator")

        return OutputEntity(
            event = event,
            englishDomain = "agent",
            englishIntent = "interactive",
            slots = slots,
            card = "{}",
            skillData = "{}",
            nlpData = "{}",
            userText = event.displayName,
            answerText = "",
        )
    }
}

