package com.ainirobot.bridge.executor

object ActionEvents {

    object Server {

        const val TTS_PLAY = "orion.agent.action.SAY"

        const val REALTIME_SAY = "orion.agent.action.REALTIME_SAY"

        const val WEATHER = "orion.agent.action.WEATHER"

        const val CALENDAR = "orion.agent.action.CALENDAR"

        const val SEND_MESSAGE = "orion.agent.action.SEND_MESSAGE"

        const val SAY_WELCOME_OR_FAREWELL_MESSAGE = "orion.agent.action.SAY_WELCOME_OR_FAREWELL_MESSAGE"

        const val GUIDE_SELECT_SPECIFIC_ROUTE = "orion.agent.action.GUIDE_SELECT_SPECIFIC_ROUTE"

        const val GUIDE_ROUTE_RECOMMENDATION = "orion.agent.action.GUIDE_ROUTE_RECOMMENDATION"

        const val GUIDE_INTRODUCTION = "orion.agent.action.GUIDE_INTRODUCTION"

        const val CALL_LLM = "orion.agent.action.CALL_LLM"

    }

    const val NAVIGATE_REC_START = "orion.agent.action.NAVIGATE_REC_START"

    const val SET_VOLUME = "orion.agent.action.SET_VOLUME"

    const val INTERVIEW_START = "orion.agent.action.INTERVIEW_START"

    const val CRUISE = "orion.agent.action.CRUISE"

    const val NOT_MOVE = "orion.agent.action.NOT_MOVE"

    const val COME_FAR = "orion.agent.action.COME_FAR"

    const val MOVE_DIRECTION = "orion.agent.action.MOVE_DIRECTION"

    const val TURN_DIRECTION = "orion.agent.action.TURN_DIRECTION"

    const val HEAD_NOD = "orion.agent.action.HEAD_NOD"

    const val START_DANCE = "orion.agent.action.START_DANCE"

    const val DO_NOTHING = "orion.agent.action.DO_NOTHING"

    const val VERIFICATION_CODE_INPUT = "orion.agent.action.VERIFICATION_CODE_INPUT"

    const val LAST_4_DIGITS_INPUT = "orion.agent.action.LAST_4_DIGITS_INPUT"

    const val CONFIRM = "orion.agent.action.CONFIRM"

    const val CANCEL = "orion.agent.action.CANCEL"

    const val BACK = "orion.agent.action.BACK"

    const val NEXT = "orion.agent.action.NEXT"

    const val TAKE_PHOTO = "orion.agent.action.TAKE_PHOTO"

    const val EXIT = "orion.agent.action.EXIT"

    const val OPEN_WEB_URL = "orion.agent.action.OPEN_WEB_URL"
    
    const val OUTDOOR_NAVIGATE_START = "orion.agent.action.OUTDOOR_NAVIGATE_START"

    const val CONFIGURE_WELCOME_MESSAGE = "orion.agent.action.CONFIGURE_WELCOME_MESSAGE"

    const val REGISTER = "orion.agent.action.REGISTER"

    const val ADJUST_SPEED = "orion.agent.action.ADJUST_SPEED"

    const val WHO_AM_I = "orion.agent.action.FACE_RECOGNITION"

    const val GO_CHARGING = "orion.agent.action.GO_CHARGING"

    const val GUIDE_ROUTE_SELECTION = "orion.agent.action.GUIDE_ROUTE_SELECTION"

    const val SET_LOCATION = "orion.agent.action.SET_LOCATION"

    const val GUIDE_ROUTE_SELECTION_FROM_MAP = "orion.agent.action.GUIDE_ROUTE_SELECTION_FROM_MAP"

    const val INTERVIEW_START_PHOTO = "orion.agent.action.INTERVIEW_START_PHOTO"

    const val CHANGE_FACE = "orion.agent.action.CHANGE_FACE"

    const val CLICK = "orion.agent.action.CLICK"

    const val COMMON_PAUSE = "orion.agent.action.COMMON_PAUSE"

    const val MULTIMEDIA_PLAY = "orion.agent.action.MULTIMEDIA_PLAY"

    const val COMMON_REPLAY = "orion.agent.action.COMMON_REPLAY"

    const val START_QUESTION = "orion.agent.action.START_QUESTION"

    const val COMMON_REPEAT = "orion.agent.action.COMMON_REPEAT"

    const val ROUTES_OTHERS = "orion.agent.action.ROUTES_OTHERS"

    const val SCORE = "orion.agent.action.SCORE"

    const val ANSWER_QUESTION_FROM_VISION = "orion.agent.action.ANSWER_QUESTION_FROM_VISION"

    const val EXIT_CRUISE = "orion.agent.action.EXIT_CRUISE"

    const val START_IMMEDIATELY = "orion.agent.action.START_IMMEDIATELY"

    const val SILENT = "orion.agent.action.SILENT"


}