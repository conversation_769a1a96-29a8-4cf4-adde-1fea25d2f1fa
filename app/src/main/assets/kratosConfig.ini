[FetchTokenUrl]
release=https://robot-passport.ainirobot.com:8866/account/bind
test=https://passporttest.ainirobot.com:8866/account/bind
prerelease=https://robot-passport.ainirobot.com:8866/account/bind
[RefreshTokenUrl]
release=https://robot-passport.ainirobot.com:8888/oauth2/token
test=http://passporttest.ainirobot.com:8888/oauth2/token
prerelease=https://robot-passport.ainirobot.com:8888/oauth2/token
[H2Url]
release=https://speech-bxm.ainirobot.com:443/h2/streaming-asr
test=https://speech-test.ainirobot.com:443/h2/streaming-asr
prerelease=https://speech-bxm-pre.ainirobot.com:443/h2/streaming-asr
[TextQueryUrl]
release=https://speech-bxm.ainirobot.com/text-asr/v1
test=http://speech-test.ainirobot.com/text-asr/v1
prerelease=http://asr.ovspre.ainirobot.com:8003/text-asr/v1
[TtsUrl]
release=https://speech-bxm.ainirobot.com/tts/v1/text2audio
test=https://speech-test.ainirobot.com/tts/v1/text2audio
prerelease=https://speech-bxm-pre.ainirobot.com/tts/v1/text2audio
[SpokermanUrl]
release=https://speech-bxm.ainirobot.com/tts/v1/voices
test=https://speech-test.ainirobot.com/tts/v1/voices
prerelease=https://speech-bxm.ainirobot.com/tts/v1/voices
[WakeupAudioUrl]
release=https://speech-bxm.ainirobot.com/tts/v1/operate/audio
test=https://speech-test.ainirobot.com/tts/v1/operate/audio
prerelease=https://speech-bxm.ainirobot.com/tts/v1/operate/audio
[HeartUrl]
release=https://speech-bxm.ainirobot.com/echo
test=https://speech-test.ainirobot.com/echo
prerelease=https://speech-bxm-pre.ainirobot.com/echo