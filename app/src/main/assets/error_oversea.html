<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=0.5">
    <title>Oops, Lost Page!</title>
    <style>
        :root {
            --primary-color: #6C5CE7; /* 主色调 */
            --particle-color: rgba(108, 92, 231, 0.5); /* 粒子颜色 */
        }

        body {
            margin: 0;
            height: 100vh;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }

        /* 动态粒子背景 */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 0;
        }

        /* 呼吸光效容器 */
        .breath-container {
            position: relative;
            z-index: 1;
            text-align: center;
            padding-top: 20vh;
            animation: breath 3s ease-in-out infinite;
        }

        /* 动态数字动画 */
        .error-code {
            font-size: 8rem;
            color: var(--primary-color);
            text-shadow: 0 4px 20px rgba(108, 92, 231, 0.2);
            margin-bottom: 1rem;
        }

        /* 情感化文案 */
        .message {
            font-size: 1.5rem;
            color: #495057;
            margin: 1rem 0;
            line-height: 1.6;
        }

        /* 关键帧动画 */
        @keyframes breath {
            0%, 100% { transform: scale(1); opacity: 0.9; }
            50% { transform: scale(1.02); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- 动态粒子背景 -->
    <canvas class="particles"></canvas>
    
    <div class="breath-container">
        <div class="error-code">4<span class="floating-dot">⁘</span>4</div>
        <p class="message">Oh no, the page got lost!</p>
        <p class="message">Try asking again~</p>
    </div>

    <script>
        // 粒子动画系统（精简版）
        (function initParticles() {
            const canvas = document.querySelector('.particles');
            const ctx = canvas.getContext('2d');
            let particles = [];

            // 初始化画布
            function resize() {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            }
            window.addEventListener('resize', resize);
            resize();

            // 创建粒子
            class Particle {
                constructor() {
                    this.reset();
                }
                reset() {
                    this.x = Math.random() * canvas.width;
                    this.y = Math.random() * canvas.height;
                    this.vx = (Math.random() - 0.5) * 0.8;
                    this.vy = (Math.random() - 0.5) * 0.8;
                    this.radius = Math.random() * 2 + 1;
                    this.alpha = Math.random() * 0.5 + 0.3;
                }
                update() {
                    this.x += this.vx;
                    this.y += this.vy;
                    if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
                    if (this.y < 0 || this.y > canvas.height) this.vy *= -1;
                    this.alpha -= 0.003;
                    if (this.alpha <= 0) this.reset();
                }
            }

            // 渲染循环
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 动态生成粒子
                if (particles.length < 80) particles.push(new Particle());
                
                particles.forEach((p, i) => {
                    p.update();
                    ctx.beginPath();
                    ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
                    ctx.fillStyle = `rgba(108, 92, 231, ${p.alpha})`;
                    ctx.fill();
                    
                    // 移除失效粒子
                    if (p.alpha <= 0) particles.splice(i, 1);
                });

                requestAnimationFrame(animate);
            }
            animate();
        })();
    </script>
</body>
</html>