<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="150dp"
    android:layout_height="100dp"
    android:background="@drawable/bg_console_window">

    <TextView
        android:id="@+id/agent_env_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="20sp"
        android:textColor="@color/white_translucent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/robot_name_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/console_robot_name_text_size"
        android:textColor="@color/white_translucent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/agent_env_view"/>

    <TextView
        android:id="@+id/close_btn"
        android:layout_width="20dp"
        android:layout_height="wrap_content"
        android:background="@drawable/button_selector"
        android:enabled="true"
        android:gravity="center_horizontal"
        android:paddingHorizontal="@dimen/console_btn_padding_hor"
        android:paddingVertical="@dimen/console_btn_padding_ver"
        android:text="@string/hide_console"
        android:textSize="@dimen/console_text_size"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/env_switch_btn"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:background="@drawable/button_selector"
        android:enabled="true"
        android:gravity="center_horizontal"
        android:paddingHorizontal="@dimen/console_btn_padding_hor"
        android:paddingVertical="@dimen/console_btn_padding_ver"
        android:layout_marginTop="@dimen/console_button_margin_vertical"
        android:text="@string/switch_env"
        android:textSize="@dimen/console_text_size"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="@id/close_btn"
        app:layout_constraintEnd_toEndOf="@id/close_btn"
        app:layout_constraintTop_toBottomOf="@id/close_btn" />

    <TextView
        android:id="@+id/clear_btn"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:background="@drawable/button_selector"
        android:enabled="true"
        android:gravity="center_horizontal"
        android:paddingHorizontal="@dimen/console_btn_padding_hor"
        android:paddingVertical="@dimen/console_btn_padding_ver"
        android:layout_marginTop="@dimen/console_button_margin_vertical"
        android:text="@string/clear_console"
        android:textSize="@dimen/console_text_size"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="@id/close_btn"
        app:layout_constraintEnd_toEndOf="@id/close_btn"
        app:layout_constraintTop_toBottomOf="@id/env_switch_btn" />

    <TextView
        android:id="@+id/microphone_btn"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:background="@drawable/button_selector"
        android:enabled="true"
        android:gravity="center_horizontal"
        android:paddingHorizontal="@dimen/console_btn_padding_hor"
        android:paddingVertical="@dimen/console_btn_padding_ver"
        android:layout_marginTop="@dimen/console_button_margin_vertical"
        android:text="@string/microphone_opened"
        android:textSize="@dimen/console_text_size"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="@id/close_btn"
        app:layout_constraintEnd_toEndOf="@id/close_btn"
        app:layout_constraintTop_toBottomOf="@id/clear_btn" />

    <TextView
        android:id="@+id/reconnect_btn"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:background="@drawable/button_selector"
        android:gravity="center_horizontal"
        android:paddingHorizontal="@dimen/console_btn_padding_hor"
        android:paddingVertical="@dimen/console_btn_padding_ver"
        android:layout_marginTop="@dimen/console_button_margin_vertical"
        android:text="@string/reconnect"
        android:textSize="@dimen/console_text_size"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="@id/microphone_btn"
        app:layout_constraintEnd_toEndOf="@id/microphone_btn"
        app:layout_constraintTop_toBottomOf="@id/microphone_btn" />

    <TextView
        android:id="@+id/vision_btn"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:background="@drawable/button_selector"
        android:gravity="center_horizontal"
        android:paddingHorizontal="@dimen/console_btn_padding_hor"
        android:paddingVertical="@dimen/console_btn_padding_ver"
        android:layout_marginTop="@dimen/console_button_margin_vertical"
        android:text="@string/vision"
        android:textSize="@dimen/console_text_size"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="@id/reconnect_btn"
        app:layout_constraintEnd_toEndOf="@id/reconnect_btn"
        app:layout_constraintTop_toBottomOf="@id/reconnect_btn" />

    <TextView
        android:id="@+id/app_version_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/console_app_version_text_size"
        android:textColor="@color/white_translucent"
        android:gravity="start"
        android:layout_marginEnd="3dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <TextView
        android:id="@+id/timestamp_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="1dp"
        android:textSize="@dimen/console_timestamp_text_size"
        android:textColor="@color/console_timestamp_color"
        app:layout_constraintStart_toEndOf="@+id/face_view"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/state_list_view"
        android:layout_width="0px"
        android:layout_height="0px"
        android:layout_marginTop="1dp"
        app:layout_constraintStart_toStartOf="@id/vision_btn"
        app:layout_constraintEnd_toEndOf="@id/vision_btn"
        app:layout_constraintTop_toBottomOf="@id/vision_btn"
        app:layout_constraintBottom_toTopOf="@id/app_version_view"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/console_list_view"
        android:layout_width="0px"
        android:layout_height="0px"
        android:layout_marginVertical="1dp"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="3dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/close_btn"
        app:layout_constraintTop_toBottomOf="@id/timestamp_view"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageView android:id="@+id/face_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_smile"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />
</androidx.constraintlayout.widget.ConstraintLayout>