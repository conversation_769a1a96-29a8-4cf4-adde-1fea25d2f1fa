<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="3dp">

    <ImageView android:id="@+id/avatar_view"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:scaleType="fitXY"
        android:layout_marginStart="1dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:ignore="ContentDescription" />

    <TextView android:id="@+id/title_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dialog_item_title_text_size"
        android:textColor="@color/title"
        android:layout_marginStart="5dp"
        app:layout_constraintStart_toEndOf="@id/avatar_view"
        app:layout_constraintTop_toTopOf="@id/avatar_view"
        tools:ignore="SmallSp" />

    <TextView android:id="@+id/subtitle_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dialog_item_subtitle_text_size"
        android:textColor="@color/subtitle"
        android:layout_marginTop="2dp"
        app:layout_constraintStart_toStartOf="@id/title_view"
        app:layout_constraintTop_toBottomOf="@id/title_view"
        tools:ignore="SmallSp" />
</androidx.constraintlayout.widget.ConstraintLayout>