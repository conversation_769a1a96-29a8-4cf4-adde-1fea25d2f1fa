<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dialog_width"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dialog_padding_hor"
    android:paddingVertical="@dimen/dialog_padding_ver"
    android:background="@drawable/bg_white_dialog">

    <TextView
        android:id="@+id/title_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="3dp"
        android:paddingBottom="5dp"
        android:gravity="start|center_vertical"
        android:textSize="@dimen/dialog_title_text_size"
        android:textColor="@color/title"
        android:textStyle="bold"
        tools:ignore="SmallSp" />

    <com.ainirobot.common.widget.CustomRecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="75dp"
        app:maxHeight="90dp"/>

    <TextView
        android:id="@+id/cancel_btn"
        android:layout_width="@dimen/dialog_button_width"
        android:layout_height="@dimen/dialog_button_height"
        android:layout_marginTop="6dp"
        android:gravity="center"
        android:textSize="@dimen/dialog_button_text_size"
        android:textColor="@color/subtitle"
        android:background="@drawable/button_selector_2"
        android:layout_gravity="center_horizontal"
        android:text="@string/cancel"
        tools:ignore="SmallSp" />
</LinearLayout>