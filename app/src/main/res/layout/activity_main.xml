<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context="com.ainirobot.app.MainActivity">

    <Button android:id="@+id/back_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:textSize="@dimen/home_btn_text_size_small"
        android:text="@string/back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <Button android:id="@+id/test_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:textSize="@dimen/home_btn_text_size_small"
        android:text="TEST"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="HardcodedText" />

    <Button android:id="@+id/restart_btn"
        android:layout_width="@dimen/home_btn_width"
        android:layout_height="@dimen/home_btn_height"
        android:layout_marginTop="30dp"
        android:textSize="@dimen/home_btn_text_size"
        android:text="@string/restart_service"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <Button android:id="@+id/record_audio_btn"
        android:layout_width="@dimen/home_btn_width"
        android:layout_height="@dimen/home_btn_height"
        android:layout_marginTop="16dp"
        android:textSize="@dimen/home_btn_text_size"
        android:text="@string/start_record_audio"
        app:layout_constraintStart_toStartOf="@id/restart_btn"
        app:layout_constraintTop_toBottomOf="@id/restart_btn"/>

    <Button android:id="@+id/show_console_bt"
        android:layout_width="@dimen/home_btn_width"
        android:layout_height="@dimen/home_btn_height"
        android:layout_marginTop="16dp"
        android:textSize="@dimen/home_btn_text_size"
        android:text="@string/show_console"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/record_audio_btn"
        app:layout_constraintTop_toBottomOf="@id/record_audio_btn"/>

    <RadioGroup android:id="@+id/thought_mode_radio_group"
        android:layout_width="@dimen/home_btn_width"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/show_console_bt"
        app:layout_constraintTop_toBottomOf="@id/show_console_bt">

        <RadioButton android:id="@+id/thought_mode_simple"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/ic_radio_btn_selector"
            android:textSize="@dimen/home_btn_text_size"
            android:text="@string/thought_mode_simple"/>

        <RadioButton android:id="@+id/thought_mode_complex"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:button="@drawable/ic_radio_btn_selector"
            android:layout_marginStart="60dp"
            android:textSize="@dimen/home_btn_text_size"
            android:text="@string/thought_mode_complex"/>
    </RadioGroup>

    <TextView
        android:id="@+id/app_version_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/console_app_version_text_size"
        android:textColor="@color/black_translucent"
        android:gravity="start"
        android:padding="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>