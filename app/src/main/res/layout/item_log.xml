<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingHorizontal="4dp"
    android:background="@color/white">

    <TextView
        android:id="@+id/tvTimestamp"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:paddingVertical="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:textSize="@dimen/test_item_text_size"
        android:textColor="#666666"
        android:fontFamily="monospace" />

    <TextView
        android:id="@+id/tvLogContent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@+id/tvTimestamp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvTimestamp"
        app:layout_constraintBottom_toBottomOf="@id/tvTimestamp"
        android:layout_weight="1"
        android:textSize="@dimen/test_item_text_size"
        android:textColor="#333333"
        android:layout_marginStart="2dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#E0E0E0"
        app:layout_constraintBottom_toBottomOf="@id/tvTimestamp"/>
</androidx.constraintlayout.widget.ConstraintLayout>