<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/dialog_width"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dialog_padding_hor"
    android:paddingVertical="@dimen/dialog_padding_ver"
    android:background="@drawable/bg_white_dialog">

    <TextView android:id="@+id/title_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="3dp"
        android:paddingBottom="5dp"
        android:gravity="start|center_vertical"
        android:textSize="@dimen/dialog_title_text_size"
        android:textColor="@color/title"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:ignore="SmallSp" />

    <FrameLayout android:id="@+id/content_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/title_view"/>

    <TextView android:id="@+id/message_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="start|top"
        android:textSize="@dimen/dialog_message_text_size"
        android:textColor="@color/title"
        android:minHeight="25dp"
        android:maxHeight="65dp"
        app:layout_constraintTop_toBottomOf="@id/content_view"
        tools:ignore="SmallSp" />

    <TextView android:id="@+id/negative_btn"
        android:layout_width="@dimen/dialog_button_width"
        android:layout_height="@dimen/dialog_button_height"
        android:text="@string/cancel"
        android:textColor="@color/subtitle"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:textSize="@dimen/dialog_button_text_size"
        android:background="@drawable/button_selector_2"
        android:layout_marginStart="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/message_view"/>

    <TextView android:id="@+id/positive_btn"
        android:layout_width="@dimen/dialog_button_width"
        android:layout_height="@dimen/dialog_button_height"
        android:textColor="@color/white"
        android:textSize="@dimen/dialog_button_text_size"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:text="@string/ok"
        android:background="@drawable/button_selector"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toBottomOf="@id/message_view"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>