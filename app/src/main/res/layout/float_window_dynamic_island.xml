<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/island_view"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:background="@drawable/bg_black_translucent"
        android:paddingHorizontal="10dp"
        android:layout_marginTop="12dp">

        <View android:id="@+id/pointer"
            android:layout_width="3dp"
            android:layout_height="3dp"
            android:background="@drawable/bg_white_circle"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>

        <com.ainirobot.common.widget.TypewriterView
            android:id="@+id/title_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="start|center_vertical"
            android:maxWidth="200dp"
            android:layout_marginStart="6dp"
            android:textColor="@color/white"
            android:textSize="@dimen/dynamic_island_text_size"
            app:layout_constraintStart_toEndOf="@id/pointer"
            app:layout_constraintEnd_toStartOf="@+id/loading_view"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <com.ainirobot.common.widget.LoadingView
            android:id="@+id/loading_view"
            android:layout_width="15dp"
            android:layout_height="1.5dp"
            android:layout_marginBottom="2dp"
            app:layout_constraintStart_toEndOf="@id/title_view"
            app:layout_constraintBottom_toBottomOf="@id/title_view"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>