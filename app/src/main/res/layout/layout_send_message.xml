<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView android:id="@+id/text_view"
        android:layout_width="match_parent"
        android:layout_height="32dp"
        android:gravity="center"
        android:background="#EFEFF8"
        android:textColor="@color/title"
        android:textSize="@dimen/dialog_message_text_size"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView android:id="@+id/text_view_2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/subtitle"
        android:textSize="5sp"
        android:layout_marginTop="3dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/text_view"
        tools:ignore="SmallSp" />
</androidx.constraintlayout.widget.ConstraintLayout>