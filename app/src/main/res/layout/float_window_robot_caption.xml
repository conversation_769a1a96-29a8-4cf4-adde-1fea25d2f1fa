<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_user_caption"
    android:layout_width="match_parent"
    android:layout_height="@dimen/caption_bar_height">

    <FrameLayout android:id="@+id/ai_caption_container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/ai_caption_bar_height"
        android:background="@drawable/bg_ai_caption"
        android:layout_marginHorizontal="@dimen/caption_bar_padding_hor"
        android:layout_marginBottom="@dimen/ai_caption_bar_margin_bottom"
        android:paddingHorizontal="@dimen/ai_caption_bar_padding_hor"
        android:paddingVertical="@dimen/ai_caption_bar_padding_ver"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <ScrollView android:id="@+id/ai_caption_scroll_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none"
            tools:ignore="UselessParent">
            <TextView
                android:id="@+id/ai_caption_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/ai_caption_text_size"
                android:textColor="@color/black"
                android:gravity="bottom|start"
                android:lineSpacingMultiplier="1.16"/>
        </ScrollView>
    </FrameLayout>

    <TextView
        android:id="@+id/generated_by_ai"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/content_generated_by_AI"
        android:layout_marginEnd="20dp"
        android:textSize="8sp"
        android:textColor="#38FFFFFF"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/ai_caption_container" />

    <com.ainirobot.common.widget.AnimImageView
        android:id="@+id/ai_ball_view"
        android:layout_width="@dimen/caption_ball_width"
        android:layout_height="@dimen/caption_ball_width"
        android:contentDescription="ai_ball"
        android:scaleType="fitXY"
        android:layout_marginStart="@dimen/caption_ball_margin_start"
        android:layout_marginBottom="@dimen/caption_ball_margin_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:ignore="HardcodedText" />

    <com.ainirobot.common.widget.AnimImageView
        android:id="@+id/ai_sweep_view"
        android:layout_width="wrap_content"
        android:layout_height="14dp"
        android:contentDescription="ai_sweep"
        android:layout_marginStart="12dp"
        android:scaleType="fitXY"
        app:layout_constraintStart_toEndOf="@id/ai_ball_view"
        app:layout_constraintTop_toTopOf="@id/ai_ball_view"
        app:layout_constraintBottom_toBottomOf="@id/ai_ball_view"
        tools:ignore="HardcodedText,RtlSymmetry" />

    <TextView
        android:id="@+id/ai_tips_view"
        android:layout_width="wrap_content"
        android:layout_height="11dp"
        android:textSize="@dimen/ai_tips_text_size"
        android:visibility="gone"
        android:textColor="@color/white"
        android:text="@string/user_speaking_tips"
        android:gravity="center"
        android:background="@drawable/bg_white_translucent"
        android:elevation="1px"
        android:paddingHorizontal="3dp"
        android:layout_marginStart="-6dp"
        app:layout_constraintStart_toEndOf="@id/ai_ball_view"
        app:layout_constraintTop_toTopOf="@id/ai_ball_view"/>

    <com.ainirobot.common.widget.StreamTextView
        android:id="@+id/user_caption_view"
        android:layout_width="0px"
        android:layout_height="36dp"
        android:textSize="@dimen/user_caption_text_size"
        android:textColor="@color/white"
        android:gravity="start|center_vertical"
        android:layout_marginHorizontal="@dimen/caption_bar_padding_hor"
        app:layout_constraintStart_toEndOf="@id/ai_ball_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/ai_ball_view"
        app:layout_constraintBottom_toBottomOf="@id/ai_ball_view"/>

    <HorizontalScrollView
        android:id="@+id/ai_progress_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="-6dp"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="37dp"
        android:scrollbars="none"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/ai_ball_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/ai_ball_view">

        <com.ainirobot.common.widget.GradientMarqueeTextView
            android:id="@+id/ai_progress_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/thinking_view_text_size"
            android:textColor="@color/white"
            android:paddingHorizontal="@dimen/caption_ai_progress_view_padding_hor"
            android:paddingVertical="@dimen/caption_ai_progress_view_padding_ver"
            app:layout_constraintStart_toEndOf="@id/ai_ball_view"
            app:layout_constraintBottom_toBottomOf="@id/ai_ball_view"/>
            
    </HorizontalScrollView>

    <com.ainirobot.common.widget.WebpTextView
        android:id="@+id/listening_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="10sp"
        android:textColor="@color/white"
        app:webpAsset="icon_listening.webp"
        app:webpSize="20dp"
        app:webpSpacing="1dp"
        android:paddingHorizontal="5dp"
        android:paddingVertical="1.5dp"
        android:layout_marginStart="6dp"
        android:layout_marginBottom="14dp"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@id/ai_ball_view"
        app:layout_constraintBottom_toBottomOf="@id/ai_ball_view"/>
</androidx.constraintlayout.widget.ConstraintLayout>