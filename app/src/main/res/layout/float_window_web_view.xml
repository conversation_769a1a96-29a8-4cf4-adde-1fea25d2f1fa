<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:id="@+id/main"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <WebView android:id="@+id/web_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <com.ainirobot.common.widget.KProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="0px"
        android:layout_height="2dp"
        app:layout_constraintTop_toTopOf="@id/web_view"
        app:layout_constraintStart_toStartOf="@id/web_view"
        app:layout_constraintEnd_toEndOf="@id/web_view"/>

    <ImageView android:id="@+id/close_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:layout_margin="3dp"
        android:src="@drawable/ic_close"
        android:background="@drawable/circular_translucent_selector"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:ignore="ContentDescription" />

    <ImageView android:id="@+id/refresh_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:enabled="false"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_refresh"
        android:background="@drawable/circular_translucent_selector"
        app:layout_constraintTop_toTopOf="@id/close_btn"
        app:layout_constraintEnd_toEndOf="parent"
        tools:ignore="ContentDescription" />

    <ImageView android:id="@+id/forward_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:enabled="false"
        android:layout_marginEnd="8dp"
        android:src="@drawable/ic_forward"
        android:background="@drawable/circular_translucent_selector"
        app:layout_constraintTop_toTopOf="@id/close_btn"
        app:layout_constraintEnd_toStartOf="@id/refresh_btn"
        tools:ignore="ContentDescription" />

    <ImageView android:id="@+id/back_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="5dp"
        android:enabled="false"
        android:layout_marginEnd="8dp"
        android:src="@drawable/ic_back"
        android:background="@drawable/circular_translucent_selector"
        app:layout_constraintTop_toTopOf="@id/close_btn"
        app:layout_constraintEnd_toStartOf="@id/forward_btn"
        tools:ignore="ContentDescription" />
</androidx.constraintlayout.widget.ConstraintLayout>