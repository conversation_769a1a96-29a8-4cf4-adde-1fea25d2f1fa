<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingVertical="12dp"
    android:paddingHorizontal="8dp">

    <!-- 左侧控制面板 -->
    <LinearLayout
        android:id="@+id/controlPanel"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginEnd="8dp"
        android:padding="8dp"
        android:background="@drawable/panel_background"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/recyclerViewLog"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintWidth_percent="0.2">
        <!-- 单选组 -->
        <RadioGroup
            android:id="@+id/radioGroup"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RadioButton
                android:id="@+id/radioMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:button="@drawable/ic_radio_btn_selector"
                android:text="MESSAGE"
                android:textSize="@dimen/home_btn_text_size"
                android:paddingHorizontal="8dp"
                android:checked="true"
                tools:ignore="HardcodedText" />

            <RadioButton
                android:id="@+id/radioTTS"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:button="@drawable/ic_radio_btn_selector"
                android:text="TTS"
                android:textSize="@dimen/home_btn_text_size"
                android:paddingHorizontal="8dp"
                tools:ignore="HardcodedText" />

            <RadioButton
                android:id="@+id/radioASR"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:button="@drawable/ic_radio_btn_selector"
                android:text="ASR"
                android:textSize="@dimen/home_btn_text_size"
                android:paddingHorizontal="8dp"
                tools:ignore="HardcodedText" />

        </RadioGroup>

        <!-- 开始/停止按钮 -->
        <TextView
            android:id="@+id/btnStartStop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="START"
            android:textSize="@dimen/test_normal_text_size"
            android:textColor="@color/white"
            android:gravity="center"
            android:padding="8dp"
            android:layout_marginVertical="4dp"
            android:background="@drawable/button_start_background"
            tools:ignore="HardcodedText" />

        <!-- 清空日志按钮 -->
        <TextView
            android:id="@+id/btnClearLog"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/clear_console"
            android:textSize="@dimen/test_normal_text_size"
            android:textColor="@color/white"
            android:gravity="center"
            android:padding="8dp"
            android:background="@drawable/button_clear_background" />

        <!-- 清空日志按钮 -->
        <TextView
            android:id="@+id/back_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="@string/back"
            android:textSize="@dimen/test_normal_text_size"
            android:textColor="@color/white"
            android:gravity="center"
            android:padding="8dp"
            android:background="@drawable/button_normal_background" />
    </LinearLayout>

    <!-- 日志RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewLog"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:background="@drawable/panel_background"
        android:padding="8dp"
        app:layout_constraintStart_toEndOf="@+id/controlPanel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:listitem="@layout/item_log" />
</androidx.constraintlayout.widget.ConstraintLayout>