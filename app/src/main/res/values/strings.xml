<resources>
    <string name="app_name">Agent Service</string>
    <string name="tips">Tips</string>
    <string name="ok">OK</string>
    <string name="cancel">Cancel</string>
    <string name="back">Back</string>
    <string name="restart_service">Restart Service</string>
    <string name="reconnect">Reconnect</string>
    <string name="vision">Vision</string>
    <string name="show_console">Show Console</string>
    <string name="hide_console">Hide</string>
    <string name="clear_console">Clear</string>
    <string name="switch_env">Env</string>
    <string name="microphone_opened">Mic On</string>
    <string name="microphone_closed">Mic Off</string>
    <string name="record_opened">Recording On</string>
    <string name="record_closed">Recording Off</string>
    <string name="error_dialog_title">Error</string>
    <string name="connection_dialog_title">Connection Error</string>
    <string name="network_invalid_tips">Network error. Please check connection</string>
    <string name="env_dev">Agent Dev</string>
    <string name="env_test">Test</string>
    <string name="env_release">Stable</string>
    <string name="switch_env_message">Current: %1$s. System will restart after switching!</string>
    <string name="switch_env_success">Switched to %1$s. Restart to apply changes!</string>
    <string name="start_record_audio">Start Record</string>
    <string name="stop_record_audio">Stop Record</string>
    <string name="user_speaking_tips">Listening</string>
    <string name="user_speaking_tips_2">Listening</string>
    <string name="ai_thinking_tips">Thinking</string>
    <string name="ai_thinking_tips_2">Processing</string>
    <string name="plan_show_dialog_title">Confirm Plan</string>
    <string name="msg_person_not_found">%1$s not found. Verify name spelling</string>
    <string name="msg_person_name_too_short">Name too short: ‘%1$s’ (min 2 chars)</string>
    <string name="send_message_to">Send to %1$s</string>
    <string name="send_message_take_picture_tips">Robot will capture your photo for recipient identification</string>
    <string name="select_person_sent">Send to:</string>
    <string name="message_sending">Sending…</string>
    <string name="temp_recognition_result">~Interim result:</string>
    <string name="recognition_result">~Result:</string>
    <string name="island_text_prefix">%1$s</string>
    <string name="volume_changed_tips">Volume: %1$d</string>
    <string name="volume_max_tips">Max volume</string>
    <string name="volume_min_tips">Min volume</string>
    <string name="debug_mode_opened">Debug Mode: On</string>
    <string name="debug_mode_closed">Debug Mode: Off</string>
    <string name="thought_mode_simple">Lite Mode</string>
    <string name="thought_mode_complex">Full Mode</string>
    <string name="caption_measure_text">Google</string>
    <string name="dialog_connect_fail_tips">Connection failed. Retrying…</string>
    <string name="dialog_disconnected_tips">Disconnected. Reconnecting…</string>
    <string name="content_generated_by_AI">Content generated by AI</string>
    <string name="tip_wakeup_word_pinyin_unmatch">Chinese character and pinyin of Wake-up word do not match. It may be caused by Delimiters</string>
</resources>