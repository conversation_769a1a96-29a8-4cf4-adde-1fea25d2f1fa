#!/bin/bash

# 解析编译类型和操作模式
BUILD_TYPE=${1:-"debug"} # 默认为 debug 模式
REGION_VERSION=${2:-"domestic"} # 默认为 domestic 国内
PUSH_ONLY=${3:-"build"} # 默认为 build 模式

case $BUILD_TYPE in
  "release")
    echo "编译正式版..."
    ;;
  "debug")
    echo "编译体验版..."
    ;;
  *)
    echo "无效的编译类型：$BUILD_TYPE。可用选项为：release、debug。"
    exit 1
    ;;
esac

case $REGION_VERSION in
  "domestic")
    echo "区域：国内"
    ;;
  "oversea")
    echo "区域：海外"
    ;;
  *)
    echo "无效的区域版本：$REGION_VERSION。可用选项为：domestic、oversea。"
    exit 1
    ;;
esac

echo "Build Start $(date '+%Y-%m-%d %H:%M:%S')"

# 只在非 push 模式下执行编译
if [ "$PUSH_ONLY" != "push" ]; then
  ./gradlew clean
  # 编译项目
  ./gradlew assembleDebug -PbuildType="$BUILD_TYPE" -PregionVersion="$REGION_VERSION"

  # 检查编译是否成功
  if [ $? -ne 0 ]; then
    echo "Build Failed $(date '+%Y-%m-%d %H:%M:%S')"
    exit 1
  else
    echo "Build End $(date '+%Y-%m-%d %H:%M:%S')"
  fi
else
  echo "跳过编译步骤，直接推送已有 APK..."
fi

# 查找编译好的 APK 文件（可以根据项目结构调整路径）
APK_PATH="./app/build/outputs/apk/debug/app-debug.apk"
OUTPUT_PATH="/system/priv-app/SpeechService/SpeechService.apk"

if [ -f "$APK_PATH" ]; then
  adb remount
  adb shell rm -rf /system/priv-app/SpeechService/oat/
  adb push "$APK_PATH" "$OUTPUT_PATH"
  echo "编译成功！APK 已保存到 $OUTPUT_PATH"
  adb shell am force-stop com.ainirobot.speechasrservice
else
  echo "未找到 APK 文件。检查路径或编译配置是否正确。"
  exit 1
fi
